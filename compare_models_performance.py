"""
对比不同模型的性能
"""

import time
import json
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from chatbi_core import extract_keywords_with_output, call_qwen_model

def test_model_performance(model_name, test_questions):
    """测试指定模型的性能"""
    print(f"\n{'='*60}")
    print(f"测试模型: {model_name}")
    print(f"{'='*60}")
    
    results = []
    total_time = 0
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n问题 {i}: {question}")
        
        start_time = time.time()
        
        try:
            # 直接调用模型进行关键词提取测试
            prompt = f"""请从以下用户问题中提取关键词，并以JSON格式返回：

用户问题: {question}

请提取以下字段的关键词：
- 数据日期
- 产品简称
- 渠道名称
- 指标名

返回格式：
{{"数据日期": ["YYYYMMDD"], "产品简称": ["产品名"], "渠道名称": ["渠道名"], "指标名": ["指标名"]}}
"""
            
            response = call_qwen_model(prompt, model=model_name, temperature=0.3)
            processing_time = time.time() - start_time
            
            if isinstance(response, dict) and "error" in response:
                print(f"❌ 失败: {response['error']}")
                results.append({
                    'question': question,
                    'success': False,
                    'error': response['error'],
                    'processing_time': processing_time
                })
            else:
                # 尝试解析响应
                if hasattr(response, 'output') and response.output and hasattr(response.output, 'choices'):
                    content = response.output.choices[0].message.content
                    
                    # 提取JSON
                    json_start = content.find('{')
                    json_end = content.rfind('}') + 1
                    if json_start >= 0 and json_end > json_start:
                        json_str = content[json_start:json_end]
                        try:
                            extracted_data = json.loads(json_str)
                            print(f"✅ 成功 ({processing_time:.2f}秒)")
                            print(f"提取结果: {json.dumps(extracted_data, ensure_ascii=False)}")
                            
                            results.append({
                                'question': question,
                                'success': True,
                                'extracted_data': extracted_data,
                                'processing_time': processing_time,
                                'response_content': content
                            })
                        except json.JSONDecodeError:
                            print(f"❌ JSON解析失败 ({processing_time:.2f}秒)")
                            print(f"响应内容: {content[:200]}...")
                            results.append({
                                'question': question,
                                'success': False,
                                'error': 'JSON解析失败',
                                'processing_time': processing_time,
                                'response_content': content
                            })
                    else:
                        print(f"❌ 未找到JSON格式 ({processing_time:.2f}秒)")
                        print(f"响应内容: {content[:200]}...")
                        results.append({
                            'question': question,
                            'success': False,
                            'error': '未找到JSON格式',
                            'processing_time': processing_time,
                            'response_content': content
                        })
                else:
                    print(f"❌ 响应格式错误 ({processing_time:.2f}秒)")
                    results.append({
                        'question': question,
                        'success': False,
                        'error': '响应格式错误',
                        'processing_time': processing_time
                    })
            
            total_time += processing_time
            
        except Exception as e:
            processing_time = time.time() - start_time
            print(f"❌ 异常: {str(e)} ({processing_time:.2f}秒)")
            results.append({
                'question': question,
                'success': False,
                'error': str(e),
                'processing_time': processing_time
            })
            total_time += processing_time
    
    # 统计结果
    successful = sum(1 for r in results if r['success'])
    failed = len(results) - successful
    avg_time = total_time / len(results) if results else 0
    
    print(f"\n{'='*60}")
    print(f"模型 {model_name} 测试结果:")
    print(f"总问题数: {len(results)}")
    print(f"成功: {successful}")
    print(f"失败: {failed}")
    print(f"成功率: {successful/len(results)*100:.1f}%")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均耗时: {avg_time:.2f}秒/问题")
    print(f"{'='*60}")
    
    return {
        'model': model_name,
        'total_questions': len(results),
        'successful': successful,
        'failed': failed,
        'success_rate': successful/len(results)*100,
        'total_time': total_time,
        'avg_time': avg_time,
        'results': results
    }

def main():
    """主函数"""
    # 测试问题集
    test_questions = [
        "昨天天天成长3号在小招的赎回金额？",
        "今天固定收益类在代销的净申赎金额是多少?",
        "2025年3月20日新启航三个月定开1号产品净申赎金额是多少？",
        "近三天固定收益类的持仓规模是多少?",
        "天天成长A各渠道的净申赎占比分别是多少？"
    ]
    
    # 要测试的模型
    models_to_test = [
        "qwen3-30b-a3b",      # 新的30B模型
        "qwen2.5-72b-instruct", # 之前使用的72B模型
        "qwen-turbo",         # 快速模型
        "qwen-plus"           # 平衡模型
    ]
    
    print("🚀 开始模型性能对比测试")
    print(f"测试问题数: {len(test_questions)}")
    print(f"测试模型数: {len(models_to_test)}")
    
    all_results = []
    
    for model in models_to_test:
        try:
            result = test_model_performance(model, test_questions)
            all_results.append(result)
        except Exception as e:
            print(f"❌ 模型 {model} 测试失败: {str(e)}")
            all_results.append({
                'model': model,
                'total_questions': len(test_questions),
                'successful': 0,
                'failed': len(test_questions),
                'success_rate': 0,
                'total_time': 0,
                'avg_time': 0,
                'error': str(e)
            })
    
    # 生成对比报告
    print(f"\n{'='*80}")
    print("📊 模型性能对比报告")
    print(f"{'='*80}")
    
    print(f"{'模型名称':<20} {'成功率':<10} {'平均耗时':<12} {'总耗时':<10} {'状态'}")
    print("-" * 80)
    
    for result in all_results:
        model_name = result['model']
        success_rate = f"{result['success_rate']:.1f}%"
        avg_time = f"{result['avg_time']:.2f}s"
        total_time = f"{result['total_time']:.2f}s"
        status = "✅ 正常" if result['success_rate'] > 0 else "❌ 失败"
        
        print(f"{model_name:<20} {success_rate:<10} {avg_time:<12} {total_time:<10} {status}")
    
    # 找出最佳模型
    valid_results = [r for r in all_results if r['success_rate'] > 0]
    if valid_results:
        # 按成功率和速度综合评分
        for result in valid_results:
            # 综合评分：成功率权重70%，速度权重30%（速度越快分数越高）
            max_time = max(r['avg_time'] for r in valid_results)
            speed_score = (max_time - result['avg_time']) / max_time * 100 if max_time > 0 else 100
            result['composite_score'] = result['success_rate'] * 0.7 + speed_score * 0.3
        
        best_model = max(valid_results, key=lambda x: x['composite_score'])
        fastest_model = min(valid_results, key=lambda x: x['avg_time'])
        most_accurate = max(valid_results, key=lambda x: x['success_rate'])
        
        print(f"\n🏆 推荐模型:")
        print(f"综合最佳: {best_model['model']} (评分: {best_model['composite_score']:.1f})")
        print(f"速度最快: {fastest_model['model']} (平均: {fastest_model['avg_time']:.2f}s)")
        print(f"准确率最高: {most_accurate['model']} (成功率: {most_accurate['success_rate']:.1f}%)")
    
    # 保存详细结果
    with open("model_performance_comparison.json", "w", encoding="utf-8") as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细结果已保存到: model_performance_comparison.json")

if __name__ == "__main__":
    main()
