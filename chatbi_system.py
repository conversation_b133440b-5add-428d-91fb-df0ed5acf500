"""
平安理财ChatBI问答系统
使用千问(<PERSON>wen)模型进行问题识别和模板匹配
"""

import json
import os
import time
import dashscope
from dashscope import Generation
import numpy as np
from datetime import datetime
import requests
import logging
import sys
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chatbi.log", encoding="utf-8"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("ChatBI")

# 设置API密钥
API_KEY = 'sk-4af3f43ae9a74f0ebed6736b453a47c6'
dashscope.api_key = API_KEY

# 设置请求超时和重试次数
REQUEST_TIMEOUT = 30  # 秒
MAX_RETRIES = 3

# 加载问题清单数据
def load_question_data(json_file="AI验证问题清单.json"):
    """
    加载问题清单数据

    参数:
        json_file (str): JSON文件路径

    返回:
        dict: 包含问题和模板的字典
    """
    try:
        with open(json_file, "r", encoding="utf-8") as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"加载问题清单数据失败: {str(e)}")
        return None

# 调用千问模型
def call_qwen_model(prompt, model="qwen-max", temperature=0.7, max_tokens=1500):
    """
    调用千问模型并获取回复，包含重试机制

    参数:
        prompt (str): 提示文本
        model (str): 模型名称
        temperature (float): 控制输出随机性
        max_tokens (int): 最大生成token数

    返回:
        dict: 模型响应
    """
    for attempt in range(MAX_RETRIES):
        try:
            logger.info(f"调用千问模型 (尝试 {attempt+1}/{MAX_RETRIES})")

            # 使用本地模拟响应（如果API不可用）
            if os.environ.get("USE_LOCAL_SIMULATION") == "1":
                logger.info("使用本地模拟响应")
                return simulate_model_response(prompt)

            # 实际调用API
            response = Generation.call(
                model=model,
                prompt=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                result_format='message',
                timeout=REQUEST_TIMEOUT
            )
            logger.info("模型调用成功")
            return response
        except Exception as e:
            error_msg = str(e)
            logger.error(f"调用模型失败 (尝试 {attempt+1}/{MAX_RETRIES}): {error_msg}")

            if attempt < MAX_RETRIES - 1:
                # 指数退避重试
                wait_time = 2 ** attempt
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                # 所有重试都失败
                logger.error(f"达到最大重试次数，放弃请求")
                return {"error": error_msg}

# 模拟模型响应（当API不可用时使用）
def simulate_model_response(prompt):
    """
    模拟模型响应，用于API不可用时的本地测试

    参数:
        prompt (str): 提示文本

    返回:
        object: 模拟的响应对象
    """
    logger.debug(f"模拟响应，提示: {prompt[:100]}...")

    # 创建一个模拟的响应对象
    class SimulatedResponse:
        def __init__(self, content):
            self.status_code = 200
            self.request_id = "simulated-request-id"
            self.output = SimulatedOutput(content)

    class SimulatedOutput:
        def __init__(self, content):
            self.choices = [SimulatedChoice(content)]

    class SimulatedChoice:
        def __init__(self, content):
            self.message = SimulatedMessage(content)

    class SimulatedMessage:
        def __init__(self, content):
            self.role = "assistant"
            self.content = content

    # 根据提示内容生成简单的模拟回复
    if "找出最相似的一个问题" in prompt:
        content = '{"序号": 1, "相似度": 85}'
    elif "提取关键信息" in prompt:
        content = '{"产品简称": ["天天成长A"], "指标名": ["收益率"]}'
    elif "平安理财的智能助手" in prompt:
        content = "您好，我是平安理财的智能助手。根据您的问题，我需要查询天天成长A产品的收益率数据。这些数据通常在我们的系统中可以获取，包括历史收益率和最新收益率。请问您需要查询的是哪个时间段的收益率呢？"
    else:
        content = "这是一个模拟的回复，因为API当前不可用。"

    logger.debug(f"模拟响应内容: {content}")
    return SimulatedResponse(content)

# 找到最相似的问题
def find_most_similar_question(user_question, questions_data, model="qwen-max"):
    """
    使用千问模型找到最相似的问题

    参数:
        user_question (str): 用户问题
        questions_data (list): 问题数据列表
        model (str): 模型名称

    返回:
        dict: 最相似的问题数据
    """
    # 本地模拟模式下，直接返回第一个问题作为相似问题
    if os.environ.get("USE_LOCAL_SIMULATION") == "1":
        logger.info("本地模拟模式：直接返回第一个问题作为相似问题")
        if questions_data and len(questions_data) > 0:
            return questions_data[0]

    # 构建提示
    prompt = f"""
请分析以下用户问题，并从给定的问题列表中找出最相似的一个问题。
返回最相似问题的序号和相似度评分（0-100分）。

用户问题: {user_question}

问题列表:
"""

    # 添加问题列表
    for i, q in enumerate(questions_data[:50]):  # 限制为前50个问题以避免提示过长
        prompt += f"{i+1}. {q['用户问题']}\n"

    prompt += "\n请以JSON格式返回结果，包含最相似问题的序号和相似度评分，格式如下：\n"
    prompt += '{"序号": 数字, "相似度": 数字}'

    # 调用模型
    response = call_qwen_model(prompt, model=model, temperature=0.3)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"查找相似问题失败: {response['error']}")
        return None

    try:
        # 解析模型返回的JSON
        content = response.output.choices[0].message.content
        # 提取JSON部分
        json_start = content.find('{')
        json_end = content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = content[json_start:json_end]
            result = json.loads(json_str)

            # 获取最相似问题的序号和相似度
            index = result.get("序号", 1) - 1  # 转为0-based索引
            similarity = result.get("相似度", 0)

            if index >= 0 and index < len(questions_data) and similarity > 50:  # 相似度阈值
                return questions_data[index]
            else:
                # 如果相似度不够，返回None
                logger.info(f"未找到足够相似的问题 (序号: {index+1}, 相似度: {similarity})")
                return None
        else:
            logger.warning("无法从模型响应中提取JSON")
            return None
    except Exception as e:
        logger.error(f"解析模型响应失败: {str(e)}")
        logger.debug(f"模型响应: {response.output.choices[0].message.content if hasattr(response, 'output') else 'No output'}")
        return None

# 提取问题中的关键信息
def extract_key_values(user_question, similar_question, model="qwen-max"):
    """
    使用千问模型提取问题中的关键信息

    参数:
        user_question (str): 用户问题
        similar_question (dict): 最相似的问题数据
        model (str): 模型名称

    返回:
        dict: 提取的关键信息
    """
    # 本地模拟模式下，返回模拟的关键信息
    if os.environ.get("USE_LOCAL_SIMULATION") == "1":
        logger.info("本地模拟模式：返回模拟的关键信息")
        return {"产品简称": ["天天成长A"], "指标名": ["收益率"]}

    # 获取预期的key+value
    expected_key_value = similar_question.get("输出key+value", "")

    # 构建提示
    prompt = f"""
请分析以下用户问题，并提取关键信息。参考类似问题的关键信息格式进行提取。

用户问题: {user_question}

类似问题: {similar_question['用户问题']}
类似问题的关键信息: {expected_key_value}

请以相同的JSON格式提取用户问题中的关键信息。只返回JSON格式的结果，不要有其他文字。
"""

    # 调用模型
    response = call_qwen_model(prompt, model=model, temperature=0.3)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"提取关键信息失败: {response['error']}")
        return None

    try:
        # 解析模型返回的JSON
        content = response.output.choices[0].message.content
        # 提取JSON部分
        json_start = content.find('{')
        json_end = content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = content[json_start:json_end]
            result = json.loads(json_str)
            return result
        else:
            logger.warning("无法从模型响应中提取JSON")
            return None
    except Exception as e:
        logger.error(f"解析模型响应失败: {str(e)}")
        logger.debug(f"模型响应: {response.output.choices[0].message.content if hasattr(response, 'output') else 'No output'}")
        return None

# 匹配模板
def match_template(key_values, templates_data):
    """
    根据提取的关键信息匹配模板

    参数:
        key_values (dict): 提取的关键信息
        templates_data (list): 模板数据列表

    返回:
        str: 匹配的模板
    """
    logger.info(f"尝试匹配模板，关键信息: {json.dumps(key_values, ensure_ascii=False)}")

    # 这里简化处理，实际应用中可能需要更复杂的匹配逻辑
    # 随机选择一个模板作为示例
    if templates_data and len(templates_data) > 0:
        for template in templates_data:
            logger.info(f"选择模板: {template}")
            return template  # 返回第一个模板的值

    logger.warning("未找到匹配的模板")
    return "未找到匹配的模板"

# 生成回答
def generate_answer(user_question, matched_template, key_values, model="qwen-max"):
    """
    使用千问模型生成回答

    参数:
        user_question (str): 用户问题
        matched_template (str): 匹配的模板
        key_values (dict): 提取的关键信息
        model (str): 模型名称

    返回:
        str: 生成的回答
    """
    # 本地模拟模式下，返回模拟的回答
    if os.environ.get("USE_LOCAL_SIMULATION") == "1":
        logger.info("本地模拟模式：返回模拟的回答")
        return f"""您好，我是平安理财的智能助手。

根据您的问题"{user_question}"，我需要查询以下信息：

1. 产品名称：{key_values.get('产品简称', ['未指定'])[0]}
2. 查询指标：{key_values.get('指标名', ['未指定'])[0]}

我会根据模板"{matched_template}"来处理您的查询。请稍等片刻，我正在为您查询相关数据。

这是一个模拟回答，实际系统会连接到数据库获取准确信息。"""

    # 构建提示
    prompt = f"""
你是平安理财的智能助手，请根据用户问题和提取的关键信息生成回答。

用户问题: {user_question}
匹配的模板: {matched_template}
提取的关键信息: {json.dumps(key_values, ensure_ascii=False)}

请生成一个专业、友好的回答，解释你将如何处理这个查询，以及需要查询哪些数据。回答要用中文。
"""

    # 调用模型
    response = call_qwen_model(prompt, model=model, temperature=0.7)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"生成回答失败: {response['error']}")
        return "抱歉，我无法生成回答。请稍后再试。"

    try:
        answer = response.output.choices[0].message.content
        return answer
    except Exception as e:
        logger.error(f"获取模型回答失败: {str(e)}")
        return "抱歉，在处理您的问题时出现了错误。请稍后再试。"

# 主函数
def main():
    try:
        # 加载问题清单数据
        logger.info("正在加载问题清单数据...")
        data = load_question_data()
        if not data:
            logger.error("无法加载问题清单数据，程序退出")
            print("无法加载问题清单数据，程序退出")
            return

        questions_data = data.get("问题", [])
        templates_data = data.get("模板", [])

        logger.info(f"成功加载问题数据: {len(questions_data)}个问题, {len(templates_data)}个模板")

        # 检查是否使用本地模拟模式
        if os.environ.get("USE_LOCAL_SIMULATION") == "1":
            print("\n[注意] 系统运行在本地模拟模式，不会调用实际API\n")

        print("=" * 50)
        print("平安理财ChatBI问答系统")
        print("使用千问(Qwen)模型进行问题识别和模板匹配")
        print("输入'exit'或'quit'退出")
        print("输入'debug on'开启调试模式")
        print("输入'local on'启用本地模拟模式")
        print("=" * 50)

        while True:
            try:
                user_question = input("\n请输入您的问题: ")

                # 处理特殊命令
                if user_question.lower() in ['exit', 'quit']:
                    print("谢谢使用，再见!")
                    break
                elif user_question.lower() == 'debug on':
                    logger.setLevel(logging.DEBUG)
                    print("已开启调试模式")
                    continue
                elif user_question.lower() == 'debug off':
                    logger.setLevel(logging.INFO)
                    print("已关闭调试模式")
                    continue
                elif user_question.lower() == 'local on':
                    os.environ["USE_LOCAL_SIMULATION"] = "1"
                    print("已启用本地模拟模式")
                    continue
                elif user_question.lower() == 'local off':
                    os.environ["USE_LOCAL_SIMULATION"] = "0"
                    print("已关闭本地模拟模式")
                    continue
                elif not user_question.strip():
                    continue

                logger.info(f"收到用户问题: {user_question}")
                start_time = time.time()

                try:
                    # 1. 找到最相似的问题
                    print("正在分析您的问题...")
                    similar_question = find_most_similar_question(user_question, questions_data)

                    if similar_question:
                        logger.info(f"找到相似问题: {similar_question['用户问题']}")
                        print(f"找到相似问题: {similar_question['用户问题']}")

                        # 2. 提取关键信息
                        print("正在提取关键信息...")
                        key_values = extract_key_values(user_question, similar_question)

                        if key_values:
                            logger.info(f"提取的关键信息: {json.dumps(key_values, ensure_ascii=False)}")
                            print(f"提取的关键信息: {json.dumps(key_values, ensure_ascii=False)}")

                            # 3. 匹配模板
                            print("正在匹配模板...")
                            matched_template = match_template(key_values, templates_data)
                            logger.info(f"匹配的模板: {matched_template}")

                            # 4. 生成回答
                            print("正在生成回答...")
                            answer = generate_answer(user_question, matched_template, key_values)

                            end_time = time.time()
                            processing_time = end_time - start_time
                            logger.info(f"处理完成，耗时: {processing_time:.2f}秒")

                            print("\n" + "=" * 50)
                            print("回答:")
                            print(answer)
                            print("-" * 50)
                            print(f"处理时间: {processing_time:.2f}秒")
                            print("=" * 50)
                        else:
                            logger.warning("无法提取关键信息")
                            print("无法提取关键信息，尝试直接回答...")

                            # 直接使用千问模型回答
                            direct_answer = direct_model_answer(user_question)

                            end_time = time.time()
                            processing_time = end_time - start_time

                            print("\n" + "=" * 50)
                            print("回答:")
                            print(direct_answer)
                            print("-" * 50)
                            print(f"处理时间: {processing_time:.2f}秒")
                            print("=" * 50)
                    else:
                        logger.warning("未找到相似的问题，尝试直接回答")
                        print("未找到相似的问题，尝试直接回答...")

                        # 直接使用千问模型回答
                        direct_answer = direct_model_answer(user_question)

                        end_time = time.time()
                        processing_time = end_time - start_time

                        print("\n" + "=" * 50)
                        print("回答:")
                        print(direct_answer)
                        print("-" * 50)
                        print(f"处理时间: {processing_time:.2f}秒")
                        print("=" * 50)

                except Exception as e:
                    logger.error(f"处理问题时出错: {str(e)}")
                    logger.debug(traceback.format_exc())
                    print(f"处理您的问题时出错: {str(e)}")
                    print("请稍后再试或尝试其他问题")

            except KeyboardInterrupt:
                print("\n操作被用户中断")
                break
            except Exception as e:
                logger.error(f"发生未预期的错误: {str(e)}")
                logger.debug(traceback.format_exc())
                print(f"发生错误: {str(e)}")

    except Exception as e:
        logger.critical(f"程序发生严重错误: {str(e)}")
        logger.debug(traceback.format_exc())
        print(f"程序发生错误: {str(e)}")
        print("请检查日志文件获取更多信息")

# 直接使用模型回答问题
def direct_model_answer(user_question):
    """
    直接使用千问模型回答问题

    参数:
        user_question (str): 用户问题

    返回:
        str: 模型回答
    """
    prompt = f"""
你是平安理财的智能助手，请回答以下问题:

{user_question}

如果你无法回答这个问题，请说明原因并建议用户如何获取相关信息。
"""
    response = call_qwen_model(prompt)

    if "error" not in response:
        return response.output.choices[0].message.content
    else:
        logger.error(f"直接回答失败: {response.get('error')}")
        return "抱歉，我暂时无法回答这个问题。请稍后再试或联系客服获取帮助。"

if __name__ == "__main__":
    main()
