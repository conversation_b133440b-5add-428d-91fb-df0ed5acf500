"""
产品库随机名称生成器
为产品库.xlsx文件的第三列生成随机简化名称
"""

import pandas as pd
import re
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("generate_random_names.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("RandomNameGenerator")

class ProductNameGenerator:
    """产品名称随机生成器"""

    def __init__(self, excel_file="产品库测试.xlsx"):
        """
        初始化生成器

        参数:
            excel_file (str): 产品库Excel文件路径
        """
        self.excel_file = excel_file
        self.df = None
        self.generated_names = set()  # 用于避免重复

    def load_excel(self):
        """加载Excel文件"""
        try:
            logger.info(f"正在加载Excel文件: {self.excel_file}")
            self.df = pd.read_excel(self.excel_file)
            logger.info(f"成功加载Excel文件，共 {len(self.df)} 行数据")
            logger.info(f"列名: {list(self.df.columns)}")

            # 检查是否有足够的列
            if len(self.df.columns) < 2:
                logger.error("Excel文件列数不足，需要至少2列")
                return False

            # 如果没有第三列，添加第三列
            if len(self.df.columns) < 3:
                self.df['随机名称'] = ''
                logger.info("添加了第三列用于存储随机名称")
            else:
                # 重命名第三列
                self.df.columns = list(self.df.columns[:2]) + ['随机名称'] + list(self.df.columns[3:])

            return True

        except Exception as e:
            logger.error(f"加载Excel文件失败: {str(e)}")
            return False

    def extract_key_chars(self, name):
        """
        从真实名称中提取关键字符生成随机名称

        参数:
            name (str): 真实名称

        返回:
            str: 生成的随机名称
        """
        if not name or pd.isna(name):
            return ""

        name = str(name).strip()

        # 定义提取规则
        rules = [
            self._extract_bank_name,
            self._extract_company_name,
            self._extract_product_name,
            self._extract_location_name,
            self._extract_generic_name
        ]

        # 尝试每个规则
        for rule in rules:
            try:
                result = rule(name)
                if result and len(result) >= 2:  # 确保生成的名称至少2个字符
                    return result
            except Exception as e:
                logger.debug(f"规则处理失败: {str(e)}")
                continue

        # 如果所有规则都失败，使用通用方法
        return self._generate_fallback_name(name)

    def _extract_bank_name(self, name):
        """提取银行名称的关键字符"""
        # 银行相关模式
        bank_patterns = [
            r'(.{2,6})农村商业银行',
            r'(.{2,6})农商银行',
            r'(.{2,6})农村信用',
            r'(.{2,6})信用社',
            r'(.{2,6})村镇银行',
            r'(.{2,6})银行'
        ]

        for pattern in bank_patterns:
            match = re.search(pattern, name)
            if match:
                location = match.group(1)
                # 清理地名
                location = re.sub(r'(省|市|县|区|自治区)$', '', location)

                # 根据银行类型生成简化名称
                if '农村商业银行' in name or '农商银行' in name:
                    if len(location) > 2:
                        return location[:2] + '农商'
                    else:
                        return location + '农商'
                elif '农村信用' in name or '信用社' in name:
                    if len(location) > 2:
                        return location[:2] + '农信'
                    else:
                        return location + '农信'
                elif '村镇银行' in name:
                    if len(location) > 2:
                        return location[:2] + '村镇'
                    else:
                        return location + '村镇'
                else:
                    # 普通银行
                    if len(location) > 2:
                        return location[:2] + '银行'
                    else:
                        return location + '银行'

        return None

    def _extract_company_name(self, name):
        """提取公司名称的关键字符"""
        # 理财公司模式
        if '理财' in name:
            # 提取理财公司名称
            patterns = [
                r'(.{2,6})理财有限责任公司',
                r'(.{2,6})理财有限公司',
                r'(.{2,6})理财'
            ]
            for pattern in patterns:
                match = re.search(pattern, name)
                if match:
                    company_name = match.group(1)
                    # 去除常见前缀
                    company_name = re.sub(r'^(中国|中华)', '', company_name)
                    if len(company_name) > 2:
                        return company_name[:2] + '理财'
                    else:
                        return company_name + '理财'

        # 基金公司模式
        if '基金' in name:
            patterns = [
                r'(.{2,6})基金管理有限公司',
                r'(.{2,6})基金'
            ]
            for pattern in patterns:
                match = re.search(pattern, name)
                if match:
                    company_name = match.group(1)
                    company_name = re.sub(r'^(中国|中华)', '', company_name)
                    if len(company_name) > 2:
                        return company_name[:2] + '基金'
                    else:
                        return company_name + '基金'

        # 证券公司模式
        if '证券' in name:
            patterns = [
                r'(.{2,6})证券股份有限公司',
                r'(.{2,6})证券有限公司',
                r'(.{2,6})证券'
            ]
            for pattern in patterns:
                match = re.search(pattern, name)
                if match:
                    company_name = match.group(1)
                    company_name = re.sub(r'^(中国|中华)', '', company_name)
                    if len(company_name) > 2:
                        return company_name[:2] + '证券'
                    else:
                        return company_name + '证券'

        # 保险公司模式
        if '保险' in name:
            patterns = [
                r'(.{2,6})保险股份有限公司',
                r'(.{2,6})保险有限公司',
                r'(.{2,6})保险'
            ]
            for pattern in patterns:
                match = re.search(pattern, name)
                if match:
                    company_name = match.group(1)
                    company_name = re.sub(r'^(中国|中华)', '', company_name)
                    if len(company_name) > 2:
                        return company_name[:2] + '保险'
                    else:
                        return company_name + '保险'

        return None

    def _extract_product_name(self, name):
        """提取产品名称的关键字符"""
        # 产品相关模式
        product_patterns = [
            r'(.{2,4})理财',
            r'(.{2,4})基金',
            r'(.{2,4})产品',
            r'(.{2,4})计划'
        ]

        for pattern in product_patterns:
            match = re.search(pattern, name)
            if match:
                product_name = match.group(1)
                return product_name

        return None

    def _extract_location_name(self, name):
        """提取地名的关键字符"""
        # 地名相关模式
        location_patterns = [
            r'(北京|上海|天津|重庆)',  # 直辖市
            r'(.{2,3}省)',  # 省份
            r'(.{2,3}市)',  # 城市
            r'(.{2,3}县)',  # 县
            r'(.{2,3}区)'   # 区
        ]

        for pattern in location_patterns:
            match = re.search(pattern, name)
            if match:
                location = match.group(1)
                # 去掉省市县区后缀
                location = re.sub(r'[省市县区]$', '', location)
                if len(location) >= 2:
                    return location[:2]  # 取前两个字符

        return None

    def _extract_generic_name(self, name):
        """通用名称提取方法"""
        # 移除常见后缀
        suffixes = ['有限责任公司', '股份有限公司', '有限公司', '银行', '集团', '控股', '投资', '基金', '证券', '保险', '信托', '理财']
        clean_name = name
        for suffix in suffixes:
            clean_name = clean_name.replace(suffix, '')

        # 移除常见前缀
        prefixes = ['中国', '中华', '国家', '全国']
        for prefix in prefixes:
            if clean_name.startswith(prefix):
                clean_name = clean_name[len(prefix):]

        # 移除数字和特殊字符
        clean_name = re.sub(r'[0-9\W]', '', clean_name)

        # 如果清理后的名称太短，使用原名称的前几个字符
        if len(clean_name) < 2:
            # 从原名称中提取中文字符
            chinese_chars = re.findall(r'[\u4e00-\u9fff]', name)
            if len(chinese_chars) >= 2:
                clean_name = ''.join(chinese_chars[:3])
            else:
                clean_name = name[:3] if len(name) >= 3 else name

        # 取前2-3个字符
        if len(clean_name) >= 3:
            return clean_name[:3]
        elif len(clean_name) >= 2:
            return clean_name
        else:
            return name[:2] if len(name) >= 2 else name

    def _generate_fallback_name(self, name):
        """备用名称生成方法"""
        # 如果所有规则都失败，使用简单的字符提取
        # 移除数字和特殊字符，只保留中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', name)

        if len(chinese_chars) >= 2:
            # 取前2-3个中文字符
            result = ''.join(chinese_chars[:3])
        else:
            # 如果中文字符不够，使用原名称的前几个字符
            result = name[:3] if len(name) >= 3 else name

        return result

    def generate_all_random_names(self):
        """为所有行生成随机名称"""
        if self.df is None:
            logger.error("Excel文件未加载")
            return False

        logger.info("开始生成随机名称...")

        # 获取第二列（真实值列）
        real_values_col = self.df.columns[1]
        logger.info(f"使用第二列作为真实值列: {real_values_col}")

        generated_count = 0

        for index, row in self.df.iterrows():
            real_value = row[real_values_col]

            if pd.isna(real_value) or not str(real_value).strip():
                logger.debug(f"第 {index + 1} 行真实值为空，跳过")
                continue

            # 生成随机名称
            random_name = self.extract_key_chars(real_value)

            # 更新第三列
            self.df.iloc[index, 2] = random_name

            logger.info(f"第 {index + 1} 行: '{real_value}' -> '{random_name}'")
            generated_count += 1

        logger.info(f"随机名称生成完成，共生成 {generated_count} 个名称")
        return True

    def save_excel(self):
        """保存Excel文件"""
        try:
            # 生成带时间戳的备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"产品库_备份_{timestamp}.xlsx"

            # 先保存备份
            self.df.to_excel(backup_file, index=False)
            logger.info(f"备份文件已保存: {backup_file}")

            # 保存到原文件
            self.df.to_excel(self.excel_file, index=False)
            logger.info(f"原文件已更新: {self.excel_file}")

            return True

        except Exception as e:
            logger.error(f"保存Excel文件失败: {str(e)}")
            return False

    def preview_results(self, num_rows=10):
        """预览生成结果"""
        if self.df is None:
            logger.error("Excel文件未加载")
            return

        logger.info(f"预览前 {num_rows} 行生成结果:")

        for i in range(min(num_rows, len(self.df))):
            real_value = self.df.iloc[i, 1]
            random_name = self.df.iloc[i, 2]
            logger.info(f"  {i+1}. '{real_value}' -> '{random_name}'")

def main():
    """主函数"""
    print("🚀 产品库随机名称生成器")
    print("=" * 50)

    # 创建生成器
    generator = ProductNameGenerator()

    # 加载Excel文件
    if not generator.load_excel():
        print("❌ 加载Excel文件失败")
        return

    # 预览原始数据
    print(f"\n📊 原始数据预览:")
    generator.preview_results(5)

    # 询问用户是否继续
    print(f"\n即将为 {len(generator.df)} 行数据生成随机名称...")
    user_input = input("是否继续？(y/n): ").lower().strip()
    if user_input != 'y':
        print("用户取消操作")
        return

    # 生成随机名称
    if not generator.generate_all_random_names():
        print("❌ 生成随机名称失败")
        return

    # 预览生成结果
    print(f"\n📋 生成结果预览:")
    generator.preview_results(10)

    # 保存文件
    if generator.save_excel():
        print("✅ 文件保存成功")
    else:
        print("❌ 文件保存失败")

    print("\n🎉 随机名称生成完成！")
    print(f"生成的随机名称数量: {len(generator.generated_names)}")

if __name__ == "__main__":
    main()
