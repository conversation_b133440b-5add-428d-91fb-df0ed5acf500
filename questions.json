[{"序号": 1, "用户问题": "3. 提取本月第一天权益类产品的预约申购金额和预约赎回金额？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['权益'], '数据日期': ['20250501-20250501'], '指标名': ['申购金额', '赎回金额']}", "输出时效": 32.8756988048553, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 2, "用户问题": "9. 提取启航系列的产品，在近三天内各渠道的持仓客户数分布。", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': [], '数据日期': ['20250509-20250512'], '产品系列': ['新启航'], '指标名': ['持仓客户数']}", "输出时效": 8.61022424697876, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 3, "用户问题": "5. 获取过去 20 天中， 中小行渠道且产品大类为 ' 固收 + ' 的产品的预约赎回金额汇总", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品分类': ['固收+'], '数据日期': ['20250422-20250512'], '渠道二级分类': ['代销-中小行'], '指标名': ['赎回金额']}", "输出时效": 10.115015745163, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 4, "用户问题": "4. 哪种渠道一级分类的持仓规模最大，是否随时间变化而改变？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道一级分类': [], '指标名': ['持仓规模']}", "输出时效": 8.79832935333252, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 5, "用户问题": "10. 持仓份额和持仓规模之间的比例关系在不同产品大类中是否一致？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品分类': [], '指标名': ['持仓份额', '持仓规模']}", "输出时效": 8.25843906402588, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 6, "用户问题": "19. 综合考虑渠道、产品大类和业务日期，哪些因素对净申赎金额（N_NET_SG_AMT）的影响最为显著？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品分类': [], '数据日期': [], '指标名': ['净申赎金额']}", "输出时效": 8.46568965911865, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 7, "用户问题": "对于人民币理财产品，不同产品期限含义的产品占比分别是多少？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品期限': []}", "输出时效": 6.93982982635498, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 8, "用户问题": "20240224，对公和零售渠道下，产品至顺1号69期和新启航一年定开24号的持仓份额和持仓规模分别是多少？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航一年定开24号', '至顺1号69期'], '数据日期': ['20240224-20240224'], '渠道一级分类': ['对公', '零售'], '指标名': ['持仓份额', '持仓规模']}", "输出时效": 34.7827913761139, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 9, "用户问题": "20240224，零售渠道的产品，按照产品和币种维度下的持仓规模是多少？？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20240224-20240224'], '渠道一级分类': ['零售'], '指标名': ['持仓规模']}", "输出时效": 10.0371556282043, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 10, "用户问题": "20240201，零售渠道下，人民币中高风险净值型产品，业绩基准和持仓规模是多少？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['净值'], '风险评级': ['R4级(中高风险)'], '数据日期': ['20240201-20240201'], '渠道一级分类': ['零售'], '指标名': ['持仓规模']}", "输出时效": 13.5421233177185, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 11, "用户问题": "20240201，零售和对公渠道下，R3级(中等风险)人民币产品，按产品和组合维度，其持仓规模和持仓客户数分别为多少？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'组合名称': [], '风险评级': ['R3级(中等风险)'], '产品简称': [], '数据日期': ['20240201-20240201'], '渠道一级分类': ['对公', '零售'], '指标名': ['持仓客户数', '持仓规模']}", "输出时效": 14.5627431869507, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 12, "用户问题": "人民币产品的近20天的持仓规模总和是多少？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250422-20250512'], '指标名': ['持仓规模']}", "输出时效": 10.3374423980713, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 13, "用户问题": "子产品7天成长4号C持仓份额占组合7天成长4号总份额的比例是多少？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['7天成长4号C'], '指标名': ['持仓份额'], '组合简称': ['7天成长4号']}", "输出时效": 12.9851801395416, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 14, "用户问题": "哪些币种的理财产品采用了市价法进行估值？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '估值时效': []}", "输出时效": 11.9125247001648, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 15, "用户问题": "零售和对公渠道下的净值型产品，持仓份额和持仓客户数是多少？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['净值'], '渠道一级分类': ['对公', '零售'], '指标名': ['持仓客户数', '持仓份额']}", "输出时效": 13.436511516571, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 16, "用户问题": "母产品为平安理财7天成长4号的固收类人民币成立成功理财产品，不同产品代码对应的理财产品的计息基础是怎样的？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品代码': [], '组合名称': [], '产品简称': [], '产品分类': ['固定收益类'], '产品状态': ['成立成功'], '产品全称': ['平安理财7天成长4号固收类理财产品']}", "输出时效": 13.9073719978333, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 17, "用户问题": "人民币中等风险产品，不同组合下的理财产品的开放类型含义和下一开放日是什么？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'组合名称': [], '风险评级': ['R3级(中等风险)'], '产品简称': [], '开放类型': ['开放式']}", "输出时效": 14.1269114017487, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 18, "用户问题": "2025年02月01日至2025年02月25日，渠道、产品、组合和产品系列维度下，个人客户的人民币成立成功的开放式理财产品，持仓客户数和成立日是怎样的？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '组合名称': [], '客户类型': ['个人'], '产品简称': [], '开放类型': ['开放式'], '成立日': [], '数据日期': ['20250201-20250225'], '产品系列': [], '产品状态': ['成立成功'], '指标名': ['持仓客户数']}", "输出时效": 24.4728047847748, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 19, "用户问题": "2025年02月24日，渠道、产品系列和产品组合下，人民币净值型非标产品，且不是优先股，持仓份额和开放类型含义是什么？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品收益类型': ['净值'], '组合名称': [], '开放类型': [], '数据日期': ['20250224-20250224'], '产品系列': [], '指标名': ['持仓份额']}", "输出时效": 17.9071578979492, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 20, "用户问题": "2025年02月01日至2025年02月25日，渠道、组合和产品维度下，个人客户的人民币中高风险预期收益的产品，且为封闭式长期期限，募集开始日和募集结束日是多少？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品收益类型': ['预期收益'], '组合名称': [], '客户类型': ['个人'], '风险评级': ['R4级(中高风险)'], '产品简称': [], '开放类型': ['封闭式'], '产品期限': [], '产品状态': ['募集中']}", "输出时效": 23.2403116226196, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 21, "用户问题": "2025年02月01日，零售渠道下，人民币已到期非现金管理类的产品，且为权益类优先股，业绩基准小于3%时，持仓份额和上一开放日是什么？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '开放类型': ['开放式'], '产品分类': ['权益'], '数据日期': ['20250201-20250201'], '产品状态': ['已到期'], '渠道一级分类': ['零售'], '指标名': ['持仓份额']}", "输出时效": 20.5464315414429, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 22, "用户问题": "今天固定收益类在代销的净申赎金额是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250512-20250512'], '渠道一级分类': ['代销'], '指标名': ['净申赎金额']}", "输出时效": 10.5813310146332, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 23, "用户问题": "今天新启航三个月定开1号产品净申赎金额是多少？", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开1号'], '数据日期': ['20250512-20250512'], '指标名': ['净申赎金额']}", "输出时效": 11.7680084705353, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 24, "用户问题": "今天新启航三个月定开4号A在国股行的赎回金额是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250512-20250512'], '渠道二级分类': ['代销-国股行'], '指标名': ['赎回金额']}", "输出时效": 12.8820142745972, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 25, "用户问题": "今天启航创利在国股行的申购金额是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250512-20250512'], '渠道二级分类': ['代销-国股行'], '产品系列': ['启航创利'], '指标名': ['申购金额']}", "输出时效": 11.622533082962, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 26, "用户问题": "今天招商银行渠道的申购金额是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250512-20250512'], '指标名': ['申购金额']}", "输出时效": 10.0665783882141, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 27, "用户问题": "今天平安理财灵活成长汇稳28天持有固收类理财产品在招商银行的申购金额是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '组合名称': ['平安理财灵活成长汇稳28天持有固收类理财产品'], '数据日期': ['20250512-20250512'], '指标名': ['申购金额']}", "输出时效": 13.3424487113953, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 28, "用户问题": "今天招商银行渠道的赎回金额是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250512-20250512'], '指标名': ['赎回金额']}", "输出时效": 10.5656995773315, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 29, "用户问题": "今天平安理财灵活成长添利日开30天持有3号固收类理财产品在国股行的赎回金额是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'组合名称': ['平安理财灵活成长添利日开30天持有3号固收类理财产品'], '数据日期': ['20250512-20250512'], '渠道二级分类': ['代销-国股行'], '指标名': ['赎回金额']}", "输出时效": 14.3102507591248, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 30, "用户问题": "今天新启航三个月定开4号A在招商银行的净申赎金额是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250512-20250512'], '指标名': ['净申赎金额']}", "输出时效": 13.4317078590393, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 31, "用户问题": "今天招商银行渠道的净申赎金额是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250512-20250512'], '指标名': ['净申赎金额']}", "输出时效": 10.9596908092499, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 32, "用户问题": "2025年3月20日固定收益类在国股行的持仓规模是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓规模']}", "输出时效": 13.5624911785126, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 33, "用户问题": "2025年3月20日固定收益类在代销的持仓规模是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓规模']}", "输出时效": 15.4524767398834, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 34, "用户问题": "2025年3月20日新启航三个月定开4号A的持仓份额是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '指标名': ['持仓份额']}", "输出时效": 15.7061882019043, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 35, "用户问题": "2025年3月20日新启航三个月定开4号A的持仓规模是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 16.2332766056061, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 36, "用户问题": "2025年3月20日新启航三个月定开4号A在国股行的持仓份额是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓份额']}", "输出时效": 17.2556195259094, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 37, "用户问题": "2025年3月20日新启航三个月定开4号A在国股行的持仓规模是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓规模']}", "输出时效": 17.5905017852783, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 38, "用户问题": "2025年3月20日启航创利的总客户数是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '指标名': ['持仓客户数']}", "输出时效": 13.5976991653442, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 39, "用户问题": "2025年3月20日启航创利在国股行的总客户数是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '产品系列': ['启航创利'], '指标名': ['持仓客户数']}", "输出时效": 17.0240070819855, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 40, "用户问题": "2025年3月20日启航创利在代销的总客户数是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '渠道一级分类': ['代销'], '指标名': ['持仓客户数']}", "输出时效": 16.3235280513763, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 41, "用户问题": "2025年3月20日国股行的总客户数对比上日变动了多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓客户数']}", "输出时效": 9.25259566307068, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 42, "用户问题": "2025年3月20日国股行客户数是多少?", "案例人员": "马璐", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓客户数']}", "输出时效": 6.55459928512573, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 43, "用户问题": "不同风险等级产品的持仓占比较去年同期有何变动？", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'风险评级': [], '指标名': ['持仓份额']}", "输出时效": 7.38729476928711, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 44, "用户问题": "零售渠道卖得最好的产品是哪只？（持仓人数最多、持仓规模最大的产品）", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '渠道一级分类': ['零售'], '指标名': ['持仓客户数', '持仓规模']}", "输出时效": 9.70589804649353, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 45, "用户问题": "老理财还剩多少规模？有多少客户？", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['老理财'], '指标名': ['持仓客户数', '持仓规模']}", "输出时效": 7.50618886947632, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 46, "用户问题": "灵活成长系列今年以来的持仓走势是怎样的？波动最大的时期是什么时候？", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250101-20250511'], '产品系列': ['灵活成长', '启元稳利'], '指标名': ['持仓份额', '持仓规模']}", "输出时效": 8.52779340744019, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 47, "用户问题": "按照期限、母产品，对比下卓稳-固收和卓稳-固收+产品的成立以来年化收益率与当前持有人数、持仓规模差异", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': [], '组合名称': [], '产品分类': ['固收+'], '数据日期': ['20250511-20250511'], '产品期限': [], '指标名': ['持仓客户数', '持仓规模']}", "输出时效": 13.8124444484711, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 49, "用户问题": "月增AUM最多的渠道是哪个？增长率是多少？以及哪只产品增速最大？", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': []}", "输出时效": 9.32861065864563, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 52, "用户问题": "净申赎排前5的产品是哪些，分别是多少金额？", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '指标名': ['净申赎金额', '持仓规模']}", "输出时效": 8.05936527252197, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 50, "用户问题": "天天成长c的净申赎金额为多少？", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'指标名': ['净申赎金额'], '组合简称': ['天天成长C款']}", "输出时效": 7.44213223457336, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 51, "用户问题": "天天成长A各渠道的净申赎占比分别是多少？较上月末、季度末有何变动？哪些渠道的占比在上升？", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250511-20250511'], '指标名': ['净申赎金额'], '组合简称': ['天天成长A款']}", "输出时效": 13.3933827877045, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 189, "用户问题": "8. 查询固收 +的所有产品，在昨天的预约申购金额。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类明细_企划=固收 +,\n产品简称= ,\n数据日期=最新一天,\n指标名=预约申购金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品分类': ['固收+'], '数据日期': ['20250511-20250511'], '指标名': ['申购金额']}", "输出时效": 19.9461743831634, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 53, "用户问题": "安心稳健日开产品最近一周的申赎情况，按时间降序展现。", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['安心稳健日开'], '数据日期': ['20250505-20250512'], '指标名': ['申购金额', '净申赎金额', '赎回金额', '赎回份额']}", "输出时效": 9.7776358127594, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 54, "用户问题": "明天预约赎回最多10只产品是哪些产品？", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250513-20250513'], '指标名': ['赎回金额']}", "输出时效": 9.25499057769775, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 55, "用户问题": "2025年3月20日新启航三个月定开1号产品净申赎金额是多少？", "案例人员": "张合军", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开1号'], '数据日期': ['20250320-20250320'], '指标名': ['净申赎金额']}", "输出时效": 5.75365471839905, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 56, "用户问题": "2025年3月20日固定收益的持仓规模对比上个月同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 6.68507099151611, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 57, "用户问题": "2025年3月20日固定收益的持仓规模对比上日变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 3.72119688987732, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 58, "用户问题": "2025年3月20日固定收益类的持仓规模对比上一年同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 4.95359492301941, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 59, "用户问题": "近三天固定收益类的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250317-20230319 产品分类=固定收益类 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250509-20250512'], '指标名': ['持仓规模']}", "输出时效": 3.31683325767517, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 60, "用户问题": "2025年3月20日固定收益的总客户数对比上个月同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 33.5435185432434, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 61, "用户问题": "2025年3月20日固收类的总客户数对比上日变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 12.5978097915649, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 62, "用户问题": "2025年3月20日固定收益类的总客户数对比上一年同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 8.41849422454834, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 63, "用户问题": "2025年3月20日固定收益类的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 6.95888185501099, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 64, "用户问题": "2025年3月20日固定收益类在国股行的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道二级分类=代销-国股行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓客户数']}", "输出时效": 9.68413639068604, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 65, "用户问题": "2025年3月20日固定收益类在招商银行的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道名称=招商银行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 29.3747789859772, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 66, "用户问题": "2025年3月20日固定收益类在招商银行的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道名称=招商银行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 9.49969244003296, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 67, "用户问题": "2025年3月20日固定收益类在代销的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道一级分类=代销 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓客户数']}", "输出时效": 9.45177030563354, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 68, "用户问题": "2025年3月20日新启航三个月定开4号A的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 8.82808494567871, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 69, "用户问题": "2025年3月20日新启航三个月定开4号A在国股行的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道二级分类=代销-国股行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓客户数']}", "输出时效": 15.5797271728516, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 70, "用户问题": "2025年3月20日新启航三个月定开4号A在招商银行的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道名称=招商银行 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '指标名': ['持仓份额']}", "输出时效": 9.6104371547699, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 71, "用户问题": "2025年3月20日新启航三个月定开4号A在招商银行的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道名称=招商银行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 10.2644214630127, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 72, "用户问题": "2025年3月20日新启航三个月定开4号A在招商银行的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道名称=招商银行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 10.2614448070526, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 73, "用户问题": "2025年3月20日新启航三个月定开4号A在代销的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道一级分类=代销 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓份额']}", "输出时效": 9.55035161972046, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 74, "用户问题": "2025年3月20日新启航三个月定开4号A在代销的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道一级分类=代销 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓规模']}", "输出时效": 9.28312468528748, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 75, "用户问题": "2025年3月20日新启航三个月定开4号A在代销的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道一级分类=代销 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓客户数']}", "输出时效": 10.4793787002563, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 76, "用户问题": "2025年3月20日灵活成长的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=灵活成长 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品系列': ['灵活成长'], '指标名': ['持仓规模']}", "输出时效": 8.71583819389343, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 77, "用户问题": "2025年3月20日启航创利在国股行的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道二级分类=代销-国股行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '产品系列': ['启航创利'], '指标名': ['持仓规模']}", "输出时效": 9.57986903190613, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 78, "用户问题": "2025年3月20日启航创利在招商银行的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道名称=招商银行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '指标名': ['持仓规模']}", "输出时效": 8.39962482452393, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 79, "用户问题": "2025年3月20日启航创利在招商银行的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道名称=招商银行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '指标名': ['持仓客户数']}", "输出时效": 8.53601002693176, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 80, "用户问题": "2025年3月20日启航创利在代销的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道一级分类=代销 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '渠道一级分类': ['代销'], '指标名': ['持仓规模']}", "输出时效": 5.92487668991089, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 81, "用户问题": "2025年3月20日国股行的持仓规模对比上个月同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道二级分类=代销-国股行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓规模']}", "输出时效": 9.05956244468689, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 82, "用户问题": "2025年3月20日国股行的持仓规模对比上日变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道二级分类=代销-国股行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓规模']}", "输出时效": 8.26424407958984, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 83, "用户问题": "2025年3月20日国股行的持仓规模对比上一年同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道二级分类=代销-国股行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓规模']}", "输出时效": 10.8193781375885, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 84, "用户问题": "2025年3月20日国股行的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道二级分类=代销-国股行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓规模']}", "输出时效": 35.1378808021545, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 85, "用户问题": "2025年3月20日国股行的总客户数对比上个月同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道二级分类=代销-国股行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓客户数']}", "输出时效": 16.8741989135742, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 86, "用户问题": "2025年3月20日国股行的总客户数对比上一年同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道二级分类=代销-国股行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓客户数']}", "输出时效": 17.1068005561829, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 87, "用户问题": "2025年3月20日国股行的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道二级分类=代销-国股行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓客户数']}", "输出时效": 18.9304888248444, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 88, "用户问题": "2025年3月20日国股行客户数对比本年初变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道二级分类=代销-国股行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓客户数']}", "输出时效": 13.7509231567383, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 89, "用户问题": "2025年3月20日招商银行渠道的规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道名称=招商银行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 14.4552764892578, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 90, "用户问题": "2025年3月20日代销的持仓规模对比上个月同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道一级分类=代销 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓规模']}", "输出时效": 13.4830613136292, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 91, "用户问题": "2025年3月20日代销的持仓规模对比上日变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道一级分类=代销 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓规模']}", "输出时效": 17.1335871219635, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 92, "用户问题": "2025年3月20日代销的持仓规模对比上一年同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道一级分类=代销 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓规模']}", "输出时效": 14.8917980194092, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 93, "用户问题": "2025年3月20日代销的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道一级分类=代销指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓客户数']}", "输出时效": 14.8721790313721, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 94, "用户问题": "2025年3月20日代销的总客户数对比本年初变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道一级分类=代销 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓客户数']}", "输出时效": 16.0226151943207, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 95, "用户问题": "2025年3月20日灵活成长汇稳28天持有的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=灵活成长汇稳28天持有 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['持仓份额'], '组合简称': ['灵活成长汇稳28天持有']}", "输出时效": 7.09931755065918, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 96, "用户问题": "2025年3月20日代销的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道一级分类=代销 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓规模']}", "输出时效": 7.32643699645996, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 97, "用户问题": "2025年3月20日灵活成长汇稳28天持有的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=灵活成长汇稳28天持有 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['持仓客户数'], '组合简称': ['灵活成长汇稳28天持有']}", "输出时效": 7.80885457992554, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 98, "用户问题": "2025年3月20日灵活成长汇稳28天持有在国股行的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=灵活成长汇稳28天持有 渠道二级分类=代销-国股行 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓份额'], '组合简称': ['灵活成长汇稳28天持有']}", "输出时效": 34.8857097625732, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 99, "用户问题": "2025年3月20日灵活成长汇稳28天持有的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=灵活成长汇稳28天持有 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['持仓规模'], '组合简称': ['灵活成长汇稳28天持有']}", "输出时效": 11.1175801753998, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 100, "用户问题": "2025年3月20日灵活成长汇稳28天持有在国股行的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=灵活成长汇稳28天持有 渠道二级分类=代销-国股行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓客户数'], '组合简称': ['灵活成长汇稳28天持有']}", "输出时效": 15.4730377197266, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 101, "用户问题": "2025年3月20日灵活成长汇稳28天持有在招商银行的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=灵活成长汇稳28天持有 渠道名称=招商银行 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '指标名': ['持仓份额'], '组合简称': ['灵活成长汇稳28天持有']}", "输出时效": 14.9689435958862, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 102, "用户问题": "2025年3月20日灵活成长汇稳28天持有在国股行的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=灵活成长汇稳28天持有 渠道二级分类=代销-国股行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓规模'], '组合简称': ['灵活成长汇稳28天持有']}", "输出时效": 12.8547208309174, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 103, "用户问题": "2025年3月20日灵活成长汇稳28天持有在招商银行的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=灵活成长汇稳28天持有 渠道名称=招商银行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数'], '组合简称': ['灵活成长汇稳28天持有']}", "输出时效": 21.9873099327087, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 104, "用户问题": "2025年3月20日灵活成长汇稳28天持有在代销的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称灵活成长汇稳28天持有 渠道一级分类=代销 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓份额'], '组合简称': ['灵活成长汇稳28天持有']}", "输出时效": 12.9054589271545, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 105, "用户问题": "2025年3月20日灵活成长汇稳28天持有在招商银行的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称灵活成长汇稳28天持有 号渠道名称=招商银行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模'], '组合简称': ['灵活成长汇稳28天持有']}", "输出时效": 33.4895586967468, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 106, "用户问题": "2025年3月20日新启航三个月定开1号在代销的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=新启航三个月定开1号 渠道一级分类=代销 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓客户数'], '组合简称': ['新启航三个月定开1号']}", "输出时效": 16.1258544921875, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 107, "用户问题": "2025年3月20日新启航三个月定开1号在代销的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=新启航三个月定开1号 渠道一级分类=代销 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['持仓规模'], '组合简称': ['新启航三个月定开1号']}", "输出时效": 14.6496679782867, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 108, "用户问题": "2025年3月20日持仓规模对比上个月同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 14.365688085556, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 109, "用户问题": "2025年3月20日持仓规模对比上日变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 12.3389654159546, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 110, "用户问题": "2025年3月20日持仓规模对比上一年同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 12.2133991718292, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 111, "用户问题": "2025年3月20日总客户数对比上个月同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 15.5743937492371, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 112, "用户问题": "2025年3月20日总客户数对比上日变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 12.3476858139038, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 113, "用户问题": "2025年3月20日总客户数对比上一年同期变动了多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 17.2911803722382, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 114, "用户问题": "2025年3月20日总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 12.7104008197784, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 115, "用户问题": "2025年3月20日持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 12.6983120441437, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 116, "用户问题": "2025年3月20日招商银行渠道的客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 渠道名称=招商银行 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 18.79607462883, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 117, "用户问题": "2025年3月20日固定收益类在机构的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 客户类型=机构 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 14.7300112247467, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 118, "用户问题": "2025年3月20日固定收益类在机构的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 客户类型=机构 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 15.4806933403015, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 119, "用户问题": "2025年3月20日启航在机构的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航成长 客户类型=机构 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '数据日期': ['20250320-20250320'], '产品系列': ['启航成长'], '指标名': ['持仓规模']}", "输出时效": 16.6319200992584, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 120, "用户问题": "2025年3月20日新启航三个月定开1号在机构的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开1号 客户类型=机构 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '数据日期': ['20250320-20250320'], '指标名': ['持仓份额'], '组合简称': ['新启航三个月定开1号']}", "输出时效": 16.2376971244812, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 121, "用户问题": "2025年3月20日新启航三个月定开1号在机构的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开1号 客户类型=机构 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模'], '组合简称': ['新启航三个月定开1号']}", "输出时效": 16.8657710552216, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 122, "用户问题": "2025年3月20日新启航三个月定开1号在机构的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开1号 客户类型=机构 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数'], '组合简称': ['新启航三个月定开1号']}", "输出时效": 16.5765879154205, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 123, "用户问题": "2025年3月20日启航在机构的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航成长 客户类型=机构 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '数据日期': ['20250320-20250320'], '产品系列': ['启航成长'], '指标名': ['持仓客户数']}", "输出时效": 14.3212378025055, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 124, "用户问题": "2025年3月20日机构类型的规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 客户类型=机构 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 16.3925428390503, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 125, "用户问题": "2025年3月20日灵活成长汇稳28天持有A在机构的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=新启航（专享）半年定开12号 客户类型=机构 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '产品简称': ['灵活成长汇稳28天持有A'], '数据日期': ['20250320-20250320'], '指标名': ['持仓份额']}", "输出时效": 10.8964064121246, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 126, "用户问题": "2025年3月20日灵活成长汇稳28天持有A在机构的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=新启航（专享）半年定开12号 客户类型=机构 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '产品简称': ['灵活成长汇稳28天持有A'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 8.02182722091675, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 127, "用户问题": "2025年3月20日灵活成长汇稳28天持有A在机构的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=新启航（专享）半年定开12号 客户类型=机构 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '产品简称': ['灵活成长汇稳28天持有A'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 8.62534785270691, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 128, "用户问题": "2025年3月20日机构的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 客户类型=机构 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 8.5422101020813, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 129, "用户问题": "2025年3月20日固定收益类在R1级(低等风险)的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 风险评级=R1级(低等风险) 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R1级(低等风险)'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 9.94384765625, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 130, "用户问题": "2025年3月20日固定收益类在R1级(低等风险)的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 风险评级=R1级(低等风险) 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R1级(低等风险)'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 8.95316243171692, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 131, "用户问题": "2025年3月20日启航增强在R1级(低等风险)的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 风险评级=R1级(低等风险) 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R1级(低等风险)'], '数据日期': ['20250320-20250320'], '产品系列': ['启航增强'], '指标名': ['持仓规模']}", "输出时效": 31.8882625102997, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 132, "用户问题": "2025年3月20日启航增强在R1级(低等风险)的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 风险评级=R1级(低等风险) 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R1级(低等风险)'], '数据日期': ['20250320-20250320'], '产品系列': ['启航增强'], '指标名': ['持仓客户数']}", "输出时效": 10.5057504177094, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 133, "用户问题": "2025年3月20日R1级(低等风险)的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 风险评级=R1级(低等风险) 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R1级(低等风险)'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 15.8873147964478, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 134, "用户问题": "2025年3月20日R1级(低等风险)的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 风险评级=R1级(低等风险) 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R1级(低等风险)'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 13.8355147838592, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 135, "用户问题": "2025年3月20日天天成长3号5期在R1级(低等风险)的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 风险评级=R1级(低等风险) 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R1级(低等风险)'], '数据日期': ['20250320-20250320'], '指标名': ['持仓份额'], '组合简称': ['天天成长3号5期']}", "输出时效": 13.998286485672, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 136, "用户问题": "2025年3月20日天天成长3号5期在R1级(低等风险)的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 风险评级=R1级(低等风险) 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R1级(低等风险)'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数'], '组合简称': ['天天成长3号5期']}", "输出时效": 16.4606161117554, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 137, "用户问题": "2025年3月20日天天成长3号5期在R1级(低等风险)的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 风险评级=R1级(低等风险) 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R1级(低等风险)'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模'], '组合简称': ['天天成长3号5期']}", "输出时效": 16.2339782714844, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 138, "用户问题": "2025年3月20日固定收益类在每日开放频率的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 产品开放频率=每日开放 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日开放'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 11.2427361011505, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 139, "用户问题": "2025年3月20日固定收益类在每日开放频率的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 产品开放频率=每日开放 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日开放'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 12.250319480896, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 140, "用户问题": "2025年3月20日启航增强在每日开放频率的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 产品开放频率=每日开放 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日开放'], '数据日期': ['20250320-20250320'], '产品系列': ['启航增强'], '指标名': ['持仓规模']}", "输出时效": 11.8180494308472, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 141, "用户问题": "2025年3月20日启航增强在每日开放频率的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 产品开放频率=每日开放 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日开放'], '数据日期': ['20250320-20250320'], '产品系列': ['启航增强'], '指标名': ['持仓客户数']}", "输出时效": 6.36359739303589, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 142, "用户问题": "2025年3月20日每日开放频率的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品开放频率=每日开放 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日开放'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 7.52887678146362, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 143, "用户问题": "2025年3月20日天天成长3号5期在每日开放频率的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 产品开放频率=每日开放 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日开放'], '数据日期': ['20250320-20250320'], '指标名': ['持仓份额'], '组合简称': ['天天成长3号5期']}", "输出时效": 9.4921817779541, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 144, "用户问题": "2025年3月20日天天成长3号5期在每日开放频率的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 产品开放频率=每日开放 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日开放'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数'], '组合简称': ['天天成长3号5期']}", "输出时效": 9.1869432926178, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 145, "用户问题": "2025年3月20日天天成长3号5期在每日开放频率的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 产品开放频率=每日开放 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日开放'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模'], '组合简称': ['天天成长3号5期']}", "输出时效": 9.05294442176819, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 146, "用户问题": "2025年3月20日每日开放频率的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品开放频率=每日开放 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日开放'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 8.70239663124084, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 147, "用户问题": "2025年3月20日固定收益类在公募的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 募集方式=公募 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'募集方式': ['公募'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 9.43747758865356, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 148, "用户问题": "2025年3月20日固定收益类在公募的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 募集方式=公募 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'募集方式': ['公募'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 9.46674942970276, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 149, "用户问题": "2025年3月20日启航增强在公募的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 募集方式=公募 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'募集方式': ['公募'], '数据日期': ['20250320-20250320'], '产品系列': ['启航增强'], '指标名': ['持仓规模']}", "输出时效": 4.94444942474365, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 150, "用户问题": "2025年3月20日启航增强在公募的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 募集方式=公募 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'募集方式': ['公募'], '数据日期': ['20250320-20250320'], '产品系列': ['启航增强'], '指标名': ['持仓客户数']}", "输出时效": 6.56711196899414, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 151, "用户问题": "2025年3月20日公募方式的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 募集方式=公募 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'募集方式': ['公募'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 7.82171821594238, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 152, "用户问题": "2025年3月20日天天成长3号5期在公募的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 募集方式=公募 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'募集方式': ['公募'], '数据日期': ['20250320-20250320'], '指标名': ['持仓份额'], '组合简称': ['天天成长3号5期']}", "输出时效": 7.50373721122742, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 153, "用户问题": "2025年3月20日天天成长3号5期在公募的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 募集方式=公募 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'募集方式': ['公募'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数'], '组合简称': ['天天成长3号5期']}", "输出时效": 10.0861873626709, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 154, "用户问题": "2025年3月20日天天成长3号5期在公募的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 募集方式=公募 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'募集方式': ['公募'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模'], '组合简称': ['天天成长3号5期']}", "输出时效": 9.00596380233765, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 155, "用户问题": "2025年3月20日公募方式的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 募集方式=公募 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'募集方式': ['公募'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 8.79118800163269, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 156, "用户问题": "2025年3月20日固定收益类在开放式的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 开放类型=开放式 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['开放式'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 9.51654005050659, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 157, "用户问题": "2025年3月20日固定收益类在开放式的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 开放类型=开放式 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['开放式'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 10.1175355911255, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 158, "用户问题": "2025年3月20日启航增强在开放式的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 开放类型=开放式 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['开放式'], '数据日期': ['20250320-20250320'], '产品系列': ['启航增强'], '指标名': ['持仓规模']}", "输出时效": 31.8025369644165, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 159, "用户问题": "2025年3月20日启航增强在开放式的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 开放类型=开放式 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['开放式'], '数据日期': ['20250320-20250320'], '产品系列': ['启航增强'], '指标名': ['持仓客户数']}", "输出时效": 9.51104927062988, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 160, "用户问题": "2025年3月20日开放式的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 开放类型=0501-开放式 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['开放式'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模']}", "输出时效": 15.09796667099, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 161, "用户问题": "2025年3月20日天天成长3号5期在开放式的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 开放类型=开放式 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['开放式'], '数据日期': ['20250320-20250320'], '指标名': ['持仓份额'], '组合简称': ['天天成长3号5期']}", "输出时效": 11.9284555912018, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 162, "用户问题": "2025年3月20日天天成长3号5期在开放式的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 开放类型=开放式 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['开放式'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数'], '组合简称': ['天天成长3号5期']}", "输出时效": 14.8032801151276, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 163, "用户问题": "2025年3月20日天天成长3号5期在开放式的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 开放类型=开放式 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['开放式'], '数据日期': ['20250320-20250320'], '指标名': ['持仓规模'], '组合简称': ['天天成长3号5期']}", "输出时效": 15.2672927379608, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 164, "用户问题": "2025年3月20日开放式的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 开放类型=开放式 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['开放式'], '数据日期': ['20250320-20250320'], '指标名': ['持仓客户数']}", "输出时效": 12.5786888599396, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 165, "用户问题": "2025年3月20日固定收益类在3M期限的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 产品期限=3M 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓规模']}", "输出时效": 14.2031047344208, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 166, "用户问题": "2025年3月20日固定收益类在3个月期限的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 产品期限=3M 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓规模']}", "输出时效": 8.65414428710937, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 167, "用户问题": "2025年3月20日固定收益类在3M期限的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 产品期限=3M 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓客户数']}", "输出时效": 37.267160654068, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 168, "用户问题": "2025年3月20日固定收益类在3个月期限的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品分类=固定收益类 产品期限=3M 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓客户数']}", "输出时效": 12.8769888877869, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 169, "用户问题": "2025年3月20日启航增强在3M期限的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 产品期限=3M 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '产品系列': ['启航增强'], '指标名': ['持仓规模']}", "输出时效": 38.839426279068, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 170, "用户问题": "2025年3月20日启航增强在3个月期限的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 产品期限=3M 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '产品系列': ['启航增强'], '指标名': ['持仓规模']}", "输出时效": 34.4322419166565, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 171, "用户问题": "2025年3月20日启航增强在3M期限的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 产品期限=3M 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '产品系列': ['启航增强'], '指标名': ['持仓客户数']}", "输出时效": 41.4592728614807, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 172, "用户问题": "2025年3月20日启航增强在3个月期限的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品系列=启航增强 产品期限=3M 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '产品系列': ['启航增强'], '指标名': ['持仓客户数']}", "输出时效": 35.5059607028961, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 173, "用户问题": "2025年3月20日3M期限的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品期限=3M 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓规模']}", "输出时效": 27.434898853302, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 174, "用户问题": "2025年3月20日3个月期限的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品期限=3M 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓规模']}", "输出时效": 19.0551836490631, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 175, "用户问题": "2025年3月20日天天成长3号5期在3M期限的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 产品期限=3M 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓份额'], '组合简称': ['天天成长3号5期']}", "输出时效": 19.435672044754, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 176, "用户问题": "2025年3月20日天天成长3号5期在3个月期限的持仓份额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 产品期限=3M 指标=持仓份额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓份额'], '组合简称': ['天天成长3号5期']}", "输出时效": 18.4371376037598, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 177, "用户问题": "2025年3月20日天天成长3号5期在3M期限的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 产品期限=3M 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓客户数'], '组合简称': ['天天成长3号5期']}", "输出时效": 38.8723866939545, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 178, "用户问题": "2025年3月20日天天成长3号5期在3个月期限的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 产品期限=3M 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓客户数'], '组合简称': ['天天成长3号5期']}", "输出时效": 21.0098958015442, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 179, "用户问题": "2025年3月20日天天成长3号5期在3M期限的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 产品期限=3M 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓规模'], '组合简称': ['天天成长3号5期']}", "输出时效": 22.5099582672119, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 180, "用户问题": "2025年3月20日天天成长3号5期在3个月期限的持仓规模是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 组合简称=天天成长3号5期 产品期限=3M 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓规模'], '组合简称': ['天天成长3号5期']}", "输出时效": 17.7147324085236, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 181, "用户问题": "2025年3月20日3M期限的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品期限=3M 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓客户数']}", "输出时效": 22.587749004364, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 182, "用户问题": "2025年3月20日3个月期限的总客户数是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250320 产品期限=3M 指标=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品期限': ['3M'], '指标名': ['持仓客户数']}", "输出时效": 24.8337016105652, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 183, "用户问题": "1. 查询今天，所有渠道的规模和份额？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=今天,\n渠道名称= ,\n指标名=持仓规模,\n指标名=持仓份额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250512-20250512'], '指标名': ['持仓份额', '持仓规模']}", "输出时效": 17.1272752285004, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 184, "用户问题": "2. 获取上周五代销渠道的各产品的净申赎金额？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=上周五,\n渠道一级分类名称= 代销,\n产品简称= ,\n指标名=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250509-20250509'], '渠道一级分类': ['代销'], '指标名': ['净申赎金额']}", "输出时效": 22.5409276485443, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 185, "用户问题": "4. 国股行渠道在过去一周内每天的客户数变化情况？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=过去一周内,\n渠道二级分类名称=代销-国股行,\n指标名=每天的持仓客户数", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250505-20250512'], '渠道二级分类': ['代销-国股行'], '指标名': ['持仓客户数']}", "输出时效": 21.5834636688232, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 186, "用户问题": "5. 今天互联网银行渠道下的产品系列及对应的持仓规模？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=今天,\n渠道二级分类名称=代销-互联网银行,\n产品系列= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250512-20250512'], '渠道二级分类': ['代销-互联网银行'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 18.2225089073181, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 187, "用户问题": "6. 对公渠道在最近一个月内的净申赎金额总和。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道二级分类名称=对公,\n数据日期=最近一个月内,\n指标名=净申赎金额总和", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250412-20250512'], '渠道一级分类': ['对公'], '指标名': ['净申赎金额']}", "输出时效": 27.957665681839, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 188, "用户问题": "7. 查询天天成长3号47期A产品，在最近一周内份额和规模？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=天天成长3号47期A,\n数据日期=最近一周内,\n指标名=持仓份额,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['天天成长3号47期A'], '数据日期': ['20250505-20250512'], '指标名': ['持仓份额', '持仓规模']}", "输出时效": 24.1254022121429, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 191, "用户问题": "1. 查询过去 10 天内，系列为天天成长的所有产品持仓规模总和以及平均持仓份额？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=过去10天内,\n产品系列=财富增长系列,\n产品简称= ,\n指标名=持仓规模总和,\n指标名=平均持仓份额", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250502-20250512'], '产品系列': [], '指标名': ['持仓份额', '持仓规模']}", "输出时效": 29.7153959274292, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 190, "用户问题": "10. 查询今天零售渠道且混合类产品的净申赎金额和持仓规模。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=今天,\n渠道一级分类名称=零售,\n产品大类明细_企划=混合,\n产品简称= ,\n指标名=净申赎金额,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['混合'], '数据日期': ['20250512-20250512'], '渠道一级分类': ['零售'], '指标名': ['净申赎金额', '持仓规模']}", "输出时效": 22.7717299461365, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 193, "用户问题": "3. 计算近 15 天内，灵活成长固收类（90天持有）下所有产品的净申赎金额总和，以及净申赎金额为正的天数占比。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合简称=稳健组合,\n产品简称= ,\n数据日期=最近15天内,\n指标名=净申赎金额总和,\n指标名=净申赎金额为正", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250427-20250512'], '产品全称': ['平安理财灵活成长固收类（90天持有）理财产品'], '指标名': ['净申赎金额']}", "输出时效": 33.1349210739136, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 192, "用户问题": "2. 获取本月以来，中小行渠道中各产品大类明细的预约申购金额平均值。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=本月以来,\n渠道名称=线上平台 A,\n产品大类明细_企划 = ,\n指标名=预约申购金额平均值", "Unnamed: 6": "", "输出key+value": "{'产品分类': [], '数据日期': ['20250501-inf'], '渠道二级分类': ['代销-中小行'], '指标名': ['申购金额']}", "输出时效": 21.6386461257935, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 195, "用户问题": "5. 获取过去20天中,中小行渠道且产品大类为固收+的产品的预约赎回金额汇总，同时统计涉及的产品数量。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=过去20天内,\n渠道一级分类名称=代销-中小行,\n产品大类明细_企划=固收 +,\n产品简称= ,\n指标名=预约赎回金额汇总,\n指标名=产品数量", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品分类': ['固收+'], '数据日期': ['20250422-20250512'], '渠道二级分类': ['代销-中小行'], '指标名': ['赎回金额']}", "输出时效": 41.3312182426453, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 194, "用户问题": "4. 查找今天零售渠道，90天成长（净值型）产品的持仓客户数总和以及平均持仓规模。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=今天,\n渠道一级分类名称=零售,\n产品简称=90天成长,\n收益类型含义=净值,\n指标名=持仓客户数总和,\n指标名=平均持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['90天成长（净值型）'], '数据日期': ['20250512-20250512'], '渠道一级分类': ['零售'], '指标名': ['持仓客户数', '持仓规模']}", "输出时效": 40.9574773311615, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 200, "用户问题": "10. 查找业务日期在近两周且产品系列为 ' 启航增强 ' 的产品，同时满足其持仓客户数大于 100000 的产品的渠道名称和对应的持仓规模。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=近两周,\n产品系列=启航增强,\n渠道名称= ,\n产品简称= ,\n指标名=持仓客户数大于100000,\n指标名= 持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': [], '数据日期': ['20250428-20250512'], '产品系列': ['启航增强'], '指标名': ['持仓客户数', '持仓规模']}", "输出时效": 29.1309373378754, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 196, "用户问题": "6. 筛选出交易日期在近一周且对公渠道的所有产品，计算其持仓份额的总和以及与前一周相比的增减比例。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=最近一周内,\n渠道名称=对公,\n产品简称= ,\n指标名=持仓份额", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250505-20250512'], '渠道一级分类': ['对公'], '指标名': ['持仓份额']}", "输出时效": 37.3072493076325, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 197, "用户问题": "7. 按照产品系列对过去一个月的数据进行分组，计算每个产品系列的持仓规模占总持仓规模的比例，并找出占比最高的三个产品系列。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列= ,\n数据日期=过去一个月,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250412-20250512'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 26.9559667110443, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 198, "用户问题": "8. 将渠道一级分类进行分组，统计近 30 天内每个分组下的平均净申赎金额，比较不同渠道一级分类的资金流动情况。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道一级分类名称= ,\n数据日期=近30天内,\n指标名=平均净申赎金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250412-20250512'], '渠道一级分类': [], '指标名': ['净申赎金额']}", "输出时效": 32.5043375492096, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 199, "用户问题": "9. 以产品大类为分组依据，计算在过去 8 天内各分组的预约申购金额的中位数，并对比不同产品大类的申购水平。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类明细_企划 = ,\n数据日期=过去8天内,\n指标名=预约申购金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': [], '数据日期': ['20250504-20250512'], '指标名': ['申购金额']}", "输出时效": 31.5351636409759, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 207, "用户问题": "9. 不同产品预约赎回金额有何差异？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称= ,\n指标名=预约赎回金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '指标名': ['赎回金额']}", "输出时效": 16.7979409694672, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 201, "用户问题": "1. 业务日期的时间跨度是多久，是否存在日期缺失的情况？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期= ,", "Unnamed: 6": "", "输出key+value": "{'数据日期': []}", "输出时效": 19.2472882270813, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 202, "用户问题": "3. 在近一个月的业务日期下，各渠道的净申赎金额变化趋势是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=近一个月,\n渠道名称= ,\n指标名=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250412-20250512'], '指标名': ['净申赎金额']}", "输出时效": 23.8921010494232, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 203, "用户问题": "5. 今天哪个渠道二级分类的预约申购金额占比最高？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道二级分类名称= ,\n指标名=预约申购金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250512-20250512'], '渠道二级分类': [], '指标名': ['申购金额']}", "输出时效": 20.9000201225281, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 204, "用户问题": "6. 不同渠道其持仓客户数分布情况如何？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称 = ,\n指标名=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '指标名': ['持仓客户数']}", "输出时效": 16.3202607631683, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 205, "用户问题": "7. 根据产品类别，哪类产品的平均持仓份额最高？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类明细_企划 = ,\n产品简称= ,\n指标名=平均持仓份额", "Unnamed: 6": "", "输出key+value": "{'产品分类': [], '指标名': ['持仓份额']}", "输出时效": 14.4024114608765, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 206, "用户问题": "8. 哪个系列的净申赎金额波动最大？分析近一年的数据。", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列= ,\n数据日期=近一年,\n指标名= 净申赎金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20240512-20250512'], '产品系列': [], '指标名': ['净申赎金额']}", "输出时效": 17.4008347988129, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 214, "用户问题": "20. 如何根据持仓规模、持仓客户数和净申赎金额等指标，对各渠道和产品进行综合评价和排名？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n产品简称= ,\n指标名=持仓规模,\n指标名=持仓客户数,\n指标名=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': [], '指标名': ['净申赎金额', '持仓客户数', '持仓规模']}", "输出时效": 29.5983445644379, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 208, "用户问题": "11. 近1年来，随着时间推移，各产品的持仓规模是增长、稳定还是下降的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=近1年来,\n产品简称= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20240512-20250512'], '指标名': ['持仓规模']}", "输出时效": 20.0724151134491, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 209, "用户问题": "13. 持仓客户数较多的渠道，其客户平均持仓规模是否也较高？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n指标名=持仓客户数,\n指标名=平均持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '客户类型': [], '指标名': ['持仓客户数', '持仓规模']}", "输出时效": 18.4143347740173, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 210, "用户问题": "14. 不同产品类别的持仓客户数在不同业务日期下的变化规律是什么？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类明细_企划= ,\n数据日期= ,\n指标名=持仓客户数 ", "Unnamed: 6": "", "输出key+value": "{'产品分类': [], '数据日期': [], '指标名': ['持仓客户数']}", "输出时效": 18.1510198116302, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 211, "用户问题": "16. 不同组合，其持仓规模在不同渠道间的分布有何特点？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合简称= ,\n渠道名称= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '组合名称': [], '指标名': ['持仓规模']}", "输出时效": 21.9370925426483, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 212, "用户问题": "17. 不同系列的预约申购金额和预约赎回金额的比例关系如何，是否因产品大类而异？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列= ,\n产品大类明细_企划= ,\n指标名=预约申购金额,\n指标名=预约赎回金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': [], '产品系列': [], '指标名': ['申购金额', '赎回金额']}", "输出时效": 21.8642072677612, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 213, "用户问题": "18. 母产品下的产品数量与该组合的持仓客户数是否存在关联？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称= ,\n指标名=产品数量,\n指标名=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'组合名称': [], '指标名': ['持仓客户数']}", "输出时效": 20.2137429714203, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 220, "用户问题": "20240224，零售渠道下已到期的产品，按照产品和币种维度下的持仓规模是多少？？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,\n产品状态含义=已到期,\n产品简称= ,\n币种含义= ,\n指标名=持仓规模 ,\n数据日期=20240224", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20240224-20240224'], '产品状态': ['已到期'], '渠道一级分类': ['零售'], '指标名': ['持仓规模']}", "输出时效": 8.912433385849, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 215, "用户问题": "20240224，不同渠道对应的持仓规模分布是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n指标名=持仓规模,\n数据日期=20240224-20240224", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20240224-20240224'], '指标名': ['持仓规模']}", "输出时效": 16.4654483795166, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 216, "用户问题": "零售渠道下，不同收益类型含义的产品数量分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,\n收益类型含义= ,\n产品简称=", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': [], '产品简称': [], '渠道一级分类': ['零售']}", "输出时效": 11.4048292636871, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 217, "用户问题": "新启航一年定开24号，其投资大类含义有哪些，是否含非标资产？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=新启航一年定开24号,\n投资大类含义= ,\n是否含非标含义=", "Unnamed: 6": "", "输出key+value": "{'产品分类': [], '组合简称': ['新启航一年定开24号']}", "输出时效": 13.9978420734405, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 218, "用户问题": "天天成长B款其风险评级含义是什么，对应的投资者风险偏好是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=天天成长B款,\n产品风险评级含义= ,\n风险偏好=", "Unnamed: 6": "", "输出key+value": "{'组合简称': ['天天成长B款']}", "输出时效": 15.5788750648498, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 219, "用户问题": "20240201至20240203，各产品系列的持仓客户数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列= ,\n指标名=持仓客户数,\n数据日期=20240201-20240203", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20240201-20240203'], '产品系列': [], '指标名': ['持仓客户数']}", "输出时效": 13.2348258495331, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 222, "用户问题": "产品7天成长4号C，在不同日期和渠道代码下，持仓规模多少", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=7天成长4号C ,\n渠道名称= ,\n指标名=持仓规模 ,\n数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['7天成长4号C'], '数据日期': [], '渠道代码': [], '指标名': ['持仓规模']}", "输出时效": 4.17205429077148, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 221, "用户问题": "哪些渠道下的理财产品存在提高信用等级的情况？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= , \n产品增信标识名称='有' ,\n产品简称=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': []}", "输出时效": 6.20861124992371, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 224, "用户问题": "组合至顺5号95期，在不同日期和渠道组合下，产品风险评级为中低风险且货币型的理财产品的持仓规模和持仓客户数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=至顺5号95期 ,\n渠道名称= ,\n产品风险评级=R2级(中低风险),\n收益类型含义=货币, \n指标名=持仓规模, \n指标名=持仓客户数,\n数据日期：", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品收益类型': ['货币'], '风险评级': ['R2级(中低风险)'], '产品简称': [], '数据日期': [], '指标名': ['持仓客户数', '持仓规模'], '组合简称': ['至顺5号95期']}", "输出时效": 9.48282098770142, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 223, "用户问题": "不同理财产品，在募集开始日和募集结束日之间的时间间隔有什么特点？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称= ,\n募集开始日= ,\n募集结束日=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品状态': ['募集中']}", "输出时效": 3.67032122611999, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 226, "用户问题": "20240201至20240203，在不同日期和渠道组合下，已到期中低风险的人民币理财产品的持仓规模和持仓客户数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n组合名称= ,\n币种含义=人民币（CNY）,\n产品状态含义=已到期,\n产品风险评级含义=R2级(中低风险), 产品简称= ,\n指标名=持仓规模 ,\n指标名=持仓客户数 ,\n数据日期=20240201-20240203", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '组合名称': [], '风险评级': ['R2级(中低风险)'], '产品简称': [], '数据日期': ['20240201-20240203'], '产品状态': ['已到期'], '指标名': ['持仓客户数', '持仓规模']}", "输出时效": 6.16114139556885, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 225, "用户问题": "相同组合的理财产品，其运作模式含义是否一致？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称= ,\n产品简称= , \n运作模式含义=", "Unnamed: 6": "", "输出key+value": "{'组合名称': [], '产品简称': [], '运作模式': []}", "输出时效": 3.47165489196777, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 227, "用户问题": "20240201，渠道、产品和组合维度下，机构客户的人民币理财产品的持仓客户数和成立日是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n产品简称= ,\n组合名称= ,\n客户类型含义=机构 ,\n币种含义=人民币（CNY）,\n成立日= ,\n指标名=持仓客户数, \n数据日期：20240201", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '组合名称': [], '客户类型': ['机构'], '产品简称': [], '成立日': [], '数据日期': ['20240201-20250511'], '指标名': ['持仓客户数']}", "输出时效": 33.1095957756042, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 229, "用户问题": "2024年02月，渠道、产品和组合维度下，人民币有产品增信标识名称且为非金融性公司的理财产品有哪些，其持仓规模如何？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n产品简称= ,\n组合名称= ,\n币种含义=人民币（CNY）,\n产品增信标识名称=有 ,\n增信机构类型名称=非金融性公司,\n指标名=持仓规模 ,\n数据日期：20240201-20240228", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '组合名称': [], '产品简称': [], '数据日期': ['20240201-20240229'], '指标名': ['持仓规模']}", "输出时效": 18.4023423194885, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 228, "用户问题": "20240224，零售渠道下，人民币中低风险净值型公募产品，业绩基准和持仓规模是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,\n币种含义=人民币（CNY）,\n产品风险评级含义=R2级(中低风险),\n收益类型含义=净值,\n募集方式含义=公募, \n业绩基准= ,\n指标名=持仓规模,\n数据日期=20240224", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['净值'], '风险评级': ['R2级(中低风险)'], '募集方式': ['公募'], '数据日期': ['20240224-20240224'], '渠道一级分类': ['零售'], '指标名': ['持仓规模']}", "输出时效": 10.0997829437256, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 230, "用户问题": "20240201，R1级(低等风险)人民币货币型的公募日开型理财产品，在对公渠道的组合维度下，持仓规模和成立日是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品风险评级含义=R1级(低等风险),\n币种含义=人民币（CNY）,\n收益类型含义=货币,\n募集方式含义=公募,\n渠道名称=对公 ,\n组合名称= ,\n成立日= ,\n指标名=持仓规模 ,\n数据日期：20240201", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['货币'], '组合名称': [], '风险评级': ['R1级(低等风险)'], '募集方式': ['公募'], '产品简称': [], '成立日': [], '数据日期': ['20240201-20250511'], '渠道一级分类': ['对公'], '运作模式': ['日开型'], '指标名': ['持仓规模']}", "输出时效": 14.5232808589935, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 236, "用户问题": "20240201至20240225，天天成长系列产品在每个业务日期的持仓份额是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=天天成长,\n指标名=持仓份额,\n数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20240201-20240225'], '产品系列': ['天天成长'], '指标名': ['持仓份额']}", "输出时效": 8.60178065299988, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 231, "用户问题": "20240201，不同渠道对应的持仓规模总和是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n指标名=持仓规模,\n数据日期=20240201", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20240201-20240201'], '指标名': ['持仓规模']}", "输出时效": 7.7946400642395, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 232, "用户问题": "对公渠道在20240201到20240210日每日的持仓规模的增长率如何计算？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=对公,\n指标名=持仓规模,\n数据日期=20240201-20240210", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20240201-20240210'], '渠道一级分类': ['对公'], '指标名': ['持仓规模']}", "输出时效": 11.0741250514984, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 233, "用户问题": "产品天天成长B款在所有业务日期的持仓规模累计值是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=天天成长B款,\n指标名=持仓规模,\n数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['天天成长B款'], '数据日期': [], '指标名': ['持仓规模']}", "输出时效": 7.34803891181946, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 234, "用户问题": "组合7天成长4号在20241101到20241110的持仓规模的移动平均值（窗口大小为 3）是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=7天成长4号,\n指标名=持仓规模,\n数据日期=20241101-20241110", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20241101-20241110'], '指标名': ['持仓规模'], '组合简称': ['7天成长4号']}", "输出时效": 9.97095847129822, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 250, "用户问题": "产品天天成长3号1期,7天成长4号,至顺5号95期,在已到期人民币产品,不同产品系列和组合下的理财产品的持仓份额和持仓客户数分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=产品天天成长3号1期,7天成长4号,至顺5号95期, \n币种含义=人民币（CNY）,\n产品状态含义=已到期,  \n指标名=持仓份额,  \n指标名=持仓客户数  ", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品系列': [], '产品状态': ['已到期'], '指标名': ['持仓客户数', '持仓份额'], '组合简称': ['7天成长4号', '至顺5号95期', '天天成长3号1期']}", "输出时效": 20.606041431427, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 251, "用户问题": "产品新启航一年定开24号在零售渠道下，收益类型为净值时，持仓客户数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=新启航一年定开24号,  \n渠道名称=零售,  \n收益类型含义=净值,  \n指标名=持仓客户数 ", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['净值'], '产品简称': ['新启航一年定开24号'], '渠道一级分类': ['零售'], '指标名': ['持仓客户数']}", "输出时效": 15.5811252593994, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 237, "用户问题": "20240201至20240225，不同渠道的持仓规模总和的排名是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n指标名=持仓规模 ,\n数据日期=20240201-20240225", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20240201-20240225'], '指标名': ['持仓规模']}", "输出时效": 8.98769688606262, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 238, "用户问题": "天天成长B款在零售渠道下，昨天的持仓规模总和占总规模的比例是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=天天成长B款 ,\n渠道名称=零售,\n指标名=持仓规模,\n数据日期：", "Unnamed: 6": "", "输出key+value": "{'数据日期': [], '渠道一级分类': ['零售'], '指标名': ['持仓规模'], '组合简称': ['天天成长B款']}", "输出时效": 10.1244332790375, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 239, "用户问题": "组合天天成长B款在20240201到20240210每日的持仓规模累计增长率是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=天天成长B款,\n币种含义=人民币（CNY）,\n指标名=持仓规模,\n数据日期=20240201-20240210", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20240201-20240210'], '指标名': ['持仓规模'], '组合简称': ['天天成长B款']}", "输出时效": 9.44916415214539, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 240, "用户问题": "2025年02月01日至2025年02月24日，各产品系列的持仓客户数变化趋势如何？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=  ,\n指标名=持仓客户数,\n数据日期: 20250201-20250224", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250201-20250224'], '产品系列': [], '指标名': ['持仓客户数']}", "输出时效": 9.98369073867798, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 241, "用户问题": "2025年02月24日，人民币R3 级产品，其业绩基准和计息基础分别是什么情况？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "币种含义=人民币（CNY）,\n产品风险评级含义=R3级(中等风险),\n组合名称= ,\n数据日期:20250224,\n业绩基准= ,\n计息基础= ", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R3级(中等风险)']}", "输出时效": 8.29012751579285, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 242, "用户问题": "2025年02月01日至2025年02月25日，对公渠道下净值型产品天天成长B款的日均持仓规模是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=对公,\n收益类型含义=净值,\n组合名称=天天成长B款,\n数据日期:20250201-02050225\n指标名= 日均持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['净值'], '数据日期': ['20250201-20250225'], '渠道一级分类': ['对公'], '指标名': ['持仓规模'], '组合简称': ['天天成长B款']}", "输出时效": 12.5740833282471, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 243, "用户问题": "2025年02月24日，产品启航成长一年定开40号，不同开放类型含义下，上一开放日和下一开放日分别是什么时候？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=启航成长一年定开40号,\n数据日期:20250224,\n开放类型含义= ,\n上一开放日= ,\n下一开放日=", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['启航成长一年定开40号'], '开放类型': ['开放式']}", "输出时效": 11.5576627254486, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 244, "用户问题": "零售渠道下，净值型的私募产品产品有多少个？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,\n收益类型含义=净值,\n产品简称= ,\n募集方式含义=私募", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['净值'], '募集方式': ['私募'], '产品简称': [], '渠道一级分类': ['零售']}", "输出时效": 6.37980771064758, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 245, "用户问题": "零售渠道下，中低风险的产品的业绩基准和估值方法是什么？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,\n产品风险评级含义=R2级(中低风险),\n产品简称= ,\n估值方法含义= ,\n业绩基准= ,", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R2级(中低风险)'], '产品简称': [], '估值时效': [], '渠道一级分类': ['零售']}", "输出时效": 8.10778570175171, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 246, "用户问题": "零售和对公渠道下，成立成功的人民币净值型产品，持仓份额和持仓客户数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,对公,\n币种含义=人民币（CNY）,\n收益类型含义=净值,\n产品状态含义=成立成功,\n指标名=持仓份额,\n指标名=指标客户数", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['净值'], '产品状态': ['成立成功'], '渠道一级分类': ['对公', '零售'], '指标名': ['持仓客户数', '持仓份额']}", "输出时效": 15.7704734802246, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 247, "用户问题": "同业机构渠道下，产品系列、产品代码和开放类型含义组合下，理财产品的募集开始日和成立日之间的时间差是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称= ,\n渠道名称=同业机构,\n产品系列= ,\n产品简称= ,\n开放类型含义= ,\n募集开始日= ,\n成立日= ", "Unnamed: 6": "", "输出key+value": "{'产品代码': [], '组合名称': [], '开放类型': [], '产品简称': [], '成立日': [], '渠道二级分类': ['同业机构'], '产品系列': [], '产品状态': ['募集中']}", "输出时效": 11.8385388851166, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 248, "用户问题": "产品至顺5号95期C，人民币中高风险的产品，不同产品系列和组合对应的理财产品的业绩基准和计息基础是什么？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=至顺5号95期C,\n组合名称= 至顺5号95期, \n产品系列= , \n产品风险评级含义=R4级(中高风险), \n币种含义=人民币（CNY）,\n业绩基准= ,\n指标名= ", "Unnamed: 6": "", "输出key+value": "{'组合名称': [], '风险评级': ['R4级(中高风险)'], '产品简称': ['至顺5号95期C'], '产品系列': []}", "输出时效": 12.0242733955383, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 249, "用户问题": "在2025年02月24日，货币型产品的持仓份额为多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期:20250224,\n产品简称= ,\n收益类型含义=货币,  \n指标名=持仓份额  ", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['货币'], '数据日期': ['20250224-20250224'], '指标名': ['持仓份额']}", "输出时效": 35.1320614814758, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 254, "用户问题": "2025年02月24日，组合至顺5号95期，在产品系列、产品和渠道组合下，净值型理财产品的持仓份额和成立日是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=至顺5号95期,  \n数据日期=20250224,\n渠道名称= ,  \n产品系列= ,\n产品简称= ,\n指标名=持仓份额,  \n成立日= ", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品收益类型': ['净值'], '产品简称': [], '成立日': [], '数据日期': ['20250224-20250224'], '产品系列': [], '指标名': ['持仓份额'], '组合简称': ['至顺5号95期']}", "输出时效": 18.3323295116425, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 257, "用户问题": "2025年02月01日，在产品和渠道的组合下，人民币预期收益的理财产品的持仓份额和成立日是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=20250201,\n币种含义=人民币（CNY）,\n渠道名称= ,\n产品简称= ,\n收益类型含义=预期收益,\n持仓份额= ,\n成立日= ", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品收益类型': ['预期收益'], '组合名称': [], '产品简称': [], '成立日': [], '数据日期': ['20250201-20250201'], '指标名': ['持仓份额']}", "输出时效": 37.0726509094238, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 252, "用户问题": "组合代码为启航成长一年定开40号、新启航一年定开24号和天天成长3号1期时，在2025年02月24日零售渠道下净值型产品的募集开始日和募集结束日是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=启航成长一年定开40号,新启航一年定开24号,天天成长3号1期,\n数据日期=20250224,\n渠道名称=零售,  \n收益类型含义=净值,\n募集开始日= ,  \n募集结束日=", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['净值'], '组合代码': [], '产品状态': ['募集中'], '渠道一级分类': ['零售'], '组合简称': ['新启航一年定开24号', '启航成长一年定开40号', '天天成长3号1期']}", "输出时效": 24.2083637714386, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 253, "用户问题": "组合为启航成长一年定开40号，在产品风险评级含义为中低风险且为人民币，不同产品系列和产品代码组合下的理财产品的开放类型含义和上一开放日是什么？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=启航成长一年定开40号, 币种含义=人民币（CNY）,\n产品风险评级含义=R1级(低等风险),开放类型含义= ,  \n上一开放日= ,\n产品系列= ", "Unnamed: 6": "", "输出key+value": "{'产品代码': [], '风险评级': ['R2级(中低风险)'], '开放类型': ['开放式'], '产品系列': [], '组合简称': ['启航成长一年定开40号']}", "输出时效": 22.6415197849274, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 263, "用户问题": "2025年02月24日，渠道、产品系列、组合和产品为维度下，人民币中低风险，且业绩基准大于5%的封闭式理财产品，持仓份额和成立日是什么？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n产品系列= ,\n组合名称= ,\n产品简称= ,\n币种含义=人民币（CNY）,\n产品风险评级含义=R2级(中低风险),\n开放类型含义=封闭式,\n业绩基准=大于5%,\n指标名=持仓份额,\n成立日= ,\n数据日期=20250224  ", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '组合名称': [], '风险评级': ['R2级(中低风险)'], '产品简称': [], '开放类型': ['封闭式'], '成立日': [], '数据日期': ['20250224-20250224'], '产品系列': [], '指标名': ['持仓份额']}", "输出时效": 15.3298208713531, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 255, "用户问题": "组合天天成长B款下人民币货币型产品，不同产品代码对应的理财产品的业绩基准和计息基础是什么？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=天天成长B款,  \n产品简称= ,\n收益类型含义=净值,\n币种含义=人民币（CNY）,\n业绩基准= ,\n计息基础= ", "Unnamed: 6": "", "输出key+value": "{'产品代码': [], '产品收益类型': ['货币'], '组合简称': ['天天成长B款']}", "输出时效": 14.4282376766205, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 256, "用户问题": "2025年02月01日至2025年02月25日，零售渠道下，币种为人民币和美元时，个人客户的已上架理财产品的募集开始日和募集结束日是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "币种含义=人民币（CNY）,美元（USD）, \n数据日期=20250201-20250225, \n渠道名称=零售,  \n客户类型含义=个人,\n产品状态含义=已上架,\n产品简称= , \n募集开始日= ,\n募集结束日=  ", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['个人'], '产品简称': [], '产品状态': ['已上架', '募集中'], '渠道一级分类': ['零售']}", "输出时效": 25.2226872444153, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 270, "用户问题": "2025年02月01日，零售渠道下，天天成长B款和至顺5号95期C产品，其分别为低等风险和中等风险，且分别为公募和私募，业绩基准和持仓份额是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,\n产品简称=天天成长B款,至顺5号95期C,\n产品风险评级含义=R1级(低等风险)和R3级(中等风险),\n募集方式含义=公募,私募,\n指标名=持仓份额,\n业绩基准= ,\n数据日期=20250201", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R1级(低等风险)', 'R3级(中等风险)'], '募集方式': ['私募', '公募'], '产品简称': ['至顺5号95期C'], '数据日期': ['20250201-20250201'], '渠道一级分类': ['零售'], '指标名': ['持仓份额'], '组合简称': ['天天成长B款']}", "输出时效": 33.4987549781799, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 258, "用户问题": "2025年02月24日，零售渠道下的人民币已到期产品，不同组合对应的理财产品的业绩基准和计息基础是什么？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=20250224, \n渠道名称=零售,\n币种含义=人民币（CNY）,\n产品状态含义=已到期,  \n业绩基准= ,\n计息基础= ", "Unnamed: 6": "", "输出key+value": "{'组合名称': [], '产品简称': [], '产品状态': ['已到期'], '渠道一级分类': ['零售']}", "输出时效": 21.5083754062653, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 259, "用户问题": "2025年02月01日至2025年02月25日，组合天天成长B款，在对公和零售渠道下，人民币货币型产品的募集开始日和募集结束日是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=天天成长B款,  \n渠道名称=对公,零售,  \n收益类型含义=货币,\n数据日期=20250201-20250225,\n币种含义=人民币（CNY）,  \n募集开始日= ,\n募集结束日  ", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['货币'], '产品状态': ['募集中'], '渠道一级分类': ['对公', '零售'], '组合简称': ['天天成长B款']}", "输出时效": 24.9621741771698, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 260, "用户问题": "2025年02月24日，零售渠道下的人民币中低风险产品，持仓份额和开放类型含义是什么？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=20250224,  \n渠道名称=零售,  \n币种含义=人民币（CNY）,\n产品风险评级含义=R2级(中低风险),\n产品简称= ,\n指标名=持仓份额,  \n开放类型含义= ", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R2级(中低风险)'], '开放类型': [], '数据日期': ['20250224-20250224'], '渠道一级分类': ['零售'], '指标名': ['持仓份额']}", "输出时效": 12.8403067588806, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 261, "用户问题": "2025年02月24日，零售渠道下，人民币中低风险已到期的绝对收益产品，不同组合代码对应的理财产品的业绩基准和计息基础是什么？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,\n产品简称= , \n币种含义=人民币（CNY）,\n产品风险评级含义=R2级(中低风险),\n产品状态含义=已到期, \n收益类型含义=绝对收益,\n业绩基准= ,\n计息基础= ,\n数据日期=20250224  ", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R2级(中低风险)'], '产品简称': [], '组合代码': [], '产品状态': ['已到期'], '渠道一级分类': ['零售']}", "输出时效": 25.8352601528168, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 262, "用户问题": "2025年02月01日至2025年02月25日，零售渠道下，人民币净值型公募产品天天成长B，募集开始日和募集结束日是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,  \n产品简称=天天成长B,  \n币种含义=人民币（CNY）,\n收益类型含义=净值,\n募集方式含义=公募,\n募集开始日= ,\n募集结束日= ,\n数据日期=20250201-20250225  ", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['净值'], '募集方式': ['公募'], '产品状态': ['募集中'], '渠道一级分类': ['零售'], '组合简称': ['天天成长B款']}", "输出时效": 13.8749577999115, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 272, "用户问题": "2025年02月01日，零售渠道下，中低风险货币型的理财产品，比较人民币和美元产品在持仓规模、持仓客户数和业绩基准方面的不同？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "币种含义=人民币（CNY）,美元（USD）,\n产品简称= ,\n渠道名称=零售,\n产品风险评级含义=R2(中低风险),\n收益类型含义=货币,\n指标名=持仓规模,\n持仓客户数业绩基准= ,\n数据日期=20250201", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['货币'], '风险评级': ['R2级(中低风险)'], '产品简称': [], '数据日期': ['20250201-20250201'], '渠道一级分类': ['零售'], '指标名': ['持仓客户数', '持仓规模']}", "输出时效": 36.4696061611175, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 264, "用户问题": "2025年02月24日，零售渠道下，人民币已上架的固定收益类现金管理理财产品的业绩基准和持仓客户数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,  \n币种含义=人民币（CNY）,\n产品状态含义=已上架,\n是否现金管理类名称 = 是,\n投资大类含义= 固定收益类,\n指标名=持仓客户数,\n业绩基准= , \n数据日期=20250224", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250224-20250224'], '产品状态': ['已上架'], '渠道一级分类': ['零售'], '产品全称': ['平安理财日添利9号现金管理类理财产品'], '指标名': ['持仓客户数']}", "输出时效": 10.5585598945618, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 265, "用户问题": "2025年02月01日至2025年02月25日，产品、组合和渠道维度下，人民币中等风险的定开型中期理财产品的募集开始日和成立日是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n组合名称= ,\n产品简称= ,\n币种含义=人民币（CNY）,\n产品风险评级含义=R3级(中等风险),\n期限分类含义=中期,\n运作模式含义= 定开型,\n募集开始日= ,\n成立日= ,\n数据日期=20250201-20250225  ", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '组合名称': [], '风险评级': ['R3级(中等风险)'], '产品简称': [], '成立日': [], '产品状态': ['募集中'], '运作模式': ['定开型']}", "输出时效": 13.4482989311218, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 266, "用户问题": "20250101-20250331，零售渠道下，人民币成立成功的市价法养老理财产品，业绩基准和持仓客户数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,\n产品简称= ,\n币种含义=人民币（CNY）,\n产品状态含义=成立成功,  \n产品特殊属性=养老理财产品,  \n估值方法含义=市价法, \n指标名=持仓客户数,\n业绩基准= ,\n数据日期=20250101-20250331", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['老理财'], '数据日期': ['20250101-20250331'], '产品状态': ['成立成功'], '渠道一级分类': ['零售'], '指标名': ['持仓客户数']}", "输出时效": 12.5558187961578, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 267, "用户问题": "2025年02月01日，零售渠道下，人民币募集中有产品增信标识的金融性公司产品，且为外部增级，业绩基准和持仓客户数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,\n币种含义=人民币（CNY）,\n产品状态含义=募集中,\n产品增信标识=有,\n产品简称= ,  \n增信机构类型名称=金融性公司,\n增信形式名称=外部增级,  \n业绩基准= ,\n指标名=持仓客户数,  \n数据日期=20250201  ", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250201-20250201'], '产品状态': ['募集中'], '渠道一级分类': ['零售'], '指标名': ['持仓客户数']}", "输出时效": 12.4108858108521, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 268, "用户问题": "2025年2月，渠道、产品和组合维度下，人民币中等风险净值型私募产品，且为3 - 6 个月 (含)最短持有期型，持仓规模和下一开放日是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n组合名称= ,\n产品简称= ,\n币种含义=人民币（CNY）,\n产品风险评级含义=R3级(中等风险),\n收益类型含义=净值,\n运作模式含义=最短持有期型,\n募集方式含义=私募,\n产品期限含义=3-6个月(含),\n指标名=持仓规模,\n下一开放日= ,\n数据日期=20250201-20250228", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品收益类型': ['净值'], '组合名称': [], '风险评级': ['R3级(中等风险)'], '募集方式': ['私募'], '产品简称': [], '开放类型': ['开放式'], '数据日期': ['20250201-20250228'], '产品期限': ['6个月'], '运作模式': ['最短持有期型'], '指标名': ['持仓规模']}", "输出时效": 20.0426909923554, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 269, "用户问题": "2025年02月01日至2025年02月25日，产品系列和渠道维度下，成立成功的人民币产品，比较净值型和货币型的理财产品在募集开始日和募集结束日时长上的区别？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列= ,\n渠道名称= ,\n币种含义=人民币（CNY）,\n产品状态含义=成立成功,\n收益类型含义=净值,货币,\n募集开始日= ,\n募集结束日= ,\n数据日期=20250201-20250225", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品收益类型': ['货币', '净值'], '产品简称': [], '产品系列': [], '产品状态': ['成立成功', '募集中']}", "输出时效": 17.1544992923737, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 277, "用户问题": "所有产品在零售渠道下的持仓份额总和占总持仓份额的比例是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称= ,\n渠道名称=零售,\n指标名=持仓份额总占总持仓份额的比例", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '渠道一级分类': ['零售'], '指标名': ['持仓份额']}", "输出时效": 17.2998678684235, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 271, "用户问题": "2025年第一季度，零售渠道下，中高风险人民币产品，比较开放式和封闭式产品在成立日到到期日之间的时间间隔差异？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,\n币种含义=人民币（CNY）,\n产品简称= ,\n产品风险评级含义=R4级(中高风险),\n开放类型含义=开放式,封闭式,\n指标名=时间间隔,\n数据日期=20250101-20250331", "Unnamed: 6": "", "输出key+value": "{'风险评级': ['R4级(中高风险)'], '产品简称': [], '开放类型': ['开放式', '封闭式'], '成立日': [], '渠道一级分类': ['零售']}", "输出时效": 26.5632979869842, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 280, "用户问题": "零售渠道对应的所有产品的持仓规模标准差是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称= ,\n渠道名称=零售,\n指标名=持仓规模标准差", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '渠道一级分类': ['零售'], '指标名': ['持仓规模']}", "输出时效": 10.3229405879974, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 273, "用户问题": "2025年02月01日，不同产品的持仓份额平均值是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称= ,\n指标名=持仓份额平均值,\n数据日期=20250201", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250201-20250201'], '指标名': ['持仓份额']}", "输出时效": 18.716769695282, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 274, "用户问题": "2025年02月01日，按渠道分组持仓规模的中位数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称= ,\n指标名=持仓规模中位数,\n数据日期：20250201", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250201-20250201'], '指标名': ['持仓规模']}", "输出时效": 16.6943035125732, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 275, "用户问题": "2025年02月01日，持仓规模的最大和持仓规模最小分别对应哪只哪个产品？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "币种含义=人民币（CNY）,\n产品简称= ,\n指标名=持仓规模最大值,最小值,\n数据日期=20250201", "Unnamed: 6": "", "输出key+value": "{'产品代码': [], '数据日期': ['20250201-20250201'], '指标名': ['持仓规模']}", "输出时效": 17.6971709728241, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 276, "用户问题": "2025年02月01日，天天成长产品系列的持仓份额方差是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=天天成长,\n指标名=持仓份额方差,\n数据日期=20250201", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250201-20250201'], '产品系列': ['天天成长'], '指标名': ['持仓份额']}", "输出时效": 39.3505871295929, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 281, "用户问题": "产品新启航一年定开24号在不同渠道下持仓份额的几何平均值是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=新启航一年定开24号,\n渠道名称= ,\n指标名=持仓份额的几何平均值", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': ['新启航一年定开24号'], '指标名': ['持仓份额']}", "输出时效": 11.6930642127991, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 278, "用户问题": "按业务日期排序，零售渠道的持仓规模的一阶差分是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,\n数据日期= ,\n指标名=持仓规模的一阶差分", "Unnamed: 6": "", "输出key+value": "{'数据日期': [], '渠道一级分类': ['零售'], '指标名': ['持仓规模']}", "输出时效": 11.7901203632355, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 279, "用户问题": "不同业务日期下，零售渠道的持仓份额的加权平均值（权重为业务日期的先后顺序）是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期= ,\n渠道名称=零售,\n指标名=持仓份额的加权平均值", "Unnamed: 6": "", "输出key+value": "{'数据日期': [], '渠道一级分类': ['零售'], '指标名': ['持仓份额']}", "输出时效": 11.2926976680756, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 283, "用户问题": "产品7天成长4号C，在不同币种下持仓份额的总和分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=7天成长4号C,\n指标名=持仓份额的总和,\n币种含义=", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['7天成长4号C'], '指标名': ['持仓份额']}", "输出时效": 10.9696025848389, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 284, "用户问题": "产品7天成长4号C的持仓规模与持仓份额的比值（每份额对应的规模）在不同渠道代码下是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=7天成长4号C,\n指标名=持仓规模与持仓份额的比值, 渠道名称=", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['7天成长4号C'], '渠道代码': [], '指标名': ['持仓份额', '持仓规模']}", "输出时效": 15.4199192523956, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 285, "用户问题": "组合天天成长3号1期对应的所有产品的持仓份额之和在总持仓份额中的占比是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=天天成长3号1期,\n产品简称=天天成长3号1期,\n指标名=持仓份额之和的占比  ", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '指标名': ['持仓份额'], '组合简称': ['天天成长3号1期']}", "输出时效": 12.0329756736755, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 289, "用户问题": "人民币的不同产品的持仓份额的调和平均值是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "币种含义=人民币（CNY）,\n产品简称= ,\n指标名=持仓份额的调和平均值 ", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '指标名': ['持仓份额']}", "输出时效": 11.6182882785797, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 290, "用户问题": "人民币产品在在不同渠道下的持仓规模的变异系数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "币种含义=人民币（CNY）,\n产品简称= ,\n渠道名称= ,\n指标名=持仓规模的变异系数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': [], '指标名': ['持仓规模']}", "输出时效": 11.1654031276703, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 293, "用户问题": "天天成长系列产品按渠道分组，持仓规模的众数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=天天成长,\n渠道名称= ,\n指标名=持仓规模的众数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': [], '产品系列': ['天天成长'], '指标名': ['持仓规模']}", "输出时效": 10.6931881904602, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 286, "用户问题": "组合7天成长4号，按业务日期计算持仓规模的同比增长率是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=7天成长4号,\n指标名=持仓规模的同比增长率,\n数据日期=", "Unnamed: 6": "", "输出key+value": "{'数据日期': [], '指标名': ['持仓规模'], '组合简称': ['7天成长4号']}", "输出时效": 11.3484897613525, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 287, "用户问题": "组合天天成长B款，在不同渠道下持仓规模的极差是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=天天成长B款,\n指标名=持仓规模的极差,\n渠道名称=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '指标名': ['持仓规模'], '组合简称': ['天天成长B款']}", "输出时效": 12.1592030525208, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 288, "用户问题": "组合天天成长B款，持仓份额在该产品系列中的分布比例是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=天天成长B款,\n产品系列= , \n指标名=持仓份额的分布比例", "Unnamed: 6": "", "输出key+value": "{'产品系列': [], '指标名': ['持仓份额'], '组合简称': ['天天成长B款']}", "输出时效": 12.3858289718628, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 294, "用户问题": "天天成长系列产品在不同业务日期下，持仓规模与持仓份额的乘积总和是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=天天成长,\n指标名=持仓规模与持仓份额的乘积总和, \n数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': [], '产品系列': ['天天成长'], '指标名': ['持仓份额', '持仓规模']}", "输出时效": 13.3249356746674, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 295, "用户问题": "天天成长系列产品持仓份额在不同币种间的分布方差是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=天天成长,\n指标名=持仓份额的分布方差,\n币种含义=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品系列': ['天天成长'], '指标名': ['持仓份额']}", "输出时效": 10.7276847362518, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 296, "用户问题": "天天成长系列产品的持仓规模在不同组合下的加权标准差（权重为组合代码下的产品数量）是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=天天成长,\n指标名=持仓规模的加权标准差, \n组合名称=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '组合代码': [], '产品系列': ['天天成长'], '指标名': ['持仓规模']}", "输出时效": 13.261833190918, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 297, "用户问题": "2025年02月01日至2025年02月25日，在渠道和产品组合下，持仓份额的加权总和（权重为产品对应的市场热度）是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=20250201-20250225,\n产品简称= ,\n渠道名称= ,\n指标名=持仓份额的加权总和", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '组合名称': [], '产品简称': [], '数据日期': ['20250201-20250225'], '指标名': ['持仓份额']}", "输出时效": 20.4715411663055, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 304, "用户问题": "7天成长4号C在华夏银行渠道下，持仓规模与持仓份额的相关系数是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=7天成长4号C,\n渠道名称=华夏银行,\n指标名=持仓规模与持仓份额相关系数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['华夏银行'], '产品简称': ['7天成长4号C'], '指标名': ['持仓份额', '持仓规模']}", "输出时效": 16.2085945606232, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 309, "用户问题": "组合7天成长4号人民币产品，不同渠道对应的持仓份额的平均值是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=7天成长4号,\n币种含义=人民币（CNY）,\n渠道名称= ,\n指标名=持仓份额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': [], '指标名': ['持仓份额'], '组合简称': ['7天成长4号']}", "输出时效": 13.5258774757385, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 311, "用户问题": "组合7天成长4号C人民币产品在不同产品代码下，持仓份额的总和是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=7天成长4号C,\n币种含义=人民币（CNY）,\n指标名=持仓份额的总和", "Unnamed: 6": "", "输出key+value": "{'产品代码': [], '组合名称': [], '产品简称': ['7天成长4号C'], '指标名': ['持仓份额']}", "输出时效": 14.1035659313202, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 314, "用户问题": "我司封闭类产品的持仓规模是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品管理人=平安理财,\n开放类型=封闭式,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '开放类型': ['封闭式'], '指标名': ['持仓规模']}", "输出时效": 11.3986387252808, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 316, "用户问题": "在国股行上架的产品中，持仓规模前5的产品分别是什么系列？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道二级分类=代销-国股行,\n产品系列=,\n产品简称= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '渠道二级分类': ['代销-国股行'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 15.5525887012482, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 298, "用户问题": "2025年02月01日至2025年02月25日，按渠道和组合分组，持仓规模的平均值是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=20250201-20250225,\n渠道名称= ,\n组合名称= ,\n指标名=持仓规模的平均值 ", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '组合名称': [], '数据日期': ['20250201-20250225'], '指标名': ['持仓规模']}", "输出时效": 17.8429386615753, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 299, "用户问题": "2025年02月01日至2025年02月25日，渠道与币种组合下，持仓份额的占比情况如何？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=20250201-20250225,\n渠道名称= ,\n币种含义= ,\n指标名=持仓份额的占比 ", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '组合名称': [], '数据日期': ['20250201-20250225'], '指标名': ['持仓份额']}", "输出时效": 18.322646856308, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 300, "用户问题": "2025年02月01日至2025年02月25日，在渠道和产品系列纬度下持仓规模的增长趋势如何（用线性回归拟合）？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=20250201-20250225,\n渠道名称= ,\n产品系列= ,\n指标名=持仓规模的增长趋势", "Unnamed: 6": "", "输出key+value": "{'组合名称': [], '渠道代码': [], '数据日期': ['20250201-20250225'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 18.0165557861328, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 301, "用户问题": "组合新启航一年定开24号在对公渠道下，不同产品对应的持仓份额的总和分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合名称=新启航一年定开24号,\n渠道名称=对公,\n产品简称= ,\n指标名=持仓份额的总和", "Unnamed: 6": "", "输出key+value": "{'渠道一级分类': ['对公'], '指标名': ['持仓份额'], '组合简称': ['新启航一年定开24号']}", "输出时效": 15.757003068924, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 318, "用户问题": "固收产品持仓规模排名前10的产品分别为什么系列？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类=固收, \n产品系列= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品分类': ['固收+'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 14.51496052742, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 303, "用户问题": "启航成长一年定开40号在不同渠道下，持仓份额的比例是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=启航成长一年定开40号,\n渠道名称= ,\n指标名=持仓份额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '指标名': ['持仓份额'], '组合简称': ['启航成长一年定开40号']}", "输出时效": 12.6131706237793, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 321, "用户问题": "排除现金产品，我司产品规模前5的系列是哪些？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类!= 现金类 ,\n产品管理人=平安理财 , \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品分类': ['现金类'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 7.89140677452087, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 305, "用户问题": "天天成长B款在各个业务日期的持仓规模的指数加权移动平均值是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=天天成长B款,\n渠道名称= ,\n指标名=持仓规模的指数加权移动平均值,\n数据日期= ", "Unnamed: 6": "", "输出key+value": "{'数据日期': [], '指标名': ['持仓规模'], '组合简称': ['天天成长B款']}", "输出时效": 12.2259650230408, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 306, "用户问题": "天天成长B款按业务日期排序，持仓规模的变化率是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=天天成长B款,\n指标名=持仓规模的变化率,\n数据日期=", "Unnamed: 6": "", "输出key+value": "{'数据日期': [], '指标名': ['持仓规模'], '组合简称': ['天天成长B款']}", "输出时效": 12.309344291687, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 307, "用户问题": "新启航一年定开24号在不同币种下，持仓规模的分布情况如何（通过分位数分析）？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=新启航一年定开24号,\n币种含义= ,\n指标名=持仓规模的分布情况", "Unnamed: 6": "", "输出key+value": "{'指标名': ['持仓规模'], '组合简称': ['新启航一年定开24号']}", "输出时效": 15.7274401187897, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 308, "用户问题": "新启航一年定开24号持仓份额在不同产品系列中的分配比例是怎样的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=新启航一年定开24号,\n产品系列= ,\n指标名=持仓份额", "Unnamed: 6": "", "输出key+value": "{'产品系列': [], '指标名': ['持仓份额'], '组合简称': ['新启航一年定开24号']}", "输出时效": 14.2977812290192, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 322, "用户问题": "招银渠道，规模前五的产品的投资期限都是多久？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=招商银行 , \n产品期限= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': [], '产品期限': [], '指标名': ['持仓规模']}", "输出时效": 8.05685901641846, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 324, "用户问题": "封闭类产品，不同封闭期限的持仓规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "开放类型=封闭式 , \n产品期限= , \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日封闭'], '产品简称': [], '开放类型': ['封闭式'], '产品期限': [], '指标名': ['持仓规模']}", "输出时效": 8.44121050834656, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 326, "用户问题": "期限超过1年的产品，不同系列的持仓规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品期限>1Y,\n产品系列= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品期限': [], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 7.42773866653442, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 312, "用户问题": "新启航系列不同的产品期限产品的规模分别为多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=新启航,\n产品期限= , \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品期限': [], '产品系列': ['新启航'], '指标名': ['持仓规模']}", "输出时效": 12.490128993988, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 313, "用户问题": "我司私募产品分不同系列的持仓规模分别为多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品管理人=平安理财,\n募集方式=私募,\n产品系列= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'募集方式': ['私募'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 12.7772419452667, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 328, "用户问题": "启航增强产品持仓规模前10的产品分别是什么？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=启航增强,\n产品简称= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品系列': ['启航增强'], '指标名': ['持仓规模']}", "输出时效": 3.7171037197113, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 330, "用户问题": "总行零售渠道，产品持仓规模前5分别是什么系列？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道一级分类=零售 , \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品系列': [], '渠道一级分类': ['零售'], '指标名': ['持仓规模']}", "输出时效": 4.42610335350037, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 337, "用户问题": "新启航系列产品在不同渠道的规模是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=新启航 , \n渠道名称= , \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': [], '产品系列': ['新启航'], '指标名': ['持仓规模']}", "输出时效": 3.22946834564209, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 317, "用户问题": "固收+产品在不同渠道的持仓规模分别为多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类=固收+ ,\n渠道名称= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品分类': ['固收+'], '指标名': ['持仓规模']}", "输出时效": 12.8748862743378, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 338, "用户问题": "今年发行的不同系列的产品持仓规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "成立日=20250101-至今 , \n产品系列=  ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '成立日': ['20250101-20251231'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 3.47076678276062, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 319, "用户问题": "不同的风险等级产品持仓规模是如何分布的？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "风险评级= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'风险评级': [], '指标名': ['持仓规模']}", "输出时效": 7.06904292106628, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 320, "用户问题": "同时面向机构客户、零售客户销售的持仓规模排名前10是什么产品？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "客户类型=个人,机构 ,\n指标名=持仓规模,客户类型数", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '渠道一级分类': ['零售'], '指标名': ['持仓规模']}", "输出时效": 8.87318539619446, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 339, "用户问题": "对比去年末，我司产品规模增长了多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=20250325、20241231 , \n产品管理人=平安理财 ,\n指标名=持仓规模的变动", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '指标名': ['持仓规模']}", "输出时效": 3.76051664352417, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 340, "用户问题": "对比去年末，不同系列的产品分别增长了多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=20250325、20241231 ,\n产品系列= ,\n指标名=持仓规模的变动", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品系列': []}", "输出时效": 3.7450578212738, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 323, "用户问题": "排除总行渠道，哪个渠道的规模最大？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道一级分类!=零售,对公,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '指标名': ['持仓规模']}", "输出时效": 7.41887450218201, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 342, "用户问题": "新启航系列不同开发频率的产品持仓规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=新启航 , \n产品开放频率= , \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品系列': ['新启航'], '指标名': ['持仓规模']}", "输出时效": 3.97874855995178, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 325, "用户问题": "个人客户，不同渠道的客户平均持仓规模是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "客户类型=个人 , \n渠道名称= ,  \n指标名=客户平均持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '客户类型': ['个人'], '指标名': ['持仓规模']}", "输出时效": 8.62295508384705, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 343, "用户问题": "中风险的产品，在不同渠道的规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "风险评级=中风险 , \n渠道名称= ,  \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '风险评级': ['R3级(中等风险)'], '产品简称': [], '指标名': ['持仓规模']}", "输出时效": 3.47003126144409, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 327, "用户问题": "不同的产品期限，持仓规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品期限= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品期限': [], '指标名': ['持仓规模']}", "输出时效": 3.15381121635437, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 344, "用户问题": "招银渠道，持仓规模排名前五的产品分别是什么系列？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=招商银行 , \n产品系列= ,  \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': [], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 4.28820180892944, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 329, "用户问题": "直销渠道各系列产品的规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=直销 , \n产品系列= , \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['直销'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 3.39914727210998, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 346, "用户问题": "在招银渠道、总行渠道共架的产品，持仓规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=零售,对公，招商银行,    指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': [], '指标名': ['持仓规模']}", "输出时效": 3.92033290863037, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 331, "用户问题": "固收+产品在不同渠道的规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类=固收+ , \n渠道名称= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品分类': ['固收+'], '指标名': ['持仓规模']}", "输出时效": 3.45433163642883, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 332, "用户问题": "新启航一年定开产品的持仓规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合简称=新启航一年定开,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品系列': ['新启航'], '运作模式': ['定开型'], '指标名': ['持仓规模']}", "输出时效": 3.45108103752136, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 333, "用户问题": "固收+产品的规模对比年初增长了多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类=固收+,  \n数据日期=20250101、20250325,\n指标名=持仓规模的变动", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固收+'], '指标名': ['持仓规模']}", "输出时效": 3.66856479644775, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 334, "用户问题": "固收+产品的规模对比上月同期增长了多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类=固收+ ,\n数据日期=20250225、20250325 ,\n指标名=持仓规模的变动", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固收+'], '指标名': ['持仓规模']}", "输出时效": 3.50656414031982, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 335, "用户问题": "现金类产品的持仓规模分别为多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类=现金类 , \n产品简称= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['现金类'], '指标名': ['持仓规模']}", "输出时效": 3.31128549575806, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 336, "用户问题": "机构客户的持仓规模是多少？对比上日、上月、去年增长了多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "客户类型=机构 , \n数据日期=20250325、20250228、20241231 ,\n指标名=持仓规模、持仓规模变动", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['机构'], '数据日期': ['20241231-20250401'], '指标名': ['持仓规模']}", "输出时效": 4.47065758705139, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 347, "用户问题": "含权产品，规模排名前10的都是什么系列？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类=固收+、混合、权益 ,\n产品系列=  ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 3.60796999931335, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 349, "用户问题": "7天成长产品各子份额的持仓规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "组合简称=7天成长, \n产品简称= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '指标名': ['持仓规模'], '组合简称': ['7天成长']}", "输出时效": 3.49464797973633, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 351, "用户问题": "产品类客户持仓规模排名前5的产品是哪些？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "客户类型=产品,\n产品简称= , \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['产品'], '产品简称': [], '指标名': ['持仓规模']}", "输出时效": 3.40503549575806, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 352, "用户问题": "按季度开放的产品跟按月开放的产品持仓规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品开放频率=每月开放、季度开放,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['季度开放'], '产品简称': [], '开放类型': ['开放式'], '指标名': ['持仓规模']}", "输出时效": 3.61395311355591, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 353, "用户问题": "不同分类的产品持仓规模占比分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品大类=  ,\n指标名=持仓规模占比", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品分类': [], '指标名': ['持仓份额']}", "输出时效": 3.50956511497498, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 357, "用户问题": "招银渠道持仓客户数前5的产品分别是什么系列的产品？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=招商银行 , \n产品系列=  , \n指标名=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': [], '产品系列': [], '指标名': ['持仓客户数']}", "输出时效": 4.24862122535706, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 358, "用户问题": "招银渠道持仓客户数前5的产品对应的投资期限分别是多久？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=招商银行 , \n产品期限=  ,\n指标名=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': [], '产品期限': [], '指标名': ['持仓客户数']}", "输出时效": 4.02961993217468, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 359, "用户问题": "封闭日开的产品规模波动前五的分别是什么产品？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "开放类型=封闭式 , \n产品开放频率=每日开放 , \n产品简称= ,\n指标名=持仓规模波动率", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日封闭'], '产品简称': [], '产品期限': ['日开'], '指标名': ['持仓规模']}", "输出时效": 3.35450196266174, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 345, "用户问题": "招银渠道，近半年的持仓规模波动率是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=招商银行 , \n数据日期=20250325、20240925,\n指标名=持仓规模波动率", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20241110-20250512'], '指标名': ['持仓规模']}", "输出时效": 3.88434886932373, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 391, "用户问题": "2025年3月20日平安理财灵活成长添利日开30天持有3号固收类理财产品产品申购金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长添利日开30天持有3号固收类理财产品 A指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'组合名称': ['平安理财灵活成长添利日开30天持有3号固收类理财产品'], '产品简称': [], '数据日期': ['20250320-20250320'], '指标名': ['申购金额']}", "输出时效": 21.050961971283, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 405, "用户问题": "2025年3月20日平安理财灵活成长添利日开30天持有3号固收类理财产品产品赎回金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长添利日开30天持有3号固收类理财产品 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'组合名称': ['平安理财灵活成长添利日开30天持有3号固收类理财产品'], '产品简称': [], '数据日期': ['20250320-20250320'], '指标名': ['赎回金额']}", "输出时效": 15.6233503818512, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 348, "用户问题": "对比去年末，不同渠道的规模分别增长了多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=20250325、20241231 , \n渠道名称=  , \n指标名=持仓规模的变动", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '指标名': ['持仓规模']}", "输出时效": 3.09282851219177, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 428, "用户问题": "T0产品持仓规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "估值时效含义=T+0 , \n产品简称= , \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '估值时效': ['T+0'], '指标名': ['持仓规模']}", "输出时效": 9.35804319381714, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 350, "用户问题": "我司产品，零售客户平均持仓规模最大的产品是哪些？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品管理人=平安理财 , \n客户类型=个人, \n产品简称= ,\n指标名=平均持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道一级分类': ['零售'], '指标名': ['持仓规模']}", "输出时效": 3.57685875892639, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 429, "用户问题": "T2产品持仓规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "估值时效含义=T+2 , \n产品简称= , \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '估值时效': ['T+2'], '指标名': ['持仓规模']}", "输出时效": 8.90473580360413, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 430, "用户问题": "T0产品持仓规模同比增长了多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "估值时效含义=T+0 , \n产品简称= , \n指标名=持仓规模的变动", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '估值时效': ['T+0'], '指标名': ['持仓规模']}", "输出时效": 10.3153729438782, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 431, "用户问题": "T0产品在各渠道的持仓规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "估值时效含义=T+0 , \n产品简称= , \n渠道名称= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': [], '估值时效': ['T+0'], '指标名': ['持仓规模']}", "输出时效": 8.93528962135315, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 354, "用户问题": "新启航系列在中银渠道的规模是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品系列=新启航 ,\n渠道名称=中国银行 , \n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['中国银行'], '产品系列': ['新启航'], '指标名': ['持仓规模']}", "输出时效": 3.43191623687744, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 355, "用户问题": "对比前一天，规模跌幅前5的渠道分别是哪些渠道？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "数据日期=20250324-20250325 , \n渠道名称= ,  \n指标名=持仓规模涨跌幅", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '指标名': ['持仓规模']}", "输出时效": 3.14930152893066, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 356, "用户问题": "今日持仓规模占总规模比例最大的前5个渠道是哪些？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "渠道名称=  ,  \n指标名=持仓规模占比", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250512-20250512'], '指标名': ['持仓规模']}", "输出时效": 3.67085528373718, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 432, "用户问题": "公司都有哪些风险级别的产品？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品简称= , \n风险评级=", "Unnamed: 6": "", "输出key+value": "{'风险评级': [], '产品简称': []}", "输出时效": 10.2269146442413, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 435, "用户问题": "存续产品规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品状态含义=成立成功 ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '指标名': ['持仓规模']}", "输出时效": 8.84838342666626, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 438, "用户问题": "存续产品客户数", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品状态含义=成立成功 ,\n数据日期=当前日期 ,\n指标名=持仓客户数 ", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '指标名': ['持仓客户数']}", "输出时效": 9.80831432342529, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 360, "用户问题": "封闭日开非现金产品在不同渠道的规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "开放类型=封闭式 , \n产品开放频率=每日开放 , \n产品大类!=现金类 ,\n渠道名称= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': ['每日封闭'], '渠道名称': [], '产品分类': ['现金类'], '产品期限': ['日开'], '指标名': ['持仓规模']}", "输出时效": 4.42464423179626, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 361, "用户问题": "日开非现金类产品不同系列的持仓规模分别是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品开放频率=每日开放 , \n产品大类!=现金类 , \n产品系列= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['现金类'], '产品期限': ['日开'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 3.73307847976685, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 362, "用户问题": "昨天固定收益类产品灵活成长的净申赎金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250319 产品分类=固定收益类 产品系列=灵活成长 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250511-20250511'], '产品系列': ['灵活成长'], '指标名': ['净申赎金额']}", "输出时效": 3.52526211738586, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 363, "用户问题": "3月20日固定收益类产品灵活成长的申购金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 产品系列=灵活成长 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '产品系列': ['灵活成长'], '指标名': ['申购金额']}", "输出时效": 3.98322010040283, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 364, "用户问题": "3月20日混合产品私享的赎回金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=混合 产品系列=私享 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['混合'], '数据日期': ['20250320-20250320'], '产品系列': ['私享'], '指标名': ['赎回金额']}", "输出时效": 4.11132454872131, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 365, "用户问题": "2025年3月20日混合产品净申赎金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=混合 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['混合'], '数据日期': ['20250320-20250320'], '指标名': ['净申赎金额']}", "输出时效": 33.0635421276093, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 366, "用户问题": "2025年3月20日固定收益类产品申购金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['申购金额']}", "输出时效": 34.6418542861938, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 367, "用户问题": "2025年3月20日固定收益类产品赎回金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['赎回金额']}", "输出时效": 8.67679190635681, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 368, "用户问题": "2025年3月20日固定收益类在国股行的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道二级分类=代销-国股行 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['净申赎金额']}", "输出时效": 14.6846251487732, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 369, "用户问题": "2025年3月20日固定收益类在国股行的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道二级分类=代销-国股行 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['申购金额']}", "输出时效": 13.7058370113373, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 370, "用户问题": "2025年3月20日固定收益类在国股行的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道二级分类=代销-国股行 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['赎回金额']}", "输出时效": 14.657796382904, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 371, "用户问题": "2025年3月20日固定收益类在招商银行的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道名称=招商银行 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['净申赎金额']}", "输出时效": 13.8436079025269, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 372, "用户问题": "2025年3月20日固定收益类在招商银行的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道名称=招商银行 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['申购金额']}", "输出时效": 14.4950544834137, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 373, "用户问题": "2025年3月20日固定收益类在招商银行的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道名称=招商银行 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '指标名': ['赎回金额']}", "输出时效": 14.8922135829926, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 374, "用户问题": "2025年3月20日固定收益类在代销的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道一级分类=代销 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['净申赎金额']}", "输出时效": 14.8297414779663, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 375, "用户问题": "2025年3月20日固定收益类在代销的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道一级分类=代销 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['申购金额']}", "输出时效": 14.4249503612518, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 376, "用户问题": "2025年3月20日固定收益类在代销的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品分类=固定收益类 渠道一级分类=代销 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['赎回金额']}", "输出时效": 37.0043578147888, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 377, "用户问题": "2025年3月20日新启航三个月定开1号产品申购金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开1号 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开1号'], '数据日期': ['20250320-20250320'], '指标名': ['申购金额']}", "输出时效": 19.2361664772034, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 378, "用户问题": "2025年3月20日新启航三个月定开1号产品赎回金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开1号 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开1号'], '数据日期': ['20250320-20250320'], '指标名': ['赎回金额']}", "输出时效": 28.0879414081573, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 379, "用户问题": "2025年3月20日新启航三个月定开4号A在国股行的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道二级分类=代销-国股行 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['净申赎金额']}", "输出时效": 21.3672046661377, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 380, "用户问题": "2025年3月20日新启航三个月定开4号A在国股行的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道二级分类=代销-国股行 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['申购金额']}", "输出时效": 20.6650097370148, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 381, "用户问题": "2025年3月20日新启航三个月定开4号A在国股行的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道二级分类=代销-国股行 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['赎回金额']}", "输出时效": 22.1844661235809, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 382, "用户问题": "2025年3月20日新启航三个月定开4号A在招商银行的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道名称=招商银行 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '指标名': ['申购金额']}", "输出时效": 22.1189403533936, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 383, "用户问题": "2025年3月20日新启航三个月定开4号A在代销的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道一级分类=代销 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['申购金额']}", "输出时效": 21.9005117416382, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 384, "用户问题": "2025年3月20日启航创利的申购金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '指标名': ['申购金额']}", "输出时效": 19.5485305786133, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 385, "用户问题": "2025年3月20日启航创利在国股行的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道二级分类=代销-国股行 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '产品系列': ['启航创利'], '指标名': ['申购金额']}", "输出时效": 21.0372993946075, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 386, "用户问题": "2025年3月20日启航创利在招商银行的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道名称=招商银行 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '指标名': ['申购金额']}", "输出时效": 20.8184106349945, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 387, "用户问题": "2025年3月20日启航创利在代销的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道一级分类=代销 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '渠道一级分类': ['代销'], '指标名': ['申购金额']}", "输出时效": 21.4490904808044, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 388, "用户问题": "2025年3月20日国股行渠道的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 渠道二级分类=代销-国股行 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['申购金额']}", "输出时效": 19.5809555053711, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 389, "用户问题": "2025年3月20日招商银行渠道的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 渠道名称=招商银行 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '指标名': ['申购金额']}", "输出时效": 18.9121432304382, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 390, "用户问题": "2025年3月20日代销渠道的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 渠道一级分类=代销 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['申购金额']}", "输出时效": 19.510605096817, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 465, "用户问题": "今天直销渠道固收类产品净申赎总规模为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_appointmentbased_transaction ", "预期key+value": "数据日期=当日销售商名称=直销指标名=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['直销'], '产品简称': [], '产品分类': ['固定收益类'], '数据日期': ['20250512-20250512'], '指标名': ['净申赎金额']}", "输出时效": 8.72732520103455, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 392, "用户问题": "2025年3月20日平安理财灵活成长添利日开30天持有3号固收类理财产品在国股行的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长添利日开30天持有3号固收类理财产品 渠道二级分类=代销-国股行 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'组合名称': ['平安理财灵活成长添利日开30天持有3号固收类理财产品'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['申购金额']}", "输出时效": 22.437059879303, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 393, "用户问题": "2025年3月20日平安理财灵活成长汇稳28天持有固收类理财产品在招商银行的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长汇稳28天持有固收类理财产品 渠道名称=招商银行指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '组合名称': ['平安理财灵活成长汇稳28天持有固收类理财产品'], '数据日期': ['20250320-20250320'], '指标名': ['申购金额']}", "输出时效": 23.7298030853271, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 394, "用户问题": "2025年3月20日平安理财灵活成长汇稳28天持有固收类理财产品在代销的申购金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长汇稳28天持有固收类理财产品 渠道一级分类=代销 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'组合名称': ['平安理财灵活成长汇稳28天持有固收类理财产品'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['申购金额']}", "输出时效": 21.9084632396698, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 395, "用户问题": "2025年3月20日申购金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 指标=申购金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['申购金额']}", "输出时效": 18.1386072635651, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 396, "用户问题": "2025年3月20日新启航三个月定开4号A在招商银行的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道名称=招商银行 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '指标名': ['赎回金额']}", "输出时效": 24.7509422302246, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 397, "用户问题": "2025年3月20日新启航三个月定开4号A在代销的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道一级分类=代销 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['赎回金额']}", "输出时效": 24.2431559562683, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 398, "用户问题": "2025年3月20日启航创利的赎回金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '指标名': ['赎回金额']}", "输出时效": 23.8587999343872, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 399, "用户问题": "2025年3月20日启航创利在国股行的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道二级分类=代销-国股行 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '产品系列': ['启航创利'], '指标名': ['赎回金额']}", "输出时效": 32.3339066505432, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 400, "用户问题": "2025年3月20日启航创利在招商银行的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道名称=招商银行 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '指标名': ['赎回金额']}", "输出时效": 16.8249936103821, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 401, "用户问题": "2025年3月20日启航创利在代销的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道一级分类=代销 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '渠道一级分类': ['代销'], '指标名': ['赎回金额']}", "输出时效": 15.22372174263, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 402, "用户问题": "2025年3月20日国股行渠道的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 渠道二级分类=代销-国股行 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['赎回金额']}", "输出时效": 37.0800063610077, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 403, "用户问题": "2025年3月20日招商银行渠道的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 渠道名称=招商银行 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '指标名': ['赎回金额']}", "输出时效": 15.2549726963043, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 404, "用户问题": "2025年3月20日代销渠道的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 渠道一级分类=代销 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['赎回金额']}", "输出时效": 14.5825135707855, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 473, "用户问题": "招商银行产品总规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品简称= 渠道名称=招商银行", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': [], '指标名': ['持仓规模']}", "输出时效": 3.4192967414856, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 406, "用户问题": "2025年3月20日平安理财灵活成长添利日开30天持有3号固收类理财产品在国股行的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长添利日开30天持有3号固收类理财产品 渠道二级分类=代销-国股行 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'组合名称': ['平安理财灵活成长添利日开30天持有3号固收类理财产品'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['赎回金额']}", "输出时效": 11.9608869552612, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 407, "用户问题": "2025年3月20日平安理财灵活成长汇稳28天持有固收类理财产品在招商银行的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长汇稳28天持有固收类理财产品 渠道名称=招商银行 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '组合名称': ['平安理财灵活成长汇稳28天持有固收类理财产品'], '数据日期': ['20250320-20250320'], '指标名': ['赎回金额']}", "输出时效": 11.6926252841949, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 408, "用户问题": "2025年3月20日平安理财灵活成长汇稳28天持有固收类理财产品在代销的赎回金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长汇稳28天持有固收类理财产品 渠道一级分类=代销指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'组合名称': ['平安理财灵活成长汇稳28天持有固收类理财产品'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['赎回金额']}", "输出时效": 10.0997579097748, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 409, "用户问题": "2025年3月20日赎回金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 指标=赎回金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['赎回金额']}", "输出时效": 6.95736241340637, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 410, "用户问题": "2025年3月20日新启航三个月定开4号A在招商银行的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道名称=招商银行 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '指标名': ['净申赎金额']}", "输出时效": 12.1132214069367, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 411, "用户问题": "2025年3月20日新启航三个月定开4号A在代销的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品简称=新启航三个月定开4号A 渠道一级分类=代销 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航三个月定开4号A'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['净申赎金额']}", "输出时效": 11.2565059661865, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 412, "用户问题": "2025年3月20日启航创利的净申赎金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '指标名': ['净申赎金额']}", "输出时效": 21.810177564621, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 413, "用户问题": "2025年3月20日启航创利在国股行的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道二级分类=代销-国股行 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '产品系列': ['启航创利'], '指标名': ['净申赎金额']}", "输出时效": 11.2521815299988, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 414, "用户问题": "2025年3月20日启航创利在招商银行的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道名称=招商银行 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '指标名': ['净申赎金额']}", "输出时效": 9.14053130149841, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 415, "用户问题": "2025年3月20日启航创利在代销的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 产品系列=启航创利 渠道一级分类=代销 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '产品系列': ['启航创利'], '渠道一级分类': ['代销'], '指标名': ['净申赎金额']}", "输出时效": 34.1824123859406, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 416, "用户问题": "2025年3月20日国股行渠道的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 渠道二级分类=代销-国股行 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['净申赎金额']}", "输出时效": 34.8163225650787, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 417, "用户问题": "2025年3月20日招商银行渠道的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 渠道名称=招商银行 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250320-20250320'], '指标名': ['净申赎金额']}", "输出时效": 12.1365568637848, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 418, "用户问题": "2025年3月20日代销渠道的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 渠道一级分类=代销 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['净申赎金额']}", "输出时效": 12.8766345977783, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 419, "用户问题": "2025年3月20日平安理财灵活成长添利日开30天持有3号固收类理财产品净申赎金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长添利日开30天持有3号固收类理财产品 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'组合名称': ['平安理财灵活成长添利日开30天持有3号固收类理财产品'], '数据日期': ['20250320-20250320'], '指标名': ['净申赎金额']}", "输出时效": 12.6584091186523, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 420, "用户问题": "2025年3月20日平安理财灵活成长添利日开30天持有3号固收类理财产品在国股行的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长添利日开30天持有3号固收类理财产品 渠道二级分类=代销-国股行 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'组合名称': ['平安理财灵活成长添利日开30天持有3号固收类理财产品'], '数据日期': ['20250320-20250320'], '渠道二级分类': ['代销-国股行'], '指标名': ['净申赎金额']}", "输出时效": 14.9785726070404, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 421, "用户问题": "2025年3月20日平安理财灵活成长添利日开30天持有3号固收类理财产品在招商银行的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长添利日开30天持有3号固收类理财产品 渠道名称=招商银行 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '组合名称': ['平安理财灵活成长添利日开30天持有3号固收类理财产品'], '数据日期': ['20250320-20250320'], '指标名': ['净申赎金额']}", "输出时效": 16.6155145168304, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 422, "用户问题": "2025年3月20日平安理财灵活成长汇稳28天持有固收类理财产品在代销的净申赎金额是多少?", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 组合名称=平安理财灵活成长汇稳28天持有固收类理财产品 渠道一级分类=代销 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'组合名称': ['平安理财灵活成长汇稳28天持有固收类理财产品'], '数据日期': ['20250320-20250320'], '渠道一级分类': ['代销'], '指标名': ['净申赎金额']}", "输出时效": 15.8906571865082, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 423, "用户问题": "2025年3月20日净申赎金额是多少？", "案例人员": "", "预期快慢思考": "快", "预期意图agent": "financial_product_appointmentbased_transaction", "预期key+value": "数据日期=20250320 指标=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250320-20250320'], '指标名': ['净申赎金额']}", "输出时效": 11.5032503604889, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 424, "用户问题": "平安理财今天的持仓规模是多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250512-20250512'], '指标名': ['持仓规模']}", "输出时效": 9.39021372795105, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 425, "用户问题": "启航创利稳进六个月1号目前规模多大", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250511-20250511'], '产品系列': ['启航创利稳进'], '指标名': ['持仓规模'], '组合简称': ['平安理财启明1个月1号']}", "输出时效": 10.964962720871, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 426, "用户问题": "平安理财管理规模现在有多少?", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'指标名': ['持仓规模']}", "输出时效": 17.609201669693, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 427, "用户问题": "平安理财近一个月规模变化？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250412-20250512'], '指标名': ['持仓规模']}", "输出时效": 10.3931367397308, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 480, "用户问题": "20241001，所有产品的持仓净值之和", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20241001 产品简称=  渠道名称=", "Unnamed: 6": "", "输出key+value": "{'组合名称': [], '产品简称': [], '数据日期': ['20241001-20241001'], '指标名': ['持仓规模']}", "输出时效": 3.84064340591431, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 488, "用户问题": "20241001，所有产品系列的持仓净值之和", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20241001 产品系列= 渠道名称= 指标名称=持仓规模", "Unnamed: 6": "", "输出key+value": "{'组合名称': [], '产品简称': [], '数据日期': ['20241001-20241001'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 3.92864155769348, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 492, "用户问题": "20240107-20250107平安理财启航成长一年半定开11号的产品持仓份额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓份额 组合名称=安理财-启航成长一年半定开11号人民币理财产品 数据日期=20240107-20250107", "Unnamed: 6": "", "输出key+value": "{'组合名称': ['平安理财-启航成长一年半定开11号人民币理财产品'], '产品简称': [], '数据日期': ['20240107-20250107'], '指标名': ['持仓份额']}", "输出时效": 5.53175044059753, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 499, "用户问题": "每个渠道上哪个现金型产品持仓份额最高？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓份额 渠道名称= 产品分类=现金型", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品收益类型': ['货币'], '产品简称': [], '指标名': ['持仓份额']}", "输出时效": 3.5066864490509, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 503, "用户问题": "昨天天天成长产品的持仓规模是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品系列=天天成长 数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250511-20250511'], '产品系列': ['天天成长'], '指标名': ['持仓规模']}", "输出时效": 3.16262197494507, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 433, "用户问题": "下层投资权益类的产品有哪些", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品简称= , \n投资资产种类=权益类？", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品分类': ['权益']}", "输出时效": 9.60905742645264, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 434, "用户问题": "半年期收益最高的存续产品有哪些", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品简称= , \n产品运作周期含义= 半年 ,\n指标名=最高业绩基准？ , \n产品状态含义=成立成功 , ", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': [], '产品简称': []}", "输出时效": 11.5759863853455, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 515, "用户问题": "这个月招商银行渠道中哪个封闭型产品中持仓份额最高？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓份额 产品简称= 渠道名称=招商银行 开放类型=封闭型 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': [], '开放类型': ['封闭式'], '指标名': ['持仓份额']}", "输出时效": 3.8857569694519, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 436, "用户问题": "截至今日存续产品只数", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品状态含义=成立成功 ,\n数据日期=当前日期？\n指标名=产品数量？", "Unnamed: 6": "", "输出key+value": "{'产品简称': []}", "输出时效": 9.16975998878479, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 437, "用户问题": "封闭式产品存续规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品状态含义=成立成功 ,\n开放类型=封闭式 , \n数据日期=当前日期？\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['封闭式'], '指标名': ['持仓规模']}", "输出时效": 10.1912000179291, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 520, "用户问题": "2024年1月31日，我司管理产品数量", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品简称': []}", "输出时效": 3.98286032676697, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 439, "用户问题": "2025年4月17日，各外部渠道的持仓规模为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417渠道名称= ,指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250417-20250417'], '指标名': ['持仓规模']}", "输出时效": 18.0693325996399, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 440, "用户问题": "2025年4月17日，各产品类型的持仓总规模？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417产品大类= ,指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': [], '数据日期': ['20250417-20250417'], '指标名': ['持仓规模']}", "输出时效": 13.4654812812805, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 441, "用户问题": "2025年4月17日，各产品大类下各产品系列合计持仓总规模？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417产品大类= ,产品系列=指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': [], '数据日期': ['20250417-20250417'], '产品系列': [], '指标名': ['持仓规模']}", "输出时效": 13.3440520763397, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 442, "用户问题": "2025年4月17日，统计每个渠道的持仓总客户数", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417渠道名称= ,指标名=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250417-20250417'], '指标名': ['持仓客户数']}", "输出时效": 14.4097797870636, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 521, "用户问题": "上个月末哪个渠道上全市场封闭型产品的持仓份额最大？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓份额 渠道名称= 产品简称= 开放类型=封闭型 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '产品简称': [], '开放类型': ['封闭式'], '数据日期': ['20250430-20250430'], '指标名': ['持仓份额']}", "输出时效": 3.96708106994629, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 444, "用户问题": "2025年4月17日，持有期产品的持仓份额总计为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417运作模式=持有期（日开、持有期） ,指标名=持仓份额模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250417-20250417'], '运作模式': ['最短持有期型'], '指标名': ['持仓份额']}", "输出时效": 11.4018402099609, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 445, "用户问题": "2025年4月17日，现金类产品的持仓总规模？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417产品大类= 现金类指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['现金类'], '数据日期': ['20250417-20250417'], '指标名': ['持仓规模']}", "输出时效": 11.8832135200501, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 446, "用户问题": "2025年4月17日，机构投资者及个人投资者的持仓总规模？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417客户类型=机构，个人指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'客户类型': ['个人', '机构'], '数据日期': ['20250417-20250417'], '指标名': ['持仓规模']}", "输出时效": 15.8848447799683, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 447, "用户问题": "2025年，各期限固收产品的各月份规模变动是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250101-20250417产品大类=固收指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固收+'], '数据日期': ['20250101-20251231'], '产品期限': [], '指标名': ['持仓规模']}", "输出时效": 12.0793690681458, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 448, "用户问题": "2025年，各月份的外部渠道总规模为多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250101-20250417渠道名称=指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250101-20251231'], '指标名': ['持仓规模']}", "输出时效": 13.5482134819031, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 522, "用户问题": "平安理财最新的新产品规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期= 持仓规模=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '指标名': ['持仓规模']}", "输出时效": 3.47866630554199, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 523, "用户问题": "公司全部理财产品的总规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "持仓规模=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '指标名': ['持仓规模']}", "输出时效": 3.19677495956421, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 451, "用户问题": "2025年4月17日，启航系列的持仓总份额及持仓总规模为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417产品系列=启航指标名=持仓规模，持仓份额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250417-20250417'], '产品系列': ['新启航'], '指标名': ['持仓份额', '持仓规模']}", "输出时效": 15.2642261981964, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 525, "用户问题": "天天成长 产品 客户数", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓客户数 产品系列=天天成长", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品系列': ['天天成长'], '指标名': ['持仓客户数']}", "输出时效": 3.48215126991272, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 453, "用户问题": "2025年4月17日，母行渠道的总客户数为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417渠道名称=平安银行指标名=持仓客户数", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250417-20250417'], '指标名': ['持仓客户数']}", "输出时效": 20.002902507782, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 454, "用户问题": "2025年4月17日，开放式产品总规模为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417开放类型含义=开放式指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['开放式'], '数据日期': ['20250417-20250417'], '指标名': ['持仓规模']}", "输出时效": 13.9186329841614, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 455, "用户问题": "各产品类型的当日规模为多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=当日产品大类=指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': [], '数据日期': ['20250512-20250512'], '指标名': ['持仓规模']}", "输出时效": 10.327517747879, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 456, "用户问题": "各产品类型的当日新增规模为多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=当日，前日产品大类=指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': [], '数据日期': ['20250512-20250512'], '指标名': ['持仓规模']}", "输出时效": 11.4264793395996, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 457, "用户问题": "各渠道的当日规模为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=当日渠道名称=指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250512-20250512'], '指标名': ['持仓规模']}", "输出时效": 9.30173444747925, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 458, "用户问题": "各渠道的相较上日规模新增多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=当日渠道名称=指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '指标名': ['持仓规模']}", "输出时效": 9.97971796989441, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 459, "用户问题": "当日零售渠道的净申赎金额为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_appointmentbased_transaction ", "预期key+value": "数据日期=当日销售商名称=零售指标名=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250512-20250512'], '渠道一级分类': ['零售'], '指标名': ['净申赎金额']}", "输出时效": 11.7092473506928, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 460, "用户问题": "当日净值型产品的净申赎金额为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_appointmentbased_transaction ", "预期key+value": "数据日期=当日收益类型=净值指标名=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['净值'], '数据日期': ['20250512-20250512'], '指标名': ['净申赎金额']}", "输出时效": 11.5427432060242, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 461, "用户问题": "当日货币型产品的净申赎金额为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_appointmentbased_transaction ", "预期key+value": "数据日期=当日收益类型=万份收益指标名=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['货币'], '数据日期': ['20250512-20250512'], '指标名': ['净申赎金额']}", "输出时效": 11.4120671749115, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 462, "用户问题": "当日各渠道销量为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_appointmentbased_transaction ", "预期key+value": "数据日期=当日销售商名称=指标名=净申赎金额，预约申购金额，预约赎回金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250512-20250512'], '指标名': ['申购金额']}", "输出时效": 10.3855650424957, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 526, "用户问题": "安心稳健产品 持仓规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品系列=安心稳健", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '产品系列': ['安心稳健'], '指标名': ['持仓规模']}", "输出时效": 3.21185088157654, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 464, "用户问题": "今天含权类的产品净申赎金额为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_appointmentbased_transaction ", "预期key+value": "数据日期=当日产品大类=含权类指标名=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250512-20250512'], '指标名': ['净申赎金额']}", "输出时效": 10.8693785667419, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 530, "用户问题": "今年来现金型产品规模 占比 全部理财产品规模？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品大类=现金类 数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品收益类型': ['货币'], '产品简称': [], '数据日期': ['20250101-20251231'], '指标名': ['持仓规模']}", "输出时效": 3.80959630012512, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 466, "用户问题": "今天渤海银行的净申赎规模新增了为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_appointmentbased_transaction ", "预期key+value": "数据日期=当日销售商名称=渤海银行指标名=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['渤海银行'], '数据日期': ['20250512-20250512'], '指标名': ['净申赎金额']}", "输出时效": 8.26217269897461, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 467, "用户问题": "今日在途申购份额有多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_appointmentbased_transaction ", "预期key+value": "数据日期=当日指标名=待确认申购份额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250512-20250512'], '指标名': ['申购金额']}", "输出时效": 7.56024408340454, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 468, "用户问题": "今日在途净申赎金额有多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_appointmentbased_transaction ", "预期key+value": "数据日期=当日指标名=待确认净申赎金额", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250512-20250512'], '指标名': ['净申赎金额']}", "输出时效": 8.11781024932861, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 469, "用户问题": "招商银行规模多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "渠道名称=招商银行 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '指标名': ['持仓规模']}", "输出时效": 4.87368965148926, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 470, "用户问题": "2024年02月14日至2025年02月14日平安银行渠道私行新启航第201期一年封闭的持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20240214-20250214 渠道名称=平安银行 产品简称=新启航第201期一年封闭 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['中原银行'], '产品简称': ['私行新启航第201期一年封闭'], '数据日期': ['20240214-20250214'], '指标名': ['持仓规模']}", "输出时效": 6.13257884979248, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 471, "用户问题": "天天成长持仓情况如何", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品系列=天天成长 指标名=持仓份额、持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品系列': ['天天成长'], '指标名': ['持仓份额', '持仓规模']}", "输出时效": 3.05441164970398, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 472, "用户问题": "2024年02月14日至2025年02月14日中国银行渠道天天成长3号B的持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "渠道简称=中国银行 产品简称=天天成长3号B 指标名=持仓份额，持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['中国银行'], '产品简称': ['天天成长3号B'], '数据日期': ['20240214-20250214'], '指标名': ['持仓规模']}", "输出时效": 5.51617383956909, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 541, "用户问题": "我司最新的产品规模是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期= 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '指标名': ['持仓规模']}", "输出时效": 2.74129343032837, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 474, "用户问题": "2024年02月14日至2025年02月14日平安理财天天成长3号B的产品持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品简称=平安理财天天成长3号33期 数据日期=20240214-20250214", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['平安理财天天成长3号33期'], '数据日期': ['20240214-20250214'], '指标名': ['持仓规模']}", "输出时效": 5.20191144943237, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 475, "用户问题": "招商银行天天成长的规模是多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "渠道名称=招商银行 产品系列=天天成长 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品系列': ['天天成长'], '指标名': ['持仓规模']}", "输出时效": 3.41382312774658, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 476, "用户问题": "20231227-20241227平安理财天天成长3号B的产品持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品简称=平安理财天天成长3号33期 数据日期=20231227-20241227", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['平安理财天天成长3号33期'], '数据日期': ['20231227-20241227'], '指标名': ['持仓规模']}", "输出时效": 5.20945429801941, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 477, "用户问题": "2024年01月27日至2025年01月22日平安理财至顺5号30期C的产品持仓份额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓份额 产品简称=至顺5号30期C 数据日期=20240127-20250122", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['至顺5号30期C'], '数据日期': ['20240127-20250122'], '指标名': ['持仓份额']}", "输出时效": 5.42283797264099, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 478, "用户问题": "哪些产品的产品风险等级是R1级？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品简称= 风险评级=R1级(低等风险)", "Unnamed: 6": "", "输出key+value": "{'风险评级': [], '产品简称': []}", "输出时效": 3.1495201587677, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 479, "用户问题": "2024年02月17日至2025年02月17日平安理财天天成长3号B的产品持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品简称=平安理财天天成长3号33期 数据日期=20240217-20250217", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['平安理财天天成长3号33期'], '数据日期': ['20240217-20250217'], '指标名': ['持仓规模']}", "输出时效": 5.22264933586121, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 48, "用户问题": "不同管理费率的产品在最近一个季度的销售份额占比有何差异？", "案例人员": "郝诗源", "预期快慢思考": "", "预期意图agent": "", "预期key+value": "", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250210-20250512'], '指标名': ['持仓份额']}", "输出时效": 8.56831169128418, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 481, "用户问题": "20240106-20250106平安理财天天成长3号B的产品持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "同77类型问题", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['平安理财天天成长3号33期'], '数据日期': ['20240106-20250106'], '指标名': ['持仓规模']}", "输出时效": 5.56123518943787, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 482, "用户问题": "天天成长3号的持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 组合简称=天天成长3号", "Unnamed: 6": "", "输出key+value": "{'指标名': ['持仓规模'], '组合简称': ['天天成长3号']}", "输出时效": 3.37195181846619, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 483, "用户问题": "2024年01月08日至2025年01月08日平安理财至顺宁安2号57期D的产品持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "同77类型问题", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['至顺宁安2号57期D'], '数据日期': ['20240108-20250108'], '指标名': ['持仓规模']}", "输出时效": 5.657559633255, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 484, "用户问题": "2024年01月21日至2025年01月21日平安理财天天成长3号B的产品持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "同77类型问题", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['平安理财天天成长3号33期'], '数据日期': ['20240121-20250121'], '指标名': ['持仓规模']}", "输出时效": 5.62854790687561, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 485, "用户问题": "2024年02月17日至2025年02月17日重庆富民银行渠道卓越成长一年定开17号的持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓份额 渠道名称=重庆富民银行 组合简称=卓越成长一年定开17号 数据日期=20240217-20250217", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['重庆富民银行'], '数据日期': ['20240217-20250217'], '指标名': ['持仓规模'], '组合简称': ['卓越成长一年定开17号']}", "输出时效": 6.05012917518616, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 486, "用户问题": "2024年02月12日至2025年02月12日平安理财天天成长3号B的产品持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "同77类型问题", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['平安理财天天成长3号33期'], '数据日期': ['20240212-20250212'], '指标名': ['持仓规模']}", "输出时效": 5.88981652259827, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 487, "用户问题": "20240112-20250108平安理财天天成长3号B的产品持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "同77类型问题", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['平安理财天天成长3号33期'], '数据日期': ['20240112-20250108'], '指标名': ['持仓规模']}", "输出时效": 5.58455204963684, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 235, "用户问题": "人民币产品的所有业务日期的持仓规模总和是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "币种含义=人民币（CNY）,\n指标名=持仓规模,\n数据日期= ", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': [], '指标名': ['持仓规模']}", "输出时效": 6.0367021560669, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 489, "用户问题": "20240106-20250106中国银行渠道天天成长3号B的持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 渠道名称=中国银行 产品简称=天天成长3号B 数据日期=20240106-20250106", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['中国银行'], '产品简称': ['天天成长3号B'], '数据日期': ['20240106-20250106'], '指标名': ['持仓规模']}", "输出时效": 5.39579701423645, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 490, "用户问题": "请查询2024年10月1日，每个组合的持仓净值总和。", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20241001 组合简称= 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'组合名称': [], '数据日期': ['20241001-20241001'], '指标名': ['持仓规模']}", "输出时效": 4.39095950126648, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 491, "用户问题": "20240107-20250107中国银行渠道天天成长3号B的持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "同120", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['中国银行'], '产品简称': ['天天成长3号B'], '数据日期': ['20240107-20250107'], '指标名': ['持仓规模']}", "输出时效": 5.3936460018158, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 282, "用户问题": "产品新启航一年定开24号在不同业务日期下，持仓规模的环比增长率是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=新启航一年定开24号,\n指标名=持仓规模的环比增长率,\n数据日期= ,", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['新启航一年定开24号'], '数据日期': [], '指标名': ['持仓规模']}", "输出时效": 12.4412171840668, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 493, "用户问题": "天天成长1号的上个月规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 组合简称=天天成长1号 数据日期=", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250401-20250430'], '指标名': ['持仓规模'], '组合简称': ['天天成长1号']}", "输出时效": 3.3824987411499, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 494, "用户问题": "天天成长1号在2024年1月1日的规模。", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 组合简称=天天成长1号 数据日期=20240101", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20240101-20240101'], '指标名': ['持仓规模'], '组合简称': ['天天成长1号']}", "输出时效": 4.24449300765991, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 495, "用户问题": "新卓越稳健策略180天在今年以来的规模变动", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=180天 产品子系列=新卓越稳健 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250101-inf'], '指标名': ['持仓规模'], '组合简称': ['新卓越稳健策略180天持有']}", "输出时效": 3.29579281806946, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 496, "用户问题": "2024年02月24日至2025年02月19日中国银行渠道天天成长3号B的持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品简称=天天成长3号B 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['中国银行'], '产品简称': ['天天成长3号B'], '数据日期': ['20240224-20250219'], '指标名': ['持仓规模']}", "输出时效": 5.25058770179749, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 497, "用户问题": "周五的现金产品规模占整体规模的占比是多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品分类=现金类 数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['现金类'], '数据日期': ['20250516-20250516'], '指标名': ['持仓规模']}", "输出时效": 3.47009420394897, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 498, "用户问题": "昨日平安理财现金类产品整体规模是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品分类=现金类 数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['现金类'], '数据日期': ['20250511-20250511'], '指标名': ['持仓规模']}", "输出时效": 3.6655478477478, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 291, "用户问题": "人民币的所有产品的持仓份额在不同业务日期的变化趋势是怎样的（通过计算趋势斜率）？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "币种含义=人民币（CNY）,\n产品简称= ,\n数据日期= ,\n指标名=持仓份额的变化趋势", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': [], '指标名': ['持仓份额']}", "输出时效": 23.5868787765503, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 500, "用户问题": "昨天平安理财整体规模是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模  数据日期=", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250511-20250511'], '指标名': ['持仓规模']}", "输出时效": 3.45350098609924, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 501, "用户问题": "昨日平安理财固收类产品整体规模是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品分类=固收类 数据日期=", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250511-20250511'], '产品全称': ['平安理财7天成长8号固收类理财产品'], '指标名': ['持仓规模']}", "输出时效": 3.95792984962463, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 502, "用户问题": "昨日哪个产品持仓规模最大？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品简称= 数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250511-20250511'], '指标名': ['持仓规模']}", "输出时效": 3.56089758872986, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 292, "用户问题": "人民币产品的持仓规模与所有币种总持仓规模的比例在不同业务日期如何变化？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "币种含义=人民币（CNY）,\n产品简称= ,\n数据日期= ,\n指标名=持仓规模的比例", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': [], '指标名': ['持仓规模']}", "输出时效": 18.3615610599518, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 302, "用户问题": "7天成长4号C在南京银行渠道下，按业务日期计算持仓规模的二阶差分是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=7天成长4号C,\n渠道名称=南京银行,\n指标名=持仓规模的二阶差分,\n数据日期= ", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['南京银行'], '产品简称': ['7天成长4号C'], '数据日期': [], '指标名': ['持仓规模']}", "输出时效": 15.5170981884003, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 505, "用户问题": "安盈成长B在2024年1月份的持仓规模最大是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品简称=安盈成长B 数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['安盈成长B'], '数据日期': ['20240101-20240131'], '指标名': ['持仓规模']}", "输出时效": 4.34078526496887, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 506, "用户问题": "昨天天天成长3号持仓份额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓份额 组合简称天天成长3号 数据日期=", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250511-20250511'], '指标名': ['持仓份额'], '组合简称': ['天天成长3号']}", "输出时效": 3.22914576530456, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 507, "用户问题": "2024年02月17日至2025年02月17日中国银行渠道天天成长3号B的持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期= 产品简称=天天成长3号B 渠道简称=中国银行", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['中国银行'], '产品简称': ['天天成长3号B'], '数据日期': ['20240217-20250217'], '指标名': ['持仓规模']}", "输出时效": 6.05950522422791, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 508, "用户问题": "安盈成长在今天的总持仓份额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓份额 产品系列=安盈成长 数据日期=", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250512-20250512'], '产品系列': ['安盈成长'], '指标名': ['持仓份额']}", "输出时效": 3.30542135238647, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 509, "用户问题": "昨日招行渠道天天成长1号整体规模是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 渠道名称=招商银行 组合简称=天天成长1号 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '数据日期': ['20250511-20250511'], '指标名': ['持仓规模'], '组合简称': ['天天成长1号']}", "输出时效": 3.54572820663452, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 310, "用户问题": "天天成长B款人民币产品按业务日期计算持仓规模的三阶中心矩是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "产品简称=天天成长B款,\n币种含义=人民币（CNY）,\n数据日期= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['天天成长B款'], '数据日期': [], '指标名': ['持仓规模']}", "输出时效": 12.3655672073364, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 315, "用户问题": "今年一季度我司发行的产品中产品规模前5的产品分别是哪些？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "成立日=20250101-20250331,\n产品简称= ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250101-20250331'], '指标名': ['持仓规模']}", "输出时效": 15.8645777702332, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 512, "用户问题": "2024年1月份，天天成长1号在中国银行渠道持仓规模最大是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 组合简称=天天成长1号 渠道名称=中国银行 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['中国银行'], '数据日期': ['20240101-20240131'], '指标名': ['持仓规模'], '组合简称': ['天天成长1号']}", "输出时效": 5.1738691329956, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 513, "用户问题": "昨天哪个渠道的持仓规模最大？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 渠道名称= 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20250511-20250511'], '指标名': ['持仓规模']}", "输出时效": 3.28779315948486, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 341, "用户问题": "二季度到期的产品持仓规模是多少？", "案例人员": "", "预期快慢思考": "慢", "预期意图agent": "", "预期key+value": "到期日=20250401-20250630 ,\n指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250401-20250630'], '指标名': ['持仓规模']}", "输出时效": 3.84350180625915, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 443, "用户问题": "今年以来，各类产品的日均持仓规模为多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250101-20250417产品大类= ,指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250101-inf'], '指标名': ['持仓规模']}", "输出时效": 11.003725528717, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 516, "用户问题": "昨天天天成长持仓份额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓份额 产品系列=天天成长 数据日期=", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250511-20250511'], '产品系列': ['天天成长'], '指标名': ['持仓份额']}", "输出时效": 3.1939811706543, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 517, "用户问题": "昨日中行渠道天天成长1号整体规模是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 渠道名称= 组合简称=天天成长1号 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['中国银行'], '数据日期': ['20250511-20250511'], '指标名': ['持仓规模'], '组合简称': ['天天成长1号']}", "输出时效": 3.68612909317017, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 518, "用户问题": "平安理财目前资产管理规模多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=  指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'指标名': ['持仓规模']}", "输出时效": 3.28802347183227, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 519, "用户问题": "2024年1月31日，以产品代码统计我司管理产品数量", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期= 产品代码=", "Unnamed: 6": "", "输出key+value": "{'产品代码': []}", "输出时效": 7.95086002349854, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 449, "用户问题": "2025年，相比2024年底，产品总规模上涨了多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417，20241231指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250101-20251231'], '指标名': ['持仓规模']}", "输出时效": 24.1240034103394, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 450, "用户问题": "2025年4月17日，理财子与银行的总产品持仓规模分别为多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417管理人=指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['廊坊银行'], '产品简称': [], '数据日期': ['20250417-20250417'], '指标名': ['持仓规模']}", "输出时效": 15.2050020694733, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 452, "用户问题": "2025年以来，新发产品的总规模有多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期=20250417产品成立日=20250101-20250417指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250101-inf'], '指标名': ['持仓规模']}", "输出时效": 13.5320949554443, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 463, "用户问题": "今天零售渠道上净申赎前五的产品是哪些？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_appointmentbased_transaction ", "预期key+value": "数据日期=当日销售商名称=零售指标名=净申赎金额", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250512-20250512'], '渠道一级分类': ['零售'], '指标名': ['净申赎金额']}", "输出时效": 13.7922310829163, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 504, "用户问题": "昨天招行哪只产品规模涨得最多", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 渠道名称=招商银行 产品简称= 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': [], '数据日期': ['20250511-20250511'], '指标名': ['持仓规模']}", "输出时效": 4.23271131515503, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 510, "用户问题": "10月20日，招行渠道中哪个产品规模最大？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 渠道名称=招商银行 产品简称= 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': [], '数据日期': ['20251020-20251020'], '指标名': ['持仓规模']}", "输出时效": 5.61185669898987, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 511, "用户问题": "昨天持仓规模最大的是哪个产品？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品简称= 数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250511-20250511'], '指标名': ['持仓规模']}", "输出时效": 3.53773784637451, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 527, "用户问题": "启航成长系列的产品开放频率是什么？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品开放频率= 产品系列=启航成长", "Unnamed: 6": "", "输出key+value": "{'产品开放频率': [], '产品系列': ['启航成长']}", "输出时效": 3.10112905502319, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 528, "用户问题": "10月10日渠道规模前三的渠道", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 渠道名称= 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': [], '数据日期': ['20251010-20251010'], '指标名': ['持仓规模']}", "输出时效": 3.80472493171692, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 529, "用户问题": "计数，现在有多少个不同的渠道名称", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "渠道名称= 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': []}", "输出时效": 2.68687462806702, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 514, "用户问题": "今日哪个产品新增持仓规模最大？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品简称= 数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250512-20250512'], '指标名': ['持仓规模']}", "输出时效": 3.20291352272034, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 531, "用户问题": "FBZCM20240926000002该资产当前的资产余额是多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20240926-20240926'], '指标名': ['持仓规模']}", "输出时效": 4.24714875221252, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 532, "用户问题": "2025年1月到期的封闭式产品规模是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期= 开放类型含义=封闭式 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'开放类型': ['封闭式'], '数据日期': ['20250101-20250131'], '指标名': ['持仓规模']}", "输出时效": 4.45188426971435, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 533, "用户问题": "昨天整体规模较上一交易日净增多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 数据日期", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20250511-20250511'], '指标名': ['持仓规模']}", "输出时效": 3.35420441627502, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 534, "用户问题": "2024年7月17日，招行渠道的现金产品有多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "渠道名称=招商银行 产品分类=现金类 指标名=持仓规模", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品分类': ['现金类']}", "输出时效": 5.75792407989502, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 524, "用户问题": "昨日规模涨跌幅主要集中在哪些产品？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品简称= 数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品简称': [], '数据日期': ['20250511-20250511'], '指标名': ['持仓规模']}", "输出时效": 3.34121751785278, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 536, "用户问题": "平安理财的非现金类产品昨天在招商银行渠道规模净增是多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品分类=！现金类 渠道名称=招商银行 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品分类': ['现金类'], '数据日期': ['20250511-20250511'], '指标名': ['持仓规模']}", "输出时效": 4.40663027763367, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 537, "用户问题": "昨天的整体规模比2024年9月26号的整体规模涨了多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 数据日期=", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20240926-20250511'], '指标名': ['持仓规模']}", "输出时效": 4.07511305809021, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 538, "用户问题": "2024年7月17日，现金产品总规模是多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品分类=现金类 数据日期=", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['现金类'], '数据日期': ['20240717-20240717'], '指标名': ['持仓规模']}", "输出时效": 3.50847458839416, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 539, "用户问题": "2024年7月17日，私募产品总规模是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 募集方式=私募 数据日期=", "Unnamed: 6": "", "输出key+value": "{'募集方式': ['私募'], '数据日期': ['20240717-20240717'], '指标名': ['持仓规模']}", "输出时效": 4.25278425216675, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 540, "用户问题": "平安理财有几个渠道有规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 渠道名称=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': []}", "输出时效": 3.183673620224, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 535, "用户问题": "昨天招行规模涨得最多的一只产品是哪只，涨了多少规模", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 产品简称= 数据日期=", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': [], '数据日期': ['20250511-20250511'], '指标名': ['持仓规模']}", "输出时效": 4.92479777336121, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 542, "用户问题": "固收类理财最新规模是多少，较年初有什么变化？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期= 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品分类': ['固定收益类'], '指标名': ['持仓规模']}", "输出时效": 3.55404305458069, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 543, "用户问题": "招商银行渠道有平安理财哪些非现金类的产品", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "产品简称= 渠道名称=招商银行", "Unnamed: 6": "", "输出key+value": "{'渠道名称': ['招商银行'], '产品简称': [], '产品分类': ['现金类']}", "输出时效": 4.23173093795776, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 544, "用户问题": "2024年02月08日至2025年02月08日平安理财天天成长3号B的产品持仓人民币金额是多少？", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "数据日期= 产品简称=平安理财天天成长3号B 指标=持仓规模", "Unnamed: 6": "", "输出key+value": "{'产品简称': ['平安理财天天成长3号33期'], '数据日期': ['20240208-20250208'], '指标名': ['持仓规模']}", "输出时效": 5.89379477500916, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}, {"序号": 545, "用户问题": "昨天的整体规模比2024年9月26号的整体规模多了多少", "案例人员": "", "预期快慢思考": "", "预期意图agent": "financial_product_scale_analytics_agent", "预期key+value": "指标名=持仓规模 数据日期=", "Unnamed: 6": "", "输出key+value": "{'数据日期': ['20240926-20250511'], '指标名': ['持仓规模']}", "输出时效": 4.55901193618774, "key+value是否准确": "", "问题描述": "", "分析": "", "输出意图": ""}]