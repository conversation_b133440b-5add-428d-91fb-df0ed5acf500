"""
安装平安理财ChatBI问答系统所需的依赖
"""

import subprocess
import sys
import os

def install_dependencies():
    """
    安装所需的依赖
    """
    print("正在安装平安理财ChatBI问答系统所需的依赖...")
    
    # 要安装的依赖列表
    dependencies = [
        "pandas",
        "openpyxl",
        "dashscope",
        "flask",
        "requests"
    ]
    
    # 安装依赖
    for dependency in dependencies:
        print(f"正在安装 {dependency}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dependency])
            print(f"{dependency} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"{dependency} 安装失败: {str(e)}")
            return False
    
    print("\n所有依赖安装完成！")
    return True

def create_directories():
    """
    创建必要的目录
    """
    directories = ["templates", "static", "knowledge_bases"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")

def main():
    """
    主函数
    """
    print("=" * 50)
    print("平安理财ChatBI问答系统安装程序")
    print("=" * 50)
    
    # 创建必要的目录
    create_directories()
    
    # 安装依赖
    if install_dependencies():
        print("\n安装完成！您可以通过以下命令启动系统：")
        print("\n1. 命令行界面:")
        print("   python chatbi_main.py")
        print("\n2. 网页界面:")
        print("   python web_app.py")
        print("\n然后在浏览器中访问: http://localhost:5000")
    else:
        print("\n安装过程中出现错误，请检查上面的错误信息。")

if __name__ == "__main__":
    main()
