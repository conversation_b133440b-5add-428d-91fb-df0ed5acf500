"""
测试特定问题的关键词提取和模板匹配
"""

import json
import dashscope
from dashscope import Generation
import os

# 设置API密钥
API_KEY = 'sk-4af3f43ae9a74f0ebed6736b453a47c6'
dashscope.api_key = API_KEY

def load_file(filename):
    """
    加载文件内容
    
    参数:
        filename (str): 文件路径
        
    返回:
        str/dict: 文件内容
    """
    try:
        print(f"正在加载文件: {filename}")
        with open(filename, "r", encoding="utf-8") as f:
            if filename.endswith(".json"):
                content = json.load(f)
            else:
                content = f.read()
        print(f"文件加载成功: {filename}")
        return content
    except Exception as e:
        print(f"加载文件失败: {filename}, 错误: {str(e)}")
        return {} if filename.endswith(".json") else ""

def call_qwen_api(prompt, model="qwen-max", temperature=0.3, max_tokens=500):
    """
    调用千问API
    
    参数:
        prompt (str): 提示文本
        model (str): 模型名称
        temperature (float): 控制输出随机性
        max_tokens (int): 最大生成token数
        
    返回:
        object: 模型响应
    """
    try:
        # 调用千问API
        response = Generation.call(
            model=model,
            prompt=prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            result_format='message'
        )
        
        # 检查响应
        if response.status_code == 200:
            return response
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            print(f"错误信息: {response.code}: {response.message}")
            return None
    
    except Exception as e:
        print(f"API调用出错: {str(e)}")
        return None

def extract_keywords(user_question):
    """
    从用户问题中提取关键词
    
    参数:
        user_question (str): 用户问题
        
    返回:
        dict: 提取的关键词
    """
    print(f"从用户问题中提取关键词: {user_question}")
    
    # 加载提示词模板
    prompt_template = load_file("prompts/extraction_prompt.txt")
    if not prompt_template:
        print("加载提取关键词提示词模板失败")
        return {}
    
    # 填充提示词模板
    prompt = prompt_template.replace("{user_question}", user_question)
    
    # 调用模型
    response = call_qwen_api(prompt)
    
    if not response:
        print("调用模型失败")
        return {}
    
    try:
        # 解析模型返回的JSON
        content = response.output.choices[0].message.content
        print("模型回复:")
        print("-" * 50)
        print(content)
        print("-" * 50)
        
        # 提取JSON部分
        json_start = content.find('{')
        json_end = content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = content[json_start:json_end]
            result = json.loads(json_str)
            print(f"提取的关键词: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return result
        else:
            print("无法从模型响应中提取JSON")
            return {}
    except Exception as e:
        print(f"解析模型响应失败: {str(e)}")
        print(f"模型响应: {response.output.choices[0].message.content}")
        return {}

def match_template(extracted_keywords):
    """
    根据提取的关键词匹配模板
    
    参数:
        extracted_keywords (dict): 提取的关键词
        
    返回:
        str: 匹配的模板
    """
    print("根据提取的关键词匹配模板")
    
    # 加载模板
    templates = load_file("templates.json")
    if not templates:
        print("加载模板失败")
        return ""
    
    # 加载提示词模板
    prompt_template = load_file("prompts/template_matching_prompt.txt")
    if not prompt_template:
        print("加载模板匹配提示词模板失败")
        return ""
    
    # 填充提示词模板
    prompt = prompt_template.replace("{extracted_keywords}", json.dumps(extracted_keywords, ensure_ascii=False))
    prompt = prompt.replace("{templates}", json.dumps(templates, ensure_ascii=False))
    
    # 调用模型
    response = call_qwen_api(prompt)
    
    if not response:
        print("调用模型失败")
        return ""
    
    try:
        # 解析模型返回的JSON
        content = response.output.choices[0].message.content
        print("模型回复:")
        print("-" * 50)
        print(content)
        print("-" * 50)
        
        # 提取JSON部分
        json_start = content.find('{')
        json_end = content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = content[json_start:json_end]
            result = json.loads(json_str)
            matched_template = result.get("matched_template", "")
            confidence = result.get("confidence", 0)
            print(f"匹配的模板: {matched_template}, 置信度: {confidence}")
            return matched_template
        else:
            print("无法从模型响应中提取JSON")
            return ""
    except Exception as e:
        print(f"解析模型响应失败: {str(e)}")
        print(f"模型响应: {response.output.choices[0].message.content}")
        return ""

def fill_template(matched_template, extracted_keywords):
    """
    将提取的关键词填充到模板中
    
    参数:
        matched_template (str): 匹配的模板
        extracted_keywords (dict): 提取的关键词
        
    返回:
        str: 填充后的模板
    """
    print("将提取的关键词填充到模板中")
    
    # 加载提示词模板
    prompt_template = load_file("prompts/template_filling_prompt.txt")
    if not prompt_template:
        print("加载模板填充提示词模板失败")
        return ""
    
    # 填充提示词模板
    prompt = prompt_template.replace("{extracted_keywords}", json.dumps(extracted_keywords, ensure_ascii=False))
    prompt = prompt.replace("{matched_template}", matched_template)
    
    # 调用模型
    response = call_qwen_api(prompt)
    
    if not response:
        print("调用模型失败")
        return ""
    
    try:
        # 获取模型返回的文本
        content = response.output.choices[0].message.content
        print("模型回复:")
        print("-" * 50)
        print(content)
        print("-" * 50)
        
        # 提取填充结果
        result_start = content.rfind("填充结果:") + len("填充结果:")
        result = content[result_start:].strip()
        # 去掉可能的代码块标记
        if result.startswith("```") and result.endswith("```"):
            result = result[3:-3].strip()
        print(f"填充后的模板: {result}")
        return result
    except Exception as e:
        print(f"解析模型响应失败: {str(e)}")
        print(f"模型响应: {response.output.choices[0].message.content}")
        return ""

def test_question(user_question):
    """
    测试特定问题
    
    参数:
        user_question (str): 用户问题
    """
    print(f"测试问题: {user_question}")
    
    # 1. 提取关键词
    extracted_keywords = extract_keywords(user_question)
    if not extracted_keywords:
        print("提取关键词失败")
        return
    
    # 2. 匹配模板
    matched_template = match_template(extracted_keywords)
    if not matched_template:
        print("匹配模板失败")
        return
    
    # 3. 填充模板
    filled_template = fill_template(matched_template, extracted_keywords)
    if not filled_template:
        print("填充模板失败")
        return
    
    # 4. 打印结果
    print("\n" + "=" * 50)
    print(f"用户问题: {user_question}")
    print(f"提取关键词: {json.dumps(extracted_keywords, ensure_ascii=False, indent=2)}")
    print(f"匹配模板: {matched_template}")
    print(f"填充模板: {filled_template}")
    print("=" * 50)

if __name__ == "__main__":
    # 测试特定问题
    test_question("今天招商银行渠道的净申赎金额是多少?")
