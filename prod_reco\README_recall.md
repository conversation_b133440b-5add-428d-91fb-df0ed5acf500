# 产品召回模块

基于BM25算法的产品召回系统，根据用户问题从产品库中召回最相似的产品内容。

## 功能特点

- 🔍 **BM25算法**: 使用经典的BM25算法计算文本相似度
- 🇨🇳 **中文支持**: 内置简单中文分词，无需额外依赖
- 📊 **Excel支持**: 直接读取Excel格式的产品库文件
- ⚡ **高效召回**: 快速返回最相似的产品列表
- 🎯 **灵活配置**: 可自定义返回结果数量

## 核心功能

### `recall_similar_products(user_question, excel_path, top_k=100)`

根据用户问题召回最相似的产品内容。

**参数:**
- `user_question` (str): 用户问题
- `excel_path` (str): 产品库Excel文件路径，默认 "knowledge_bases/产品库.xlsx"
- `top_k` (int): 返回最相似的前k条数据，默认100

**返回:**
- `List[str]`: 最相似的产品内容列表（Excel第二列的值）

## 快速开始

```python
from product_recommender import recall_similar_products

# 基本使用
user_question = "我想要低风险的理财产品"
similar_products = recall_similar_products(
    user_question=user_question,
    excel_path="knowledge_bases/产品库.xlsx",
    top_k=100  # 召回最相似的100个产品
)

print(f"召回的产品数量: {len(similar_products)}")
for i, product in enumerate(similar_products[:5], 1):
    print(f"{i}. {product}")
```

## 数据格式要求

产品库Excel文件需要满足以下要求：

1. **至少包含2列数据**
2. **第二列为产品名称或描述**（用于BM25匹配和召回）
3. **支持中文内容**
4. **文件格式为.xlsx**

示例数据格式：

| 产品代码 | 产品名称 | 产品类型 | 风险等级 |
|---------|---------|---------|---------|
| P001 | 天天成长3号现金管理类理财产品 | 现金管理 | 低风险 |
| P002 | 新启航三个月定开混合类理财产品 | 混合类 | 中等风险 |

## 算法说明

### BM25算法

BM25 (Best Matching 25) 是一种用于信息检索的排序算法，特别适合文本相似度计算。

**公式:**
```
Score(D,Q) = Σ IDF(qi) × (f(qi,D) × (k1 + 1)) / (f(qi,D) + k1 × (1 - b + b × |D| / avgdl))
```

### 中文分词

使用简单的中文分词策略：
- 中文字符按字符分割
- 英文和数字按词分割
- 过滤标点符号

## 文件结构

```
prod_reco/
├── product_recommender.py  # 主要功能模块
├── test_recommender.py     # 测试文件
├── example_usage.py        # 使用示例
└── README_recall.md        # 说明文档
```

## 测试

运行测试文件：

```bash
python test_recommender.py
```

运行使用示例：

```bash
python example_usage.py
```

## 使用示例

```python
# 召回不同数量的产品
questions = [
    "高收益投资产品",
    "基金推荐", 
    "现金管理产品",
    "定期理财",
    "低风险投资"
]

for question in questions:
    # 召回前10个最相似的产品
    results = recall_similar_products(question, "产品库.xlsx", top_k=10)
    print(f"问题: {question}")
    print(f"召回数量: {len(results)}")
    print("前3个结果:")
    for i, product in enumerate(results[:3], 1):
        print(f"  {i}. {product}")
    print()
```

## 性能特点

- **加载速度**: 快速加载Excel文件和训练BM25模型
- **召回速度**: 单次召回通常在毫秒级别完成
- **内存使用**: 轻量级实现，内存占用较小
- **扩展性**: 支持大规模产品库（测试过万级数据）

## 注意事项

1. **Excel文件路径**: 确保Excel文件路径正确
2. **数据质量**: 产品描述越详细，召回效果越好
3. **中文支持**: 内置分词适合一般场景
4. **召回数量**: top_k不能超过实际产品数量
