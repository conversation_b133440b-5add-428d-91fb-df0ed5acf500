# 问题规则匹配模块API文档

## 📋 模块概述

`question_rule_matcher.py` 是一个用于从用户问题中识别和提取规则元素的Python模块。它能够根据规则测试文件，自动匹配用户问题中包含的产品名称、渠道信息、组合信息等实体。

## 🚀 快速开始

### 基本使用

```python
from question_rule_matcher import match_question_simple

# 简单匹配
question = "平安理财启元策略日开270天持有4号固收类理财产品B的规模是多少？"
result = match_question_simple(question)
print(result)
# 输出: {'产品名称': '平安理财启元策略日开270天持有4号固收类理财产品B', '产品系列': '启元策略', ...}
```

### 高级使用

```python
from question_rule_matcher import QuestionRuleMatcher

# 创建匹配器实例
matcher = QuestionRuleMatcher("规则测试.xlsx")

# 详细匹配
result = matcher.match_question_detailed(question)
print(f"匹配率: {result['match_rate']:.1%}")
print(f"匹配项: {result['matches']}")
```

## 📚 API参考

### 类：QuestionRuleMatcher

#### 构造函数

```python
QuestionRuleMatcher(rule_file="规则测试.xlsx", sheet_name=None)
```

**参数：**
- `rule_file` (str): 规则测试文件路径，默认为"规则测试.xlsx"
- `sheet_name` (str): 工作表名称，如果为None则使用第一个工作表

#### 主要方法

##### 1. match_question(question: str) -> Dict[str, str]

从用户问题中匹配规则元素。

**参数：**
- `question` (str): 用户问题

**返回：**
- `Dict[str, str]`: 匹配到的键值对字典

**示例：**
```python
matcher = QuestionRuleMatcher()
result = matcher.match_question("天天成长3号的赎回金额")
# 返回: {'产品简称': '天天成长3号', '产品系列': '天天成长', ...}
```

##### 2. match_question_detailed(question: str) -> Dict

详细匹配用户问题，返回详细信息。

**参数：**
- `question` (str): 用户问题

**返回：**
- `Dict`: 包含以下字段的字典
  - `question`: 原始问题
  - `matches`: 匹配结果
  - `match_count`: 匹配项数量
  - `total_categories`: 总类别数
  - `match_rate`: 匹配率
  - `unmatched_categories`: 未匹配的类别

**示例：**
```python
result = matcher.match_question_detailed("天天成长3号的赎回金额")
print(f"匹配率: {result['match_rate']:.1%}")
print(f"未匹配类别: {result['unmatched_categories']}")
```

##### 3. get_category_values(category: str) -> List[str]

获取指定类别的所有值。

**参数：**
- `category` (str): 类别名称

**返回：**
- `List[str]`: 该类别的所有值

**示例：**
```python
product_series = matcher.get_category_values("产品系列")
print(product_series[:5])  # 显示前5个产品系列
```

##### 4. get_all_categories() -> List[str]

获取所有类别名称。

**返回：**
- `List[str]`: 所有类别名称列表

**示例：**
```python
categories = matcher.get_all_categories()
print(categories)  # ['产品代码', '产品简称', '产品名称', ...]
```

##### 5. search_value_in_category(value: str, category: str = None) -> List[Tuple[str, str]]

在指定类别或所有类别中搜索包含指定值的项。

**参数：**
- `value` (str): 要搜索的值
- `category` (str): 指定类别，如果为None则搜索所有类别

**返回：**
- `List[Tuple[str, str]]`: 匹配的(类别, 值)元组列表

**示例：**
```python
results = matcher.search_value_in_category("天天成长")
for category, value in results:
    print(f"{category}: {value}")
```

##### 6. export_rules_summary() -> Dict

导出规则摘要信息。

**返回：**
- `Dict`: 规则摘要信息

**示例：**
```python
summary = matcher.export_rules_summary()
print(f"总类别数: {summary['total_categories']}")
for category, info in summary['categories'].items():
    print(f"{category}: {info['count']} 个元素")
```

### 函数：match_question_simple

```python
match_question_simple(question: str, rule_file: str = "规则测试.xlsx") -> Dict[str, str]
```

简化的问题匹配函数，直接返回匹配结果。

**参数：**
- `question` (str): 用户问题
- `rule_file` (str): 规则文件路径

**返回：**
- `Dict[str, str]`: 匹配到的键值对字典

## 📊 支持的实体类别

根据规则测试文件，模块支持以下实体类别：

| 类别名称 | 描述 | 元素数量 | 示例 |
|----------|------|----------|------|
| 产品代码 | 产品的唯一标识码 | 6,882 | QXFG14M80A, TC1X00101C |
| 产品简称 | 产品的简化名称 | 6,872 | 启元添利180天持有3号, 天天成长3号 |
| 产品名称 | 产品的完整名称 | 6,879 | 平安理财启元策略日开270天持有4号固收类理财产品B |
| 产品系列 | 产品所属系列 | 48 | 启航成长, 天天成长, 启元策略 |
| 渠道代码 | 销售渠道的代码 | 185 | F49, 933, 020104 |
| 渠道名称 | 销售渠道的名称 | 185 | F51, 913, F03 |
| 组合代码 | 投资组合的代码 | 3,631 | LS5037, QXFG14M055 |
| 组合简称 | 投资组合的简称 | 3,644 | 启元添利日开90天持有9号 |
| 组合名称 | 投资组合的完整名称 | 3,631 | 平安理财新启航第185期一年封闭固收类理财产品 |

## 🔍 匹配算法

### 匹配策略

1. **文本预处理**：去除空格和特殊字符
2. **最长匹配**：优先匹配更长的字符串
3. **精确包含**：检查处理后的问题是否包含规则值
4. **多类别匹配**：同时在所有类别中查找匹配项

### 匹配优先级

- 按字符串长度降序排列，优先匹配更具体的实体
- 例如："平安理财启元策略日开270天持有4号固收类理财产品B" 优先于 "启元策略"

## 📝 使用示例

### 示例1：产品查询

```python
from question_rule_matcher import match_question_simple

question = "平安理财启元策略日开270天持有4号固收类理财产品B的规模是多少？"
result = match_question_simple(question)

print("识别到的实体:")
for entity_type, entity_value in result.items():
    print(f"  {entity_type}: {entity_value}")

# 输出:
# 识别到的实体:
#   产品名称: 平安理财启元策略日开270天持有4号固收类理财产品B
#   产品系列: 启元策略
#   组合简称: 启元策略日开270天持有4号
#   组合名称: 平安理财启元策略日开270天持有4号固收类理财产品
```

### 示例2：批量处理

```python
from question_rule_matcher import QuestionRuleMatcher

questions = [
    "昨天天天成长3号在小招的赎回金额？",
    "新启航三个月定开1号产品净申赎金额是多少？",
    "固定收益类的持仓规模是多少?"
]

matcher = QuestionRuleMatcher()

for question in questions:
    result = matcher.match_question(question)
    print(f"问题: {question}")
    print(f"匹配结果: {result}")
    print()
```

### 示例3：实体搜索

```python
from question_rule_matcher import QuestionRuleMatcher

matcher = QuestionRuleMatcher()

# 搜索包含"天天成长"的所有实体
results = matcher.search_value_in_category("天天成长")

print("包含'天天成长'的实体:")
for category, value in results:
    print(f"  {category}: {value}")
```

## ⚠️ 注意事项

1. **文件格式**：确保规则测试文件为Excel格式(.xlsx)，且包含至少两列
2. **编码问题**：文件应使用UTF-8编码，避免中文乱码
3. **性能考虑**：大规模数据匹配时可能需要较长时间
4. **匹配精度**：基于字符串包含匹配，可能存在误匹配情况
5. **内存使用**：规则文件较大时会占用较多内存

## 🔧 自定义配置

### 修改匹配逻辑

如需自定义匹配逻辑，可以重写以下方法：

```python
class CustomMatcher(QuestionRuleMatcher):
    def preprocess_text(self, text: str) -> str:
        # 自定义文本预处理逻辑
        return super().preprocess_text(text)
    
    def find_longest_match(self, question: str, values: List[str]) -> Optional[str]:
        # 自定义匹配算法
        return super().find_longest_match(question, values)
```

## 📞 技术支持

如遇到问题，请检查：

1. 规则测试文件是否存在且格式正确
2. Python环境是否安装了必要的依赖包（pandas, openpyxl）
3. 文件权限是否足够
4. 查看日志输出了解详细错误信息

---

**版本信息：**
- 模块版本：v1.0
- 最后更新：2025-05-29
- 兼容性：Python 3.6+
