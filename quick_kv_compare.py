"""
快速KV比对脚本 - 简化版
专门用于快速比对预期KV.xlsx文件中的实际输出和预期输出
"""

import pandas as pd
import json
import re
from datetime import datetime

def extract_json_simple(text):
    """简化的JSON提取函数"""
    if pd.isna(text) or not str(text).strip():
        return None

    text = str(text).strip()

    # 直接尝试解析
    try:
        return json.loads(text)
    except:
        pass

    # 查找花括号内容
    match = re.search(r'\{.*\}', text, re.DOTALL)
    if match:
        try:
            return json.loads(match.group(0))
        except:
            pass

    return None

def normalize_for_comparison(obj):
    """标准化对象用于比较"""
    if not isinstance(obj, dict):
        return obj

    result = {}
    for key, value in obj.items():
        # 标准化键名
        norm_key = str(key).strip().lower()

        # 标准化值
        if isinstance(value, list):
            # 排序并去重
            norm_value = sorted(list(set(str(v).strip() for v in value if v and str(v).strip())))
        elif isinstance(value, str):
            norm_value = value.strip()
        else:
            norm_value = value

        result[norm_key] = norm_value

    return result

def compare_json_simple(actual, expected):
    """简化的JSON比较"""
    if actual is None and expected is None:
        return True, "两者都为空"

    if actual is None:
        return False, "实际输出为空"

    if expected is None:
        return False, "预期输出为空"

    # 标准化
    norm_actual = normalize_for_comparison(actual)
    norm_expected = normalize_for_comparison(expected)

    # 比较
    if norm_actual == norm_expected:
        return True, "一致"
    else:
        # 找出差异
        actual_keys = set(norm_actual.keys())
        expected_keys = set(norm_expected.keys())

        differences = []

        # 检查缺失的键
        missing = expected_keys - actual_keys
        if missing:
            differences.append(f"缺少键: {list(missing)}")

        # 检查多余的键
        extra = actual_keys - expected_keys
        if extra:
            differences.append(f"多余键: {list(extra)}")

        # 检查值不同的键
        common_keys = actual_keys & expected_keys
        for key in common_keys:
            if norm_actual[key] != norm_expected[key]:
                differences.append(f"键'{key}'值不同")

        return False, "; ".join(differences) if differences else "值不同"

def quick_compare(excel_file="预期KV.xlsx"):
    """快速比对函数"""
    print(f"🚀 开始快速比对: {excel_file}")

    # 读取Excel
    try:
        df = pd.read_excel(excel_file)
        print(f"✅ 成功读取文件，共 {len(df)} 行")
        print(f"列名: {list(df.columns)}")
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return

    # 自动识别列（假设第2列是实际输出，第3列是预期输出）
    if len(df.columns) >= 3:
        actual_col = df.columns[1]  # 第2列
        expected_col = df.columns[2]  # 第3列
        print(f"📊 使用列: 实际输出='{actual_col}', 预期输出='{expected_col}'")
    else:
        print("❌ 文件列数不足，需要至少3列")
        return

    # 比对
    results = []
    consistent_count = 0

    print("\n🔍 开始比对...")

    for idx, row in df.iterrows():
        actual_text = row[actual_col]
        expected_text = row[expected_col]

        # 提取JSON
        actual_json = extract_json_simple(actual_text)
        expected_json = extract_json_simple(expected_text)

        # 比较
        is_consistent, details = compare_json_simple(actual_json, expected_json)

        result_text = "一致" if is_consistent else "不一致"
        results.append(result_text)

        if is_consistent:
            consistent_count += 1
        else:
            print(f"❌ 第 {idx + 1} 行不一致: {details}")

    # 添加比较结果列
    df['比较结果'] = results

    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"快速比对结果_{timestamp}.xlsx"
    df.to_excel(output_file, index=False)

    # 统计
    total = len(df)
    inconsistent = total - consistent_count
    accuracy = consistent_count / total * 100

    print(f"\n📊 比对完成!")
    print(f"总行数: {total}")
    print(f"一致: {consistent_count}")
    print(f"不一致: {inconsistent}")
    print(f"一致率: {accuracy:.1f}%")
    print(f"结果已保存到: {output_file}")

    return {
        'total': total,
        'consistent': consistent_count,
        'inconsistent': inconsistent,
        'accuracy': accuracy,
        'output_file': output_file
    }

def main():
    """主函数，支持命令行参数"""
    import sys
    import os

    # 检查命令行参数
    if len(sys.argv) > 1:
        target_file = sys.argv[1]
        if not os.path.exists(target_file):
            print(f"❌ 文件不存在: {target_file}")
            return
    else:
        # 自动查找文件
        files_to_check = ["预期KV.xlsx", "预期KV.xls", "KV.xlsx", "kv.xlsx"]
        target_file = None

        for file in files_to_check:
            if os.path.exists(file):
                target_file = file
                break

        if not target_file:
            print("❌ 未找到KV文件")
            print("请确保以下文件之一存在于当前目录中:")
            for file in files_to_check:
                print(f"  - {file}")
            print("\n或者使用命令: python quick_kv_compare.py <文件名>")
            return

    # 执行比对
    try:
        result = quick_compare(target_file)

        if result:
            print(f"\n🎯 比对总结:")
            print(f"文件: {target_file}")
            print(f"准确率: {result['accuracy']:.1f}%")
            if result['inconsistent'] == 0:
                print("🎉 所有数据都一致！")
            else:
                print(f"⚠️  发现 {result['inconsistent']} 行不一致")

    except Exception as e:
        print(f"❌ 比对过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
