"""
DataFrame匹配模块
使用高级相似度算法进行DataFrame搜索和匹配
"""

import pandas as pd
import re
import logging
from difflib import SequenceMatcher
from typing import List, Dict, Tuple, Optional

# 设置日志
logger = logging.getLogger('DataFrame-Matcher')
logger.setLevel(logging.INFO)

class DataFrameMatcher:
    """
    DataFrame匹配器，使用多种高级相似度算法进行文本匹配
    """
    
    def __init__(self):
        """
        初始化DataFrame匹配器
        """
        self.logger = logger
    
    def search_matches(self, dataframe: pd.DataFrame, question: str, 
                      exact_match: bool = True, fuzzy_threshold: float = 0.6) -> List[Dict]:
        """
        在DataFrame中搜索匹配项
        
        参数:
            dataframe (pd.DataFrame): 要搜索的DataFrame，至少包含3列
            question (str): 用户问题
            exact_match (bool): 是否进行精确匹配，默认True
            fuzzy_threshold (float): 模糊匹配的相似度阈值，默认0.6
            
        返回:
            List[Dict]: 匹配结果列表，每个字典包含第一列和第二列的键值对
        """
        if not self._validate_inputs(dataframe, question):
            return []
        
        # 获取列名
        col1_name = dataframe.columns[0]  # 第一列
        col2_name = dataframe.columns[1]  # 第二列
        col3_name = dataframe.columns[2]  # 第三列
        
        self.logger.info(f"搜索DataFrame，列名: [{col1_name}, {col2_name}, {col3_name}]")
        self.logger.info(f"问题: {question}")
        
        matches = []
        
        if exact_match:
            # 精确匹配
            exact_matches = self._exact_search(dataframe, question, col1_name, col2_name, col3_name)
            matches.extend(exact_matches)
        
        # 模糊匹配
        fuzzy_matches = self._fuzzy_search(dataframe, question, col1_name, col2_name, col3_name, fuzzy_threshold)
        
        # 合并结果，去重
        all_matches = self._merge_and_deduplicate(matches, fuzzy_matches)
        
        self.logger.info(f"总共找到 {len(all_matches)} 个匹配")
        return all_matches
    
    def _validate_inputs(self, dataframe: pd.DataFrame, question: str) -> bool:
        """
        验证输入参数
        
        参数:
            dataframe (pd.DataFrame): DataFrame
            question (str): 问题
            
        返回:
            bool: 是否有效
        """
        if dataframe is None or dataframe.empty:
            self.logger.warning("DataFrame为空或None")
            return False
        
        if len(dataframe.columns) < 3:
            self.logger.warning(f"DataFrame列数不足，需要至少3列，当前有{len(dataframe.columns)}列")
            return False
        
        if not question or not question.strip():
            self.logger.warning("问题为空")
            return False
        
        return True
    
    def _exact_search(self, dataframe: pd.DataFrame, question: str, 
                     col1_name: str, col2_name: str, col3_name: str) -> List[Dict]:
        """
        精确搜索
        
        参数:
            dataframe (pd.DataFrame): DataFrame
            question (str): 问题
            col1_name, col2_name, col3_name (str): 列名
            
        返回:
            List[Dict]: 精确匹配结果
        """
        matches = []
        question_lower = question.lower().strip()
        
        for index, row in dataframe.iterrows():
            # 检查第二列
            col2_value = str(row[col2_name]).lower().strip()
            if col2_value and col2_value != 'nan' and col2_value in question_lower:
                match_dict = {
                    col1_name: row[col1_name],
                    col2_name: row[col2_name],
                    'match_type': 'exact',
                    'matched_column': col2_name,
                    'matched_text': row[col2_name]
                }
                matches.append(match_dict)
                self.logger.info(f"精确匹配(第二列): {match_dict}")
                continue
            
            # 检查第三列
            col3_value = str(row[col3_name]).lower().strip()
            if col3_value and col3_value != 'nan' and col3_value in question_lower:
                match_dict = {
                    col1_name: row[col1_name],
                    col2_name: row[col2_name],
                    'match_type': 'exact',
                    'matched_column': col3_name,
                    'matched_text': row[col3_name]
                }
                matches.append(match_dict)
                self.logger.info(f"精确匹配(第三列): {match_dict}")
        
        return matches
    
    def _fuzzy_search(self, dataframe: pd.DataFrame, question: str, 
                     col1_name: str, col2_name: str, col3_name: str, 
                     threshold: float) -> List[Dict]:
        """
        模糊搜索，使用多种高级相似度算法
        
        参数:
            dataframe (pd.DataFrame): DataFrame
            question (str): 问题
            col1_name, col2_name, col3_name (str): 列名
            threshold (float): 相似度阈值
            
        返回:
            List[Dict]: 模糊匹配结果
        """
        matches = []
        
        for index, row in dataframe.iterrows():
            # 检查第二列
            col2_similarity = self._calculate_advanced_similarity(question, str(row[col2_name]))
            if col2_similarity >= threshold:
                match_dict = {
                    col1_name: row[col1_name],
                    col2_name: row[col2_name],
                    'match_type': 'fuzzy',
                    'matched_column': col2_name,
                    'matched_text': row[col2_name],
                    'similarity': round(col2_similarity, 3)
                }
                matches.append(match_dict)
                self.logger.info(f"模糊匹配(第二列): {match_dict}")
                continue
            
            # 检查第三列
            col3_similarity = self._calculate_advanced_similarity(question, str(row[col3_name]))
            if col3_similarity >= threshold:
                match_dict = {
                    col1_name: row[col1_name],
                    col2_name: row[col2_name],
                    'match_type': 'fuzzy',
                    'matched_column': col3_name,
                    'matched_text': row[col3_name],
                    'similarity': round(col3_similarity, 3)
                }
                matches.append(match_dict)
                self.logger.info(f"模糊匹配(第三列): {match_dict}")
        
        # 按相似度降序排序
        matches.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        return matches
    
    def _calculate_advanced_similarity(self, text1: str, text2: str) -> float:
        """
        使用多种高级算法计算文本相似度
        
        参数:
            text1 (str): 第一个文本
            text2 (str): 第二个文本
            
        返回:
            float: 相似度分数 (0-1)
        """
        if not text1 or not text2 or str(text2) == 'nan':
            return 0.0
        
        text1_clean = self._clean_text(str(text1))
        text2_clean = self._clean_text(str(text2))
        
        if not text1_clean or not text2_clean:
            return 0.0
        
        # 1. 序列匹配器相似度 (SequenceMatcher)
        seq_similarity = SequenceMatcher(None, text1_clean, text2_clean).ratio()
        
        # 2. 包含关系检查
        containment_similarity = self._calculate_containment_similarity(text1_clean, text2_clean)
        
        # 3. Jaccard相似度 (基于字符n-gram)
        jaccard_similarity = self._calculate_jaccard_similarity(text1_clean, text2_clean)
        
        # 4. 编辑距离相似度
        edit_distance_similarity = self._calculate_edit_distance_similarity(text1_clean, text2_clean)
        
        # 5. 词汇重叠相似度
        word_overlap_similarity = self._calculate_word_overlap_similarity(text1_clean, text2_clean)
        
        # 综合相似度计算 (加权平均)
        weights = {
            'sequence': 0.25,
            'containment': 0.30,
            'jaccard': 0.20,
            'edit_distance': 0.15,
            'word_overlap': 0.10
        }
        
        final_similarity = (
            seq_similarity * weights['sequence'] +
            containment_similarity * weights['containment'] +
            jaccard_similarity * weights['jaccard'] +
            edit_distance_similarity * weights['edit_distance'] +
            word_overlap_similarity * weights['word_overlap']
        )
        
        return min(1.0, max(0.0, final_similarity))
    
    def _clean_text(self, text: str) -> str:
        """
        清理文本
        
        参数:
            text (str): 原始文本
            
        返回:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        # 转换为小写，去除多余空白，移除特殊字符
        cleaned = re.sub(r'[^\w\s\u4e00-\u9fff]', '', str(text).lower().strip())
        cleaned = re.sub(r'\s+', ' ', cleaned)
        return cleaned
    
    def _calculate_containment_similarity(self, text1: str, text2: str) -> float:
        """
        计算包含关系相似度
        """
        if text2 in text1 or text1 in text2:
            return 1.0
        
        # 计算部分包含
        shorter, longer = (text1, text2) if len(text1) < len(text2) else (text2, text1)
        
        if not shorter:
            return 0.0
        
        # 查找最长公共子串
        max_match = 0
        for i in range(len(shorter)):
            for j in range(i + 1, len(shorter) + 1):
                substring = shorter[i:j]
                if substring in longer:
                    max_match = max(max_match, len(substring))
        
        return max_match / len(shorter) if shorter else 0.0

    def _calculate_jaccard_similarity(self, text1: str, text2: str, n: int = 2) -> float:
        """
        计算基于n-gram的Jaccard相似度

        参数:
            text1, text2 (str): 要比较的文本
            n (int): n-gram的大小，默认为2

        返回:
            float: Jaccard相似度
        """
        def get_ngrams(text: str, n: int) -> set:
            if len(text) < n:
                return {text}
            return {text[i:i+n] for i in range(len(text) - n + 1)}

        ngrams1 = get_ngrams(text1, n)
        ngrams2 = get_ngrams(text2, n)

        if not ngrams1 and not ngrams2:
            return 1.0
        if not ngrams1 or not ngrams2:
            return 0.0

        intersection = len(ngrams1.intersection(ngrams2))
        union = len(ngrams1.union(ngrams2))

        return intersection / union if union > 0 else 0.0

    def _calculate_edit_distance_similarity(self, text1: str, text2: str) -> float:
        """
        计算基于编辑距离的相似度

        参数:
            text1, text2 (str): 要比较的文本

        返回:
            float: 编辑距离相似度
        """
        def levenshtein_distance(s1: str, s2: str) -> int:
            if len(s1) < len(s2):
                return levenshtein_distance(s2, s1)

            if len(s2) == 0:
                return len(s1)

            previous_row = list(range(len(s2) + 1))
            for i, c1 in enumerate(s1):
                current_row = [i + 1]
                for j, c2 in enumerate(s2):
                    insertions = previous_row[j + 1] + 1
                    deletions = current_row[j] + 1
                    substitutions = previous_row[j] + (c1 != c2)
                    current_row.append(min(insertions, deletions, substitutions))
                previous_row = current_row

            return previous_row[-1]

        if not text1 and not text2:
            return 1.0
        if not text1 or not text2:
            return 0.0

        max_len = max(len(text1), len(text2))
        edit_distance = levenshtein_distance(text1, text2)

        return 1.0 - (edit_distance / max_len) if max_len > 0 else 0.0

    def _calculate_word_overlap_similarity(self, text1: str, text2: str) -> float:
        """
        计算词汇重叠相似度

        参数:
            text1, text2 (str): 要比较的文本

        返回:
            float: 词汇重叠相似度
        """
        words1 = set(text1.split())
        words2 = set(text2.split())

        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        return intersection / union if union > 0 else 0.0

    def _merge_and_deduplicate(self, exact_matches: List[Dict], fuzzy_matches: List[Dict]) -> List[Dict]:
        """
        合并精确匹配和模糊匹配结果，并去重

        参数:
            exact_matches (List[Dict]): 精确匹配结果
            fuzzy_matches (List[Dict]): 模糊匹配结果

        返回:
            List[Dict]: 合并后的结果
        """
        # 使用第一列的值作为去重的键
        seen_keys = set()
        merged_results = []

        # 首先添加精确匹配结果（优先级更高）
        for match in exact_matches:
            key = str(match[list(match.keys())[0]])  # 第一列的值
            if key not in seen_keys:
                seen_keys.add(key)
                merged_results.append(match)

        # 然后添加模糊匹配结果（如果不重复）
        for match in fuzzy_matches:
            key = str(match[list(match.keys())[0]])  # 第一列的值
            if key not in seen_keys:
                seen_keys.add(key)
                merged_results.append(match)

        return merged_results

    def get_best_match(self, dataframe: pd.DataFrame, question: str,
                      fuzzy_threshold: float = 0.6) -> Optional[Dict]:
        """
        获取最佳匹配结果

        参数:
            dataframe (pd.DataFrame): 要搜索的DataFrame
            question (str): 用户问题
            fuzzy_threshold (float): 模糊匹配阈值

        返回:
            Optional[Dict]: 最佳匹配结果，如果没有找到则返回None
        """
        matches = self.search_matches(dataframe, question, exact_match=True, fuzzy_threshold=fuzzy_threshold)

        if not matches:
            return None

        # 精确匹配优先
        exact_matches = [m for m in matches if m.get('match_type') == 'exact']
        if exact_matches:
            return exact_matches[0]

        # 否则返回相似度最高的模糊匹配
        fuzzy_matches = [m for m in matches if m.get('match_type') == 'fuzzy']
        if fuzzy_matches:
            return max(fuzzy_matches, key=lambda x: x.get('similarity', 0))

        return matches[0] if matches else None


# 便捷函数
def search_dataframe(dataframe: pd.DataFrame, question: str,
                    exact_match: bool = True, fuzzy_threshold: float = 0.6) -> List[Dict]:
    """
    便捷函数：在DataFrame中搜索匹配项

    参数:
        dataframe (pd.DataFrame): 要搜索的DataFrame
        question (str): 用户问题
        exact_match (bool): 是否进行精确匹配
        fuzzy_threshold (float): 模糊匹配阈值

    返回:
        List[Dict]: 匹配结果列表
    """
    matcher = DataFrameMatcher()
    return matcher.search_matches(dataframe, question, exact_match, fuzzy_threshold)


def get_best_dataframe_match(dataframe: pd.DataFrame, question: str,
                           fuzzy_threshold: float = 0.6) -> Optional[Dict]:
    """
    便捷函数：获取最佳匹配结果

    参数:
        dataframe (pd.DataFrame): 要搜索的DataFrame
        question (str): 用户问题
        fuzzy_threshold (float): 模糊匹配阈值

    返回:
        Optional[Dict]: 最佳匹配结果
    """
    matcher = DataFrameMatcher()
    return matcher.get_best_match(dataframe, question, fuzzy_threshold)
