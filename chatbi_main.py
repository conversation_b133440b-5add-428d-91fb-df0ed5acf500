"""
平安理财ChatBI问答系统主程序
"""

import json
import os
import time
import logging
import sys
import traceback
from datetime import datetime
from chatbi_core import process_question

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chatbi_main.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ChatBI-Main")

def print_banner():
    """
    打印系统横幅
    """
    banner = """
    ╔═══════════════════════════════════════════════════════════════╗
    ║                                                               ║
    ║                  平安理财 ChatBI 问答系统                     ║
    ║                                                               ║
    ║  基于千问(Qwen)大模型，实现智能问答、关键词提取和模板匹配    ║
    ║                                                               ║
    ╚═══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_help():
    """
    打印帮助信息
    """
    help_text = """
    命令列表:
    - help: 显示帮助信息
    - exit/quit: 退出系统
    - debug on/off: 开启/关闭调试模式
    - local on/off: 开启/关闭本地模拟模式
    - clear: 清屏
    - history: 显示历史记录
    """
    print(help_text)

def save_history(history):
    """
    保存历史记录
    
    参数:
        history (list): 历史记录
    """
    try:
        with open("history.json", "w", encoding="utf-8") as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"保存历史记录失败: {str(e)}")

def load_history():
    """
    加载历史记录
    
    返回:
        list: 历史记录
    """
    try:
        if os.path.exists("history.json"):
            with open("history.json", "r", encoding="utf-8") as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"加载历史记录失败: {str(e)}")
    return []

def clear_screen():
    """
    清屏
    """
    os.system('cls' if os.name == 'nt' else 'clear')

def main():
    """
    主函数
    """
    try:
        # 打印系统横幅
        print_banner()
        
        # 加载历史记录
        history = load_history()
        
        # 检查是否使用本地模拟模式
        if os.environ.get("USE_LOCAL_SIMULATION") == "1":
            print("\n[注意] 系统运行在本地模拟模式，不会调用实际API\n")
        
        print("\n输入 'help' 获取帮助信息，输入 'exit' 或 'quit' 退出系统\n")
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n请输入您的问题: ")
                
                # 处理特殊命令
                if user_input.lower() in ['exit', 'quit']:
                    print("谢谢使用，再见!")
                    break
                elif user_input.lower() == 'help':
                    print_help()
                    continue
                elif user_input.lower() == 'debug on':
                    logging.getLogger().setLevel(logging.DEBUG)
                    print("已开启调试模式")
                    continue
                elif user_input.lower() == 'debug off':
                    logging.getLogger().setLevel(logging.INFO)
                    print("已关闭调试模式")
                    continue
                elif user_input.lower() == 'local on':
                    os.environ["USE_LOCAL_SIMULATION"] = "1"
                    print("已启用本地模拟模式")
                    continue
                elif user_input.lower() == 'local off':
                    os.environ["USE_LOCAL_SIMULATION"] = "0"
                    print("已关闭本地模拟模式")
                    continue
                elif user_input.lower() == 'clear':
                    clear_screen()
                    print_banner()
                    continue
                elif user_input.lower() == 'history':
                    if history:
                        print("\n历史记录:")
                        for i, record in enumerate(history[-10:], 1):
                            print(f"{i}. 问题: {record['user_question']}")
                            print(f"   回填模板: {record['filled_template']}")
                            print()
                    else:
                        print("暂无历史记录")
                    continue
                elif not user_input.strip():
                    continue
                
                # 处理用户问题
                logger.info(f"收到用户问题: {user_input}")
                start_time = time.time()
                
                try:
                    # 处理问题
                    result = process_question(user_input)
                    
                    if result["success"]:
                        # 添加到历史记录
                        history.append(result)
                        save_history(history)
                        
                        # 打印结果
                        print("\n" + "=" * 70)
                        print(f"用户问题: {result['user_question']}")
                        print("-" * 70)
                        print(f"提取关键词: {json.dumps(result['extracted_keywords'], ensure_ascii=False)}")
                        print("-" * 70)
                        print(f"匹配模板: {result['matched_template']}")
                        print("-" * 70)
                        print(f"回填模板: {result['filled_template']}")
                        print("-" * 70)
                        print(f"处理时间: {result['processing_time']:.2f}秒")
                        print("=" * 70)
                    else:
                        print("\n" + "=" * 70)
                        print(f"处理失败: {result['error']}")
                        print("=" * 70)
                
                except Exception as e:
                    logger.error(f"处理问题时出错: {str(e)}")
                    logger.debug(traceback.format_exc())
                    print(f"处理您的问题时出错: {str(e)}")
                    print("请稍后再试或尝试其他问题")
            
            except KeyboardInterrupt:
                print("\n操作被用户中断")
                break
            except Exception as e:
                logger.error(f"发生未预期的错误: {str(e)}")
                logger.debug(traceback.format_exc())
                print(f"发生错误: {str(e)}")
    
    except Exception as e:
        logger.critical(f"程序发生严重错误: {str(e)}")
        logger.debug(traceback.format_exc())
        print(f"程序发生错误: {str(e)}")
        print("请检查日志文件获取更多信息")

if __name__ == "__main__":
    main()
