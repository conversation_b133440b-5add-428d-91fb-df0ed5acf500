"""
创建提示词模板
用于平安理财ChatBI问答系统
"""

import json
import os
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chatbi_prompts.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ChatBI-Prompts")

def load_knowledge_base(filename):
    """
    加载知识库
    
    参数:
        filename (str): 知识库文件路径
        
    返回:
        dict: 知识库数据
    """
    try:
        logger.info(f"正在加载知识库: {filename}")
        with open(filename, "r", encoding="utf-8") as f:
            data = json.load(f)
        logger.info(f"知识库加载成功")
        return data
    except Exception as e:
        logger.error(f"加载知识库失败: {str(e)}")
        return {}

def create_extraction_prompt():
    """
    创建关键词提取提示词模板
    
    返回:
        str: 提示词模板
    """
    logger.info("正在创建关键词提取提示词模板")
    
    # 加载知识库
    industry_jargon = load_knowledge_base("knowledge_bases/industry_jargon.json")
    business_rules = load_knowledge_base("knowledge_bases/business_rules.json")
    metadata = load_knowledge_base("knowledge_bases/metadata.json")
    
    # 构建提示词
    prompt = """# 平安理财ChatBI关键词提取

## 任务描述
你是平安理财ChatBI系统的关键词提取组件。你的任务是从用户问题中提取关键词，并将其映射到标准字段。

## 知识库

### 行业黑话库
以下是金融行业常用的黑话和别名，请在提取关键词时进行转换：

"""
    
    # 添加行业黑话库
    if industry_jargon:
        for category, items in industry_jargon.items():
            prompt += f"#### {category}\n"
            for alias, standard in items.items():
                prompt += f"- {alias} → {standard}\n"
            prompt += "\n"
    
    prompt += """### 业务规则库
以下是业务规则，帮助你理解问题中的业务逻辑：

"""
    
    # 添加业务规则库
    if business_rules:
        for category, items in business_rules.items():
            prompt += f"#### {category}\n"
            for rule, description in items.items():
                prompt += f"- {rule}: {description}\n"
            prompt += "\n"
    
    prompt += """### 元数据库
以下是数据字段的定义和含义，帮助你理解问题中涉及的数据：

"""
    
    # 添加元数据库
    if metadata:
        for category, items in metadata.items():
            prompt += f"#### {category}\n"
            if isinstance(items, dict):
                for field, sub_items in items.items():
                    prompt += f"- {field}:\n"
                    if isinstance(sub_items, dict):
                        for sub_field, description in sub_items.items():
                            prompt += f"  - {sub_field}: {description}\n"
                    else:
                        prompt += f"  - {sub_items}\n"
            prompt += "\n"
    
    prompt += """## 提取规则
1. 从用户问题中提取关键词，并映射到标准字段
2. 处理时间表达式，如"昨天"、"今天"、"本月"等，转换为具体日期
3. 处理行业黑话和别名，转换为标准表达
4. 识别问题中的指标名称，如"持仓规模"、"申购金额"等
5. 输出JSON格式的结果，包含提取的关键词及其对应的标准字段

## 输出格式
```json
{
  "字段名1": ["值1", "值2", ...],
  "字段名2": ["值1", "值2", ...],
  ...
}
```

## 示例
用户问题: "昨天天天成长3号在小招的赎回金额？"
提取结果:
```json
{
  "数据日期": ["2025年5月14日"],
  "产品简称": ["天天成长3号"],
  "渠道名称": ["招商银行"],
  "指标名": ["赎回金额"]
}
```

现在，请分析以下用户问题，提取关键词：

用户问题: "{user_question}"

提取结果:
"""
    
    logger.info("关键词提取提示词模板创建完成")
    return prompt

def create_template_matching_prompt():
    """
    创建模板匹配提示词模板
    
    返回:
        str: 提示词模板
    """
    logger.info("正在创建模板匹配提示词模板")
    
    prompt = """# 平安理财ChatBI模板匹配

## 任务描述
你是平安理财ChatBI系统的模板匹配组件。你的任务是根据提取的关键词，从模板库中找到最匹配的模板。

## 模板格式
模板使用 {#字段名#} 的格式表示变量，例如：
- {#数据日期#}{#产品简称#}在{#渠道名称#}的赎回金额是多少?
- {#数据日期#}{#产品简称#}的总客户数是多少?

## 匹配规则
1. 根据提取的关键词，找到包含这些字段的模板
2. 优先选择字段匹配度最高的模板
3. 如果有多个模板匹配度相同，选择语义最接近的模板

## 输出格式
```json
{
  "matched_template": "模板内容",
  "confidence": 匹配度(0-100)
}
```

## 示例
提取的关键词:
```json
{
  "数据日期": ["2025年5月14日"],
  "产品简称": ["天天成长3号"],
  "渠道名称": ["招商银行"],
  "指标名": ["赎回金额"]
}
```

模板库:
```json
{
  "{#数据日期#}{#产品简称#}在{#渠道名称#}的赎回金额是多少?": "{#数据日期#}{#产品简称#}在{#渠道名称#}的赎回金额是多少?",
  "{#数据日期#}{#产品简称#}的总客户数是多少?": "{#数据日期#}{#产品简称#}的总客户数是多少?"
}
```

匹配结果:
```json
{
  "matched_template": "{#数据日期#}{#产品简称#}在{#渠道名称#}的赎回金额是多少?",
  "confidence": 95
}
```

现在，请根据以下提取的关键词和模板库，找到最匹配的模板：

提取的关键词:
```json
{extracted_keywords}
```

模板库:
```json
{templates}
```

匹配结果:
"""
    
    logger.info("模板匹配提示词模板创建完成")
    return prompt

def create_template_filling_prompt():
    """
    创建模板填充提示词模板
    
    返回:
        str: 提示词模板
    """
    logger.info("正在创建模板填充提示词模板")
    
    prompt = """# 平安理财ChatBI模板填充

## 任务描述
你是平安理财ChatBI系统的模板填充组件。你的任务是将提取的关键词填充到匹配的模板中。

## 填充规则
1. 将模板中的 {#字段名#} 替换为提取的关键词对应的值
2. 如果一个字段有多个值，使用第一个值
3. 如果模板中的字段在提取的关键词中不存在，保留原样

## 输出格式
```
填充后的模板内容
```

## 示例
提取的关键词:
```json
{
  "数据日期": ["2025年5月14日"],
  "产品简称": ["天天成长3号"],
  "渠道名称": ["招商银行"],
  "指标名": ["赎回金额"]
}
```

匹配的模板:
```
{#数据日期#}{#产品简称#}在{#渠道名称#}的赎回金额是多少?
```

填充结果:
```
2025年5月14日天天成长3号在招商银行的赎回金额是多少?
```

现在，请根据以下提取的关键词和匹配的模板，生成填充后的模板：

提取的关键词:
```json
{extracted_keywords}
```

匹配的模板:
```
{matched_template}
```

填充结果:
"""
    
    logger.info("模板填充提示词模板创建完成")
    return prompt

def save_prompt_template(prompt, filename):
    """
    保存提示词模板
    
    参数:
        prompt (str): 提示词模板
        filename (str): 文件名
    """
    try:
        logger.info(f"正在保存提示词模板: {filename}")
        with open(filename, "w", encoding="utf-8") as f:
            f.write(prompt)
        logger.info(f"提示词模板保存成功: {filename}")
    except Exception as e:
        logger.error(f"保存提示词模板失败: {str(e)}")

def main():
    # 创建提示词模板目录
    os.makedirs("prompts", exist_ok=True)
    
    # 创建关键词提取提示词模板
    extraction_prompt = create_extraction_prompt()
    save_prompt_template(extraction_prompt, "prompts/extraction_prompt.txt")
    
    # 创建模板匹配提示词模板
    template_matching_prompt = create_template_matching_prompt()
    save_prompt_template(template_matching_prompt, "prompts/template_matching_prompt.txt")
    
    # 创建模板填充提示词模板
    template_filling_prompt = create_template_filling_prompt()
    save_prompt_template(template_filling_prompt, "prompts/template_filling_prompt.txt")
    
    logger.info("所有提示词模板创建完成")

if __name__ == "__main__":
    main()
