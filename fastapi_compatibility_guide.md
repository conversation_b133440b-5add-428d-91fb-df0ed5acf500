# FastAPI兼容性指南

## 问题说明

在FastAPI和uvicorn环境中使用`extract_keywords_with_output`的并发调用时，可能会遇到以下问题：

1. **事件循环冲突**：`concurrent.futures.ThreadPoolExecutor`在异步环境中可能导致死锁
2. **性能问题**：同步代码阻塞异步事件循环
3. **资源竞争**：多个请求同时使用线程池可能导致资源竞争

## 解决方案

我们提供了两个版本的关键词提取函数：

### 1. 同步版本（推荐用于FastAPI）

```python
from chatbi_core import extract_keywords_with_output

# 在FastAPI路由中使用
@app.post("/extract_keywords")
async def extract_keywords_endpoint(question: str):
    # 同步版本，在FastAPI中运行良好
    keywords, model_output = extract_keywords_with_output(question)
    return {
        "keywords": keywords,
        "model_output": model_output
    }
```

### 2. 异步版本（适用于高并发场景）

```python
from chatbi_core import extract_keywords_with_output_async

# 在FastAPI路由中使用异步版本
@app.post("/extract_keywords_async")
async def extract_keywords_async_endpoint(question: str):
    # 异步版本，更适合高并发
    keywords, model_output = await extract_keywords_with_output_async(question)
    return {
        "keywords": keywords,
        "model_output": model_output
    }
```

## 完整的FastAPI示例

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from chatbi_core import extract_keywords_with_output, extract_keywords_with_output_async
import time

app = FastAPI(title="ChatBI关键词提取API")

class QuestionRequest(BaseModel):
    question: str
    use_async: bool = False

class KeywordResponse(BaseModel):
    question: str
    keywords: dict
    model_output: str = None
    processing_time: float
    method: str

@app.post("/extract_keywords", response_model=KeywordResponse)
async def extract_keywords_endpoint(request: QuestionRequest):
    """
    提取关键词接口
    
    支持同步和异步两种模式
    """
    try:
        start_time = time.time()
        
        if request.use_async:
            # 使用异步版本
            keywords, model_output = await extract_keywords_with_output_async(request.question)
            method = "async"
        else:
            # 使用同步版本（推荐）
            keywords, model_output = extract_keywords_with_output(request.question)
            method = "sync"
        
        processing_time = time.time() - start_time
        
        return KeywordResponse(
            question=request.question,
            keywords=keywords,
            model_output=model_output,
            processing_time=processing_time,
            method=method
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"关键词提取失败: {str(e)}")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "ChatBI关键词提取"}

# 启动命令
# uvicorn fastapi_app:app --host 0.0.0.0 --port 8000 --reload
```

## 性能对比测试

```python
import asyncio
import time
from chatbi_core import extract_keywords_with_output, extract_keywords_with_output_async

async def performance_test():
    """性能对比测试"""
    question = "昨天天天成长3号在小招的赎回金额？"
    
    # 测试同步版本
    print("测试同步版本...")
    start_time = time.time()
    keywords_sync, _ = extract_keywords_with_output(question)
    sync_time = time.time() - start_time
    print(f"同步版本耗时: {sync_time:.2f}秒")
    
    # 测试异步版本
    print("测试异步版本...")
    start_time = time.time()
    keywords_async, _ = await extract_keywords_with_output_async(question)
    async_time = time.time() - start_time
    print(f"异步版本耗时: {async_time:.2f}秒")
    
    # 结果对比
    print(f"结果一致性: {keywords_sync == keywords_async}")
    print(f"性能差异: {abs(sync_time - async_time):.2f}秒")

# 运行测试
# asyncio.run(performance_test())
```

## 并发测试

```python
import asyncio
import aiohttp
import time

async def concurrent_test():
    """并发请求测试"""
    url = "http://localhost:8000/extract_keywords"
    questions = [
        "昨天天天成长3号在小招的赎回金额？",
        "今天固定收益类在代销的净申赎金额是多少?",
        "平安理财启元策略日开270天持有4号固收类理财产品B的规模是多少？"
    ] * 10  # 30个并发请求
    
    async with aiohttp.ClientSession() as session:
        start_time = time.time()
        
        tasks = []
        for question in questions:
            task = session.post(url, json={
                "question": question,
                "use_async": True  # 使用异步版本
            })
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks)
        
        end_time = time.time()
        
        print(f"并发测试完成:")
        print(f"请求数量: {len(questions)}")
        print(f"总耗时: {end_time - start_time:.2f}秒")
        print(f"平均耗时: {(end_time - start_time) / len(questions):.2f}秒/请求")
        print(f"QPS: {len(questions) / (end_time - start_time):.2f}")

# 运行并发测试
# asyncio.run(concurrent_test())
```

## 最佳实践建议

### 1. 选择合适的版本

- **低并发场景**：使用同步版本`extract_keywords_with_output()`
- **高并发场景**：使用异步版本`extract_keywords_with_output_async()`
- **混合场景**：根据请求量动态选择

### 2. 错误处理

```python
@app.post("/extract_keywords_safe")
async def extract_keywords_safe(request: QuestionRequest):
    try:
        if request.use_async:
            keywords, model_output = await extract_keywords_with_output_async(request.question)
        else:
            keywords, model_output = extract_keywords_with_output(request.question)
        
        if not keywords:
            raise HTTPException(status_code=400, detail="未能提取到关键词")
        
        return {"keywords": keywords, "model_output": model_output}
        
    except asyncio.TimeoutError:
        raise HTTPException(status_code=408, detail="请求超时")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器错误: {str(e)}")
```

### 3. 监控和日志

```python
import logging
from fastapi import Request

logger = logging.getLogger("chatbi_api")

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    
    logger.info(f"{request.method} {request.url} - {response.status_code} - {process_time:.2f}s")
    return response
```

### 4. 资源限制

```python
from fastapi import FastAPI
from contextlib import asynccontextmanager
import asyncio

# 限制并发数量
semaphore = asyncio.Semaphore(10)  # 最多10个并发请求

@app.post("/extract_keywords_limited")
async def extract_keywords_limited(request: QuestionRequest):
    async with semaphore:
        keywords, model_output = await extract_keywords_with_output_async(request.question)
        return {"keywords": keywords, "model_output": model_output}
```

## 故障排除

### 常见问题

1. **"This event loop is already running"错误**
   - 解决方案：使用异步版本`extract_keywords_with_output_async()`

2. **请求超时**
   - 解决方案：增加超时时间或优化模型调用

3. **内存使用过高**
   - 解决方案：限制并发数量，使用连接池

4. **性能下降**
   - 解决方案：监控资源使用，优化线程池配置

### 调试技巧

```python
import logging
import sys

# 启用详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chatbi_debug.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

# 监控异步任务
async def debug_extract_keywords(question: str):
    logger = logging.getLogger("debug")
    logger.info(f"开始处理问题: {question}")
    
    try:
        result = await extract_keywords_with_output_async(question)
        logger.info(f"处理完成: {len(result[0])} 个关键词")
        return result
    except Exception as e:
        logger.error(f"处理失败: {str(e)}", exc_info=True)
        raise
```

---

**总结**：通过提供同步和异步两个版本，我们解决了FastAPI环境中的兼容性问题。推荐在大多数场景下使用同步版本，在高并发场景下使用异步版本。
