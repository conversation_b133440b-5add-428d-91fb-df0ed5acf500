// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const questionForm = document.getElementById('questionForm');
    const userQuestionInput = document.getElementById('userQuestion');
    const chatMessages = document.getElementById('chatMessages');
    const loadingOverlay = document.getElementById('loadingOverlay');
    const resultContainer = document.getElementById('resultContainer');
    const extractedKeywords = document.getElementById('extractedKeywords');
    const matchedTemplate = document.getElementById('matchedTemplate');
    const filledTemplate = document.getElementById('filledTemplate');
    const processingTime = document.getElementById('processingTime');
    const historyList = document.getElementById('historyList');
    const clearHistoryBtn = document.getElementById('clearHistory');

    // 加载历史记录
    loadHistory();

    // 提交问题表单
    questionForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const question = userQuestionInput.value.trim();
        if (!question) return;

        // 添加用户消息到聊天区域
        addMessage(question, 'user');

        // 清空输入框
        userQuestionInput.value = '';

        // 显示加载中提示
        loadingOverlay.style.display = 'flex';

        // 隐藏结果容器
        resultContainer.style.display = 'none';

        // 发送请求到后端
        fetch('/api/process_question', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ question: question })
        })
        .then(response => response.json())
        .then(data => {
            // 隐藏加载中提示
            loadingOverlay.style.display = 'none';

            if (data.success) {
                const result = data.result;

                // 添加系统回复到聊天区域
                addMessage(result.filled_template, 'system');

                // 显示处理结果
                showResult(result);

                // 重新加载历史记录
                loadHistory();
            } else {
                // 添加错误消息到聊天区域
                addMessage(`处理失败: ${data.error}`, 'system');

                // 即使处理失败，也显示处理结果（包括大模型输出）
                if (data.result) {
                    showResult(data.result);
                }
            }
        })
        .catch(error => {
            // 隐藏加载中提示
            loadingOverlay.style.display = 'none';

            // 添加错误消息到聊天区域
            addMessage(`发生错误: ${error.message}`, 'system');
            console.error('Error:', error);
        });
    });

    // 清空历史记录
    clearHistoryBtn.addEventListener('click', function() {
        if (confirm('确定要清空所有历史记录吗？')) {
            fetch('/api/clear_history', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    historyList.innerHTML = '';
                    alert('历史记录已清空');
                } else {
                    alert(`清空历史记录失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert(`发生错误: ${error.message}`);
            });
        }
    });

    // 添加消息到聊天区域
    function addMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = content;

        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = getCurrentTime();

        messageDiv.appendChild(contentDiv);
        messageDiv.appendChild(timeDiv);

        chatMessages.appendChild(messageDiv);

        // 滚动到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // 显示处理结果
    function showResult(result) {
        // 显示提取的关键词
        extractedKeywords.textContent = JSON.stringify(result.extracted_keywords, null, 2);

        // 显示匹配的模板
        matchedTemplate.textContent = result.matched_template;

        // 显示填充后的模板
        filledTemplate.textContent = result.filled_template;

        // 显示大模型输出
        const modelOutput = document.getElementById('modelOutput');
        if (modelOutput && result.model_output) {
            modelOutput.textContent = result.model_output;
        } else if (modelOutput) {
            modelOutput.textContent = "无大模型输出";
        }

        // 显示处理时间
        processingTime.textContent = `处理时间: ${result.processing_time.toFixed(2)}秒`;

        // 显示结果容器
        resultContainer.style.display = 'block';
    }

    // 加载历史记录
    function loadHistory() {
        fetch('/api/history')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 清空历史记录列表
                historyList.innerHTML = '';

                // 添加历史记录
                const history = data.history;
                history.forEach((item, index) => {
                    const historyItem = document.createElement('div');
                    historyItem.className = 'history-item';
                    historyItem.textContent = item.user_question;
                    historyItem.title = item.filled_template;

                    // 点击历史记录项
                    historyItem.addEventListener('click', function() {
                        // 添加用户消息到聊天区域
                        addMessage(item.user_question, 'user');

                        // 添加系统回复到聊天区域
                        addMessage(item.filled_template, 'system');

                        // 显示处理结果
                        showResult(item);
                    });

                    historyList.appendChild(historyItem);
                });
            } else {
                console.error('加载历史记录失败:', data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }

    // 获取当前时间
    function getCurrentTime() {
        const now = new Date();
        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    }
});
