<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平安理财ChatBI问答系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 sidebar">
                <div class="sidebar-header">
                    <h3>平安理财ChatBI</h3>
                    <p>基于千问(Qwen)大模型</p>
                </div>
                <div class="sidebar-content">
                    <div class="history-header">
                        <h5>历史记录</h5>
                        <button id="clearHistory" class="btn btn-sm btn-outline-danger">清空</button>
                    </div>
                    <div id="historyList" class="history-list">
                        <!-- 历史记录将在这里动态加载 -->
                    </div>
                </div>
                <div class="sidebar-footer">
                    <p>© 2025 平安理财</p>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9 main-content">
                <div class="chat-container">
                    <div id="chatMessages" class="chat-messages">
                        <div class="system-message">
                            <div class="message-content">
                                <p>欢迎使用平安理财ChatBI问答系统！</p>
                                <p>您可以询问关于产品、渠道、规模等方面的问题。</p>
                                <p>例如：</p>
                                <ul>
                                    <li>昨天天天成长3号在小招的赎回金额？</li>
                                    <li>本月稳利多6号的持仓规模是多少？</li>
                                    <li>上周新启航系列产品的总客户数？</li>
                                </ul>
                            </div>
                        </div>
                        <!-- 聊天消息将在这里动态加载 -->
                    </div>

                    <div class="chat-input">
                        <form id="questionForm">
                            <div class="input-group">
                                <input type="text" id="userQuestion" class="form-control" placeholder="请输入您的问题..." required>
                                <button type="submit" class="btn btn-primary">发送</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="result-container" id="resultContainer" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5>处理结果</h5>
                        </div>
                        <div class="card-body">
                            <div class="result-section">
                                <h6>提取关键词</h6>
                                <pre id="extractedKeywords" class="result-code"></pre>
                            </div>
                            <div class="result-section">
                                <h6>匹配模板</h6>
                                <pre id="matchedTemplate" class="result-code"></pre>
                            </div>
                            <div class="result-section">
                                <h6>回填模板</h6>
                                <pre id="filledTemplate" class="result-code"></pre>
                            </div>
                            <div class="result-section">
                                <h6>大模型输出</h6>
                                <pre id="modelOutput" class="result-code"></pre>
                            </div>
                            <div class="result-info">
                                <small id="processingTime"></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中提示 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p>正在处理您的问题...</p>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/script.js"></script>
</body>
</html>
