"""
产品推荐模块
使用BM25算法根据用户问题推荐最相似的产品
"""

import pandas as pd
import math
import re
from collections import Counter, defaultdict
from typing import List, Tuple, Dict
import os


def simple_chinese_tokenize(text: str) -> List[str]:
    """
    简单的中文分词函数
    将中文字符按字符分割，英文和数字按词分割

    参数:
        text (str): 输入文本

    返回:
        List[str]: 分词结果
    """
    if not text:
        return []

    tokens = []
    current_word = ""

    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符
            if current_word:
                tokens.append(current_word)
                current_word = ""
            tokens.append(char)
        elif char.isalnum():  # 英文字母或数字
            current_word += char
        else:  # 其他字符（标点符号等）
            if current_word:
                tokens.append(current_word)
                current_word = ""

    if current_word:
        tokens.append(current_word)

    return [token for token in tokens if token.strip()]


class BM25:
    """
    BM25算法实现
    """
    
    def __init__(self, k1: float = 1.5, b: float = 0.75):
        """
        初始化BM25算法
        
        参数:
            k1 (float): 控制词频饱和度的参数，默认1.5
            b (float): 控制文档长度归一化的参数，默认0.75
        """
        self.k1 = k1
        self.b = b
        self.corpus = []
        self.doc_freqs = []
        self.idf = {}
        self.doc_len = []
        self.avgdl = 0
        
    def fit(self, corpus: List[str]):
        """
        训练BM25模型
        
        参数:
            corpus (List[str]): 文档语料库
        """
        self.corpus = corpus
        self.doc_len = [len(doc.split()) for doc in corpus]
        self.avgdl = sum(self.doc_len) / len(self.doc_len)
        
        # 计算每个文档的词频
        self.doc_freqs = []
        for doc in corpus:
            freq = Counter(doc.split())
            self.doc_freqs.append(freq)
        
        # 计算IDF值
        self._calculate_idf()
    
    def _calculate_idf(self):
        """
        计算逆文档频率(IDF)
        """
        N = len(self.corpus)
        self.idf = defaultdict(float)
        
        # 统计每个词在多少个文档中出现
        df = defaultdict(int)
        for doc_freq in self.doc_freqs:
            for word in doc_freq.keys():
                df[word] += 1
        
        # 计算IDF
        for word, freq in df.items():
            self.idf[word] = math.log((N - freq + 0.5) / (freq + 0.5))
    
    def get_scores(self, query: str) -> List[float]:
        """
        计算查询与所有文档的BM25分数
        
        参数:
            query (str): 查询字符串
            
        返回:
            List[float]: 每个文档的BM25分数
        """
        query_words = query.split()
        scores = []
        
        for i, doc_freq in enumerate(self.doc_freqs):
            score = 0
            doc_len = self.doc_len[i]
            
            for word in query_words:
                if word in doc_freq:
                    # BM25公式
                    tf = doc_freq[word]
                    idf = self.idf.get(word, 0)
                    
                    numerator = tf * (self.k1 + 1)
                    denominator = tf + self.k1 * (1 - self.b + self.b * doc_len / self.avgdl)
                    
                    score += idf * (numerator / denominator)
            
            scores.append(score)
        
        return scores


class ProductRecommender:
    """
    产品推荐器
    """
    
    def __init__(self, excel_path: str = "knowledge_bases/产品库.xlsx"):
        """
        初始化产品推荐器
        
        参数:
            excel_path (str): 产品库Excel文件路径
        """
        self.excel_path = excel_path
        self.df = None
        self.bm25 = BM25()
        self.product_texts = []
        self.load_product_data()
    
    def load_product_data(self):
        """
        加载产品数据
        """
        try:
            # 读取Excel文件
            self.df = pd.read_excel(self.excel_path)
            
            if len(self.df.columns) < 2:
                raise ValueError("Excel文件至少需要2列数据")
            
            # 提取第二列数据
            second_column = self.df.iloc[:, 1]  # 第二列（索引为1）
            
            # 处理文本数据
            self.product_texts = []
            for text in second_column:
                if pd.isna(text):
                    self.product_texts.append("")
                else:
                    # 使用简单中文分词
                    tokens = simple_chinese_tokenize(str(text))
                    segmented = " ".join(tokens)
                    self.product_texts.append(segmented)
            
            # 训练BM25模型
            if self.product_texts:
                self.bm25.fit(self.product_texts)
                print(f"成功加载 {len(self.product_texts)} 条产品数据")
            else:
                print("警告: 没有找到有效的产品数据")
                
        except FileNotFoundError:
            print(f"错误: 找不到文件 {self.excel_path}")
            self.df = pd.DataFrame()
            self.product_texts = []
        except Exception as e:
            print(f"加载产品数据时出错: {str(e)}")
            self.df = pd.DataFrame()
            self.product_texts = []
    
    def preprocess_query(self, query: str) -> str:
        """
        预处理用户查询
        
        参数:
            query (str): 用户问题
            
        返回:
            str: 预处理后的查询
        """
        if not query:
            return ""
        
        # 使用简单中文分词
        tokens = simple_chinese_tokenize(query)
        segmented = " ".join(tokens)
        return segmented
    
    def recommend_products(self, user_question: str, top_k: int = 100) -> List[Dict]:
        """
        根据用户问题推荐产品
        
        参数:
            user_question (str): 用户问题
            top_k (int): 返回最相似的前k条数据，默认100
            
        返回:
            List[Dict]: 推荐的产品列表，每个元素包含产品信息和相似度分数
        """
        if self.df is None or self.df.empty:
            print("错误: 产品数据未加载")
            return []
        
        if not user_question.strip():
            print("错误: 用户问题不能为空")
            return []
        
        # 预处理用户问题
        processed_query = self.preprocess_query(user_question)
        
        if not processed_query.strip():
            print("警告: 预处理后的查询为空")
            return []
        
        # 计算BM25分数
        scores = self.bm25.get_scores(processed_query)
        
        # 创建结果列表
        results = []
        for i, score in enumerate(scores):
            if i < len(self.df):
                result = {
                    'index': i,
                    'score': score,
                    'product_info': self.df.iloc[i].to_dict()
                }
                results.append(result)
        
        # 按分数降序排序
        results.sort(key=lambda x: x['score'], reverse=True)
        
        # 返回前top_k个结果
        top_results = results[:top_k]
        
        print(f"用户问题: {user_question}")
        print(f"找到 {len(top_results)} 条相似产品")
        if top_results:
            print(f"最高分数: {top_results[0]['score']:.4f}")
            print(f"最低分数: {top_results[-1]['score']:.4f}")
        
        return top_results
    
    def get_product_list(self, user_question: str, top_k: int = 100) -> List[str]:
        """
        获取推荐产品的简化列表（仅返回第二列的值）
        
        参数:
            user_question (str): 用户问题
            top_k (int): 返回最相似的前k条数据，默认100
            
        返回:
            List[str]: 推荐的产品名称列表
        """
        recommendations = self.recommend_products(user_question, top_k)
        
        if not recommendations:
            return []
        
        # 提取第二列的值
        product_list = []
        second_column_name = self.df.columns[1]  # 第二列的列名
        
        for rec in recommendations:
            product_name = rec['product_info'].get(second_column_name, "")
            if product_name and not pd.isna(product_name):
                product_list.append(str(product_name))
        
        return product_list


def recommend_products_by_question(user_question: str, excel_path: str = "knowledge_bases/产品库.xlsx", top_k: int = 100) -> List[str]:
    """
    便捷函数：根据用户问题推荐产品
    
    参数:
        user_question (str): 用户问题
        excel_path (str): 产品库Excel文件路径
        top_k (int): 返回最相似的前k条数据，默认100
        
    返回:
        List[str]: 推荐的产品名称列表
    """
    recommender = ProductRecommender(excel_path)
    return recommender.get_product_list(user_question, top_k)


# 示例使用
if __name__ == "__main__":
    # 测试函数
    def test_product_recommender():
        """
        测试产品推荐功能
        """
        print("=" * 60)
        print("测试产品推荐功能")
        print("=" * 60)
        
        # 创建推荐器实例
        recommender = ProductRecommender()
        
        # 测试问题
        test_questions = [
            "我想要理财产品",
            "有什么基金推荐",
            "低风险的投资产品",
            "高收益理财",
            "定期存款"
        ]
        
        for question in test_questions:
            print(f"\n问题: {question}")
            print("-" * 40)
            
            # 获取推荐结果
            recommendations = recommender.get_product_list(question, top_k=5)
            
            if recommendations:
                print("推荐产品:")
                for i, product in enumerate(recommendations, 1):
                    print(f"  {i}. {product}")
            else:
                print("没有找到相关产品")
        
        print("\n" + "=" * 60)
        print("测试完成")
    
    # 运行测试
    test_product_recommender()
