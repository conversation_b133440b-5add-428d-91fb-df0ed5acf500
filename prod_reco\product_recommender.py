"""
产品召回模块
使用BM25算法根据用户问题召回最相似的产品内容
"""

import pandas as pd
import math
from collections import Counter, defaultdict
from typing import List


def simple_chinese_tokenize(text: str) -> List[str]:
    """
    简单的中文分词函数
    将中文字符按字符分割，英文和数字按词分割

    参数:
        text (str): 输入文本

    返回:
        List[str]: 分词结果
    """
    if not text:
        return []

    tokens = []
    current_word = ""

    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符
            if current_word:
                tokens.append(current_word)
                current_word = ""
            tokens.append(char)
        elif char.isalnum():  # 英文字母或数字
            current_word += char
        else:  # 其他字符（标点符号等）
            if current_word:
                tokens.append(current_word)
                current_word = ""

    if current_word:
        tokens.append(current_word)

    return [token for token in tokens if token.strip()]


class BM25:
    """
    BM25算法实现
    """

    def __init__(self, k1: float = 1.5, b: float = 0.75):
        """
        初始化BM25算法

        参数:
            k1 (float): 控制词频饱和度的参数，默认1.5
            b (float): 控制文档长度归一化的参数，默认0.75
        """
        self.k1 = k1
        self.b = b
        self.corpus = []
        self.doc_freqs = []
        self.idf = {}
        self.doc_len = []
        self.avgdl = 0

    def fit(self, corpus: List[str]):
        """
        训练BM25模型

        参数:
            corpus (List[str]): 文档语料库
        """
        self.corpus = corpus
        self.doc_len = [len(doc.split()) for doc in corpus]
        self.avgdl = sum(self.doc_len) / len(self.doc_len) if self.doc_len else 0

        # 计算每个文档的词频
        self.doc_freqs = []
        for doc in corpus:
            freq = Counter(doc.split())
            self.doc_freqs.append(freq)

        # 计算IDF值
        self._calculate_idf()

    def _calculate_idf(self):
        """
        计算逆文档频率(IDF)
        """
        N = len(self.corpus)
        self.idf = defaultdict(float)

        # 统计每个词在多少个文档中出现
        df = defaultdict(int)
        for doc_freq in self.doc_freqs:
            for word in doc_freq.keys():
                df[word] += 1

        # 计算IDF
        for word, freq in df.items():
            self.idf[word] = math.log((N - freq + 0.5) / (freq + 0.5))

    def get_scores(self, query: str) -> List[float]:
        """
        计算查询与所有文档的BM25分数

        参数:
            query (str): 查询字符串

        返回:
            List[float]: 每个文档的BM25分数
        """
        query_words = query.split()
        scores = []

        for i, doc_freq in enumerate(self.doc_freqs):
            score = 0
            doc_len = self.doc_len[i]

            for word in query_words:
                if word in doc_freq:
                    # BM25公式
                    tf = doc_freq[word]
                    idf = self.idf.get(word, 0)

                    numerator = tf * (self.k1 + 1)
                    denominator = tf + self.k1 * (1 - self.b + self.b * doc_len / self.avgdl) if self.avgdl > 0 else tf + self.k1

                    score += idf * (numerator / denominator)

            scores.append(score)

        return scores


def recall_similar_products(user_question: str, excel_path: str = "knowledge_bases/产品库.xlsx", top_k: int = 100) -> List[str]:
    """
    根据用户问题召回最相似的产品内容

    参数:
        user_question (str): 用户问题
        excel_path (str): 产品库Excel文件路径，默认 "knowledge_bases/产品库.xlsx"
        top_k (int): 返回最相似的前k条数据，默认100

    返回:
        List[str]: 最相似的产品内容列表（第二列的值）
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)

        if len(df.columns) < 2:
            print("错误: Excel文件至少需要2列数据")
            return []

        # 提取第二列数据
        second_column = df.iloc[:, 1]  # 第二列（索引为1）

        # 处理文本数据并进行分词
        product_texts = []
        original_texts = []

        for text in second_column:
            if pd.isna(text):
                product_texts.append("")
                original_texts.append("")
            else:
                original_text = str(text)
                original_texts.append(original_text)

                # 使用简单中文分词
                tokens = simple_chinese_tokenize(original_text)
                segmented = " ".join(tokens)
                product_texts.append(segmented)

        if not product_texts:
            print("警告: 没有找到有效的产品数据")
            return []

        # 初始化并训练BM25模型
        bm25 = BM25()
        bm25.fit(product_texts)

        # 预处理用户问题
        if not user_question.strip():
            print("错误: 用户问题不能为空")
            return []

        tokens = simple_chinese_tokenize(user_question)
        processed_query = " ".join(tokens)

        if not processed_query.strip():
            print("警告: 预处理后的查询为空")
            return []

        # 计算BM25分数
        scores = bm25.get_scores(processed_query)

        # 创建(分数, 原始文本)的元组列表
        scored_texts = [(score, original_texts[i]) for i, score in enumerate(scores) if i < len(original_texts)]

        # 按分数降序排序
        scored_texts.sort(key=lambda x: x[0], reverse=True)

        # 提取前top_k个结果的文本
        result_texts = []
        for score, text in scored_texts[:top_k]:
            if text and not pd.isna(text):
                result_texts.append(text)

        return result_texts

    except FileNotFoundError:
        print(f"错误: 找不到文件 {excel_path}")
        return []
    except Exception as e:
        print(f"处理过程中出错: {str(e)}")
        return []


# 示例使用
if __name__ == "__main__":
    # 测试函数
    def test_recall_function():
        """
        测试产品召回功能
        """
        print("=" * 60)
        print("测试产品召回功能")
        print("=" * 60)

        # 测试问题
        test_questions = [
            "我想要理财产品",
            "有什么基金推荐",
            "低风险的投资产品",
            "高收益理财",
            "定期存款"
        ]

        for question in test_questions:
            print(f"\n问题: {question}")
            print("-" * 40)

            # 召回相似产品
            similar_products = recall_similar_products(question, top_k=5)

            if similar_products:
                print("召回的相似产品:")
                for i, product in enumerate(similar_products, 1):
                    print(f"  {i}. {product}")
            else:
                print("没有找到相关产品")

        print("\n" + "=" * 60)
        print("测试完成")

    # 运行测试
    test_recall_function()
