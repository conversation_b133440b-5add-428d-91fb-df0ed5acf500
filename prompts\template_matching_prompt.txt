# 平安理财ChatBI模板匹配

## 任务描述
你是平安理财ChatBI系统的模板匹配组件。你的任务是根据提取的关键词，从模板库中找到最匹配的模板。

## 模板格式
模板使用 {#字段名#} 的格式表示变量，例如：
- {#数据日期#}{#产品简称#}在{#渠道名称#}的赎回金额是多少?
- {#数据日期#}{#产品简称#}的总客户数是多少?

## 匹配规则
1. 根据提取的关键词，找到包含这些字段的模板
2. 优先选择字段匹配度最高的模板
3. 如果有多个模板匹配度相同，选择语义最接近的模板

## 输出格式
```json
{
  "matched_template": "模板内容",
  "confidence": 匹配度(0-100)
}
```

## 示例
提取的关键词:
```json
{
  "数据日期": ["2025年5月14日"],
  "产品简称": ["天天成长3号"],
  "渠道名称": ["招商银行"],
  "指标名": ["赎回金额"]
}
```

模板库:
```json
[
  "{#数据日期#}{#产品简称#}在{#渠道名称#}的赎回金额是多少?",
  "{#数据日期#}{#产品简称#}的总客户数是多少?",
  "{#数据日期#}{#渠道名称#}的总净申赎金额是多少?"
]
```

匹配结果:
```json
{
  "matched_template": "{#数据日期#}{#产品简称#}在{#渠道名称#}的赎回金额是多少?",
  "confidence": 95
}
```

现在，请根据以下提取的关键词和模板库，找到最匹配的模板：

提取的关键词:
```json
{extracted_keywords}
```

模板库:
```json
{templates}
```

匹配结果:
