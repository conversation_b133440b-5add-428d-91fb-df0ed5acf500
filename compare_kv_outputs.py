"""
比对预期KV.xlsx文件中的实际输出列和预期输出列
将两列内容解析为JSON格式，并比较键值对是否一致
"""

import pandas as pd
import json
import re
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("kv_comparison.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("KVComparison")

class KVComparator:
    def __init__(self, excel_file="预期KV.xlsx", sheet_name=None):
        """
        初始化KV比对器

        参数:
            excel_file (str): Excel文件路径
            sheet_name (str): 工作表名称，如果为None则使用第一个工作表
        """
        self.excel_file = excel_file
        self.sheet_name = sheet_name
        self.df = None
        self.comparison_results = []

    def load_excel(self):
        """加载Excel文件"""
        try:
            logger.info(f"正在加载Excel文件: {self.excel_file}")

            # 读取Excel文件
            if self.sheet_name:
                self.df = pd.read_excel(self.excel_file, sheet_name=self.sheet_name)
            else:
                self.df = pd.read_excel(self.excel_file)

            logger.info(f"成功加载Excel文件，共 {len(self.df)} 行数据")
            logger.info(f"列名: {list(self.df.columns)}")

            return True

        except Exception as e:
            logger.error(f"加载Excel文件失败: {str(e)}")
            return False

    def identify_columns(self):
        """识别实际输出列和预期输出列"""
        columns = list(self.df.columns)

        # 尝试自动识别列
        actual_col = None
        expected_col = None

        # 查找包含"实际"、"actual"、"输出"等关键词的列
        for col in columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in ['实际', 'actual', '实际输出', 'output']):
                if '预期' not in col_lower and '期望' not in col_lower:
                    actual_col = col
                    break

        # 查找包含"预期"、"expected"、"期望"等关键词的列
        for col in columns:
            col_lower = str(col).lower()
            if any(keyword in col_lower for keyword in ['预期', 'expected', '期望', '预期输出']):
                expected_col = col
                break

        # 如果自动识别失败，让用户手动选择
        if not actual_col or not expected_col:
            logger.info("自动识别列失败，请手动选择:")
            print("\n可用的列:")
            for i, col in enumerate(columns):
                print(f"{i+1}. {col}")

            if not actual_col:
                while True:
                    try:
                        choice = int(input("请选择实际输出列的编号: ")) - 1
                        if 0 <= choice < len(columns):
                            actual_col = columns[choice]
                            break
                        else:
                            print("无效选择，请重新输入")
                    except ValueError:
                        print("请输入有效的数字")

            if not expected_col:
                while True:
                    try:
                        choice = int(input("请选择预期输出列的编号: ")) - 1
                        if 0 <= choice < len(columns):
                            expected_col = columns[choice]
                            break
                        else:
                            print("无效选择，请重新输入")
                    except ValueError:
                        print("请输入有效的数字")

        logger.info(f"实际输出列: {actual_col}")
        logger.info(f"预期输出列: {expected_col}")

        return actual_col, expected_col

    def extract_json_from_text(self, text):
        """从文本中提取JSON"""
        if pd.isna(text) or not str(text).strip():
            return None

        text = str(text).strip()

        # 方法1：直接尝试解析整个文本
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # 方法2：查找JSON代码块（```json ... ```）
        json_pattern = r'```json\s*(.*?)\s*```'
        match = re.search(json_pattern, text, re.DOTALL | re.IGNORECASE)
        if match:
            try:
                return json.loads(match.group(1))
            except json.JSONDecodeError:
                pass

        # 方法3：查找花括号包围的内容
        brace_pattern = r'\{.*\}'
        match = re.search(brace_pattern, text, re.DOTALL)
        if match:
            try:
                return json.loads(match.group(0))
            except json.JSONDecodeError:
                pass

        # 方法4：尝试修复常见的JSON格式问题
        # 替换单引号为双引号
        fixed_text = text.replace("'", '"')
        try:
            return json.loads(fixed_text)
        except json.JSONDecodeError:
            pass

        # 方法5：尝试提取键值对并构建JSON
        try:
            # 查找类似 "key": "value" 的模式
            kv_pattern = r'"([^"]+)":\s*"([^"]*)"'
            matches = re.findall(kv_pattern, text)
            if matches:
                result = {}
                for key, value in matches:
                    result[key] = value
                return result
        except Exception:
            pass

        logger.warning(f"无法从文本中提取JSON: {text[:100]}...")
        return None

    def normalize_json(self, json_obj):
        """标准化JSON对象，便于比较"""
        if not isinstance(json_obj, dict):
            return json_obj

        normalized = {}
        for key, value in json_obj.items():
            # 标准化键名（去除空格，转换为小写）
            normalized_key = str(key).strip().lower()

            # 标准化值
            if isinstance(value, list):
                # 对列表进行排序和去重
                if value and isinstance(value[0], str):
                    normalized_value = sorted(list(set(v.strip() for v in value if v and str(v).strip())))
                else:
                    normalized_value = sorted(list(set(value)))
            elif isinstance(value, str):
                normalized_value = value.strip()
            else:
                normalized_value = value

            normalized[normalized_key] = normalized_value

        return normalized

    def compare_json_objects(self, actual_json, expected_json):
        """比较两个JSON对象，跳过数据日期键的比较"""
        if actual_json is None and expected_json is None:
            return True, "两者都为空"

        if actual_json is None:
            return False, "实际输出为空"

        if expected_json is None:
            return False, "预期输出为空"

        # 标准化JSON对象
        normalized_actual = self.normalize_json(actual_json)
        normalized_expected = self.normalize_json(expected_json)

        # 定义要跳过比较的键（标准化后的格式）
        skip_keys = {'数据日期', 'datadate', 'date', '日期'}  # 支持多种可能的数据日期键名

        # 过滤掉要跳过的键
        filtered_actual = {k: v for k, v in normalized_actual.items() if k not in skip_keys}
        filtered_expected = {k: v for k, v in normalized_expected.items() if k not in skip_keys}

        # 记录跳过的键
        skipped_keys_actual = [k for k in normalized_actual.keys() if k in skip_keys]
        skipped_keys_expected = [k for k in normalized_expected.keys() if k in skip_keys]

        if skipped_keys_actual or skipped_keys_expected:
            logger.info(f"跳过数据日期键的比较: 实际输出={skipped_keys_actual}, 预期输出={skipped_keys_expected}")

        # 比较过滤后的键
        actual_keys = set(filtered_actual.keys())
        expected_keys = set(filtered_expected.keys())

        missing_keys = expected_keys - actual_keys
        extra_keys = actual_keys - expected_keys

        # 比较共同键的值
        common_keys = actual_keys & expected_keys
        value_differences = []

        for key in common_keys:
            actual_value = filtered_actual[key]
            expected_value = filtered_expected[key]

            if actual_value != expected_value:
                value_differences.append({
                    'key': key,
                    'actual': actual_value,
                    'expected': expected_value
                })

        # 判断是否一致（不考虑数据日期键）
        is_consistent = (len(missing_keys) == 0 and
                        len(extra_keys) == 0 and
                        len(value_differences) == 0)

        # 生成详细说明
        details = []
        if missing_keys:
            details.append(f"缺少键: {list(missing_keys)}")
        if extra_keys:
            details.append(f"多余键: {list(extra_keys)}")
        if value_differences:
            for diff in value_differences:
                details.append(f"键'{diff['key']}'值不同: 实际={diff['actual']}, 预期={diff['expected']}")

        # 添加跳过键的说明
        if skipped_keys_actual or skipped_keys_expected:
            skip_info = f"已跳过数据日期键的比较"
            if details:
                details.append(skip_info)
            else:
                details = [skip_info]

        if is_consistent:
            return True, "一致" + (f" ({details[0]})" if skipped_keys_actual or skipped_keys_expected else "")
        else:
            return False, "; ".join(details)

    def compare_all_rows(self, actual_col, expected_col):
        """比较所有行的数据"""
        logger.info("开始比较所有行的数据...")

        results = []
        consistent_count = 0
        inconsistent_count = 0

        for index, row in self.df.iterrows():
            actual_text = row[actual_col]
            expected_text = row[expected_col]

            logger.info(f"处理第 {index + 1} 行...")

            # 提取JSON
            actual_json = self.extract_json_from_text(actual_text)
            expected_json = self.extract_json_from_text(expected_text)

            # 比较JSON
            is_consistent, details = self.compare_json_objects(actual_json, expected_json)

            result = {
                'row_index': index + 1,
                'actual_text': actual_text,
                'expected_text': expected_text,
                'actual_json': actual_json,
                'expected_json': expected_json,
                'is_consistent': is_consistent,
                'details': details,
                'comparison_result': "一致" if is_consistent else "不一致"
            }

            results.append(result)

            if is_consistent:
                consistent_count += 1
            else:
                inconsistent_count += 1
                logger.warning(f"第 {index + 1} 行不一致: {details}")

        logger.info(f"比较完成: 一致 {consistent_count} 行, 不一致 {inconsistent_count} 行")

        self.comparison_results = results
        return results

    def save_results(self, actual_col, expected_col):
        """保存比较结果到Excel文件"""
        if not self.comparison_results:
            logger.error("没有比较结果可保存")
            return

        # 创建新的DataFrame
        result_df = self.df.copy()

        # 添加新列
        result_df['实际输出JSON'] = [r['actual_json'] for r in self.comparison_results]
        result_df['预期输出JSON'] = [r['expected_json'] for r in self.comparison_results]
        result_df['比较结果'] = [r['comparison_result'] for r in self.comparison_results]
        result_df['详细说明'] = [r['details'] for r in self.comparison_results]

        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"KV比较结果_{timestamp}.xlsx"

        try:
            result_df.to_excel(output_file, index=False)
            logger.info(f"比较结果已保存到: {output_file}")

            # 保存详细的JSON结果
            json_output_file = f"KV比较详细结果_{timestamp}.json"
            with open(json_output_file, 'w', encoding='utf-8') as f:
                json.dump(self.comparison_results, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"详细结果已保存到: {json_output_file}")

        except Exception as e:
            logger.error(f"保存结果失败: {str(e)}")

    def generate_summary_report(self):
        """生成汇总报告"""
        if not self.comparison_results:
            return

        total_rows = len(self.comparison_results)
        consistent_rows = sum(1 for r in self.comparison_results if r['is_consistent'])
        inconsistent_rows = total_rows - consistent_rows

        print(f"\n{'='*60}")
        print("📊 KV比较汇总报告")
        print(f"{'='*60}")
        print(f"总行数: {total_rows}")
        print(f"一致行数: {consistent_rows}")
        print(f"不一致行数: {inconsistent_rows}")
        print(f"一致率: {consistent_rows/total_rows*100:.1f}%")

        if inconsistent_rows > 0:
            print(f"\n❌ 不一致的行:")
            for r in self.comparison_results:
                if not r['is_consistent']:
                    print(f"  第 {r['row_index']} 行: {r['details']}")

        print(f"{'='*60}")

def main():
    """主函数"""
    # 检查文件是否存在
    excel_file = "预期KV.xlsx"
    if not pd.io.common.file_exists(excel_file):
        logger.error(f"文件不存在: {excel_file}")
        return

    # 创建比对器
    comparator = KVComparator(excel_file)

    # 加载Excel文件
    if not comparator.load_excel():
        return

    # 识别列
    actual_col, expected_col = comparator.identify_columns()

    if not actual_col or not expected_col:
        logger.error("无法识别实际输出列和预期输出列")
        return

    # 进行比较
    results = comparator.compare_all_rows(actual_col, expected_col)

    # 保存结果
    comparator.save_results(actual_col, expected_col)

    # 生成汇总报告
    comparator.generate_summary_report()

if __name__ == "__main__":
    main()
