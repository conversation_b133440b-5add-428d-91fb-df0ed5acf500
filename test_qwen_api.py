"""
测试千问API是否可用
"""

import dashscope
from dashscope import Generation

# 设置API密钥
API_KEY = 'sk-4af3f43ae9a74f0ebed6736b453a47c6'
dashscope.api_key = API_KEY

def test_qwen_api():
    """
    测试千问API是否可用
    """
    print("正在测试千问API...")
    
    try:
        # 构建一个简单的提示
        prompt = "请介绍你自己，以及模型参数。"
        
        # 调用千问API
        response = Generation.call(
            model="qwen-max",
            prompt=prompt,
            temperature=0.7,
            max_tokens=100,
            result_format='message'
        )
        
        # 检查响应
        if response.status_code == 200:
            print("API调用成功！")
            print("模型回复:", response.output.choices[0].message.content)
            return True
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            print(f"错误信息: {response.code}: {response.message}")
            return False
    
    except Exception as e:
        print(f"API调用出错: {str(e)}")
        return False

if __name__ == "__main__":
    test_qwen_api()
