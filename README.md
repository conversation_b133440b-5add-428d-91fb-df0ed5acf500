# 平安理财ChatBI问答系统

基于千问(Qwen)大模型的智能问答系统，用于识别用户问题、提取关键词、匹配模板和回填模板。

## 功能特点

- 使用千问(Qwen)大模型识别用户问题并提取关键词
- 将提取的关键词与验证问题清单中的模板进行匹配
- 将关键词回填到匹配的模板中
- 提供命令行和网页两种界面
- 支持历史记录管理
- 包含行业黑话库、业务规则库和元数据库

## 系统要求

- Python 3.7+
- 网络连接（用于调用千问API）

## 安装步骤

1. 克隆或下载本项目到本地
2. 运行安装脚本安装依赖：

```bash
python install_dependencies.py
```

## 使用方法

### 命令行界面

运行以下命令启动命令行界面：

```bash
python chatbi_main.py
```

命令行界面支持以下命令：

- `help`: 显示帮助信息
- `exit`/`quit`: 退出系统
- `debug on`/`off`: 开启/关闭调试模式
- `local on`/`off`: 开启/关闭本地模拟模式
- `clear`: 清屏
- `history`: 显示历史记录

### 网页界面

运行以下命令启动网页界面：

```bash
python web_app.py
```

然后在浏览器中访问：http://localhost:5000

网页界面功能：

- 输入问题并获取回答
- 查看处理结果（提取的关键词、匹配的模板、回填的模板）
- 查看和管理历史记录

## 文件结构

- `chatbi_core.py`: 核心功能实现
- `chatbi_main.py`: 命令行界面
- `web_app.py`: 网页界面
- `split_data.py`: 数据拆分工具
- `knowledge_bases.py`: 知识库创建工具
- `prompt_templates.py`: 提示词模板创建工具
- `templates/`: HTML模板目录
- `static/`: 静态文件目录
- `knowledge_bases/`: 知识库目录
- `prompts/`: 提示词模板目录

## 示例

用户问题：
```
昨天天天成长3号在小招的赎回金额？
```

提取关键词：
```json
{
  "数据日期": ["2025年5月14日"],
  "产品简称": ["天天成长3号"],
  "渠道名称": ["招商银行"],
  "指标名": ["赎回金额"]
}
```

匹配模板：
```
{#数据日期#}{#产品简称#}在{#渠道名称#}的赎回金额是多少?
```

回填模板：
```
2025年5月14日天天成长3号在招商银行的赎回金额是多少?
```

## 配置

系统使用千问(Qwen)API进行问题识别和处理。API密钥配置在`chatbi_core.py`文件中：

```python
# 设置API密钥
API_KEY = 'sk-4af3f43ae9a74f0ebed6736b453a47c6'
dashscope.api_key = API_KEY
```

## 许可证

本项目仅供学习和研究使用。
