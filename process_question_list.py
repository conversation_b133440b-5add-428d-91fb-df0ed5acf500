"""
批量处理问题清单脚本
从Excel文件的第一列提取问题，调用chatbi_core的extract_keywords_with_output函数，
将结果保存到第二列
"""

import pandas as pd
import json
import time
import logging
from datetime import datetime
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入chatbi_core模块
from chatbi_core import (
    extract_keywords_with_output,
    extract_keywords_zhibiao,
    extract_keywords_weidu,
    extract_keywords_time
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("process_question_list.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("QuestionProcessor")

class QuestionProcessor:
    def __init__(self, excel_file="问题清单.xlsx", sheet_name=None):
        """
        初始化问题处理器

        参数:
            excel_file (str): Excel文件路径
            sheet_name (str): 工作表名称，如果为None则使用第一个工作表
        """
        self.excel_file = excel_file
        self.sheet_name = sheet_name
        self.df = None
        self.processed_count = 0
        self.failed_count = 0
        self.results = []

    def load_excel(self):
        """加载Excel文件"""
        try:
            logger.info(f"正在加载Excel文件: {self.excel_file}")

            # 读取Excel文件
            if self.sheet_name:
                self.df = pd.read_excel(self.excel_file, sheet_name=self.sheet_name)
            else:
                self.df = pd.read_excel(self.excel_file)

            logger.info(f"成功加载Excel文件，共 {len(self.df)} 行数据")
            logger.info(f"列名: {list(self.df.columns)}")

            # 检查是否有足够的列，需要5列：问题、维度、指标、时间、合并输出
            required_columns = ['问题', '维度关键词', '指标关键词', '时间关键词', '合并关键词']

            # 如果列数不足，添加缺失的列
            current_columns = list(self.df.columns)

            # 确保第一列是问题列
            if len(current_columns) >= 1:
                current_columns[0] = '问题'

            # 添加缺失的列
            for i, col_name in enumerate(required_columns):
                if i >= len(current_columns):
                    self.df[col_name] = ''
                elif i > 0:  # 第一列保持原有数据，其他列重命名
                    if i < len(current_columns):
                        current_columns[i] = col_name

            # 重命名列
            if len(current_columns) >= len(required_columns):
                rename_dict = {old: new for old, new in zip(self.df.columns[:len(required_columns)], required_columns)}
                self.df.rename(columns=rename_dict, inplace=True)

            logger.info(f"Excel列结构: {list(self.df.columns)}")

            return True

        except Exception as e:
            logger.error(f"加载Excel文件失败: {str(e)}")
            return False

    def get_questions_from_first_column(self):
        """从第一列获取所有问题"""
        if self.df is None:
            logger.error("Excel文件未加载")
            return []

        # 获取第一列的所有非空值
        first_column = self.df.iloc[:, 0]
        questions = []

        for idx, question in enumerate(first_column):
            if pd.notna(question) and str(question).strip():
                questions.append({
                    'index': idx,
                    'question': str(question).strip()
                })

        logger.info(f"从第一列提取到 {len(questions)} 个有效问题")
        return questions

    def process_single_question(self, question_data):
        """处理单个问题"""
        question = question_data['question']
        index = question_data['index']

        logger.info(f"处理问题 {index + 1}: {question}")

        try:
            # 分别调用三个提取函数
            start_time = time.time()

            logger.info(f"开始处理问题 {index + 1}: {question}")

            # 1. 提取维度关键词
            logger.info("提取维度关键词...")
            keywords_weidu = extract_keywords_weidu(question)

            # 2. 提取指标关键词
            logger.info("提取指标关键词...")
            keywords_zhibiao = extract_keywords_zhibiao(question)

            # 3. 提取时间关键词
            logger.info("提取时间关键词...")
            keywords_time = extract_keywords_time(question)

            # 4. 获取合并结果
            logger.info("获取合并关键词...")
            merged_keywords, model_output = extract_keywords_with_output(question)

            processing_time = time.time() - start_time

            # 准备结果数据
            result = {
                'index': index,
                'question': question,
                'keywords_weidu': keywords_weidu,
                'keywords_zhibiao': keywords_zhibiao,
                'keywords_time': keywords_time,
                'merged_keywords': merged_keywords,
                'model_output': model_output,
                'processing_time': processing_time,
                'success': True,
                'error': None
            }

            # 将结果转换为字符串格式保存到Excel的不同列
            def format_keywords(keywords):
                if keywords:
                    return json.dumps(keywords, ensure_ascii=False, indent=2)
                else:
                    return "提取失败"

            # 更新DataFrame的各列
            self.df.iloc[index, 1] = format_keywords(keywords_weidu)    # 维度关键词
            self.df.iloc[index, 2] = format_keywords(keywords_zhibiao)  # 指标关键词
            self.df.iloc[index, 3] = format_keywords(keywords_time)     # 时间关键词
            self.df.iloc[index, 4] = format_keywords(merged_keywords)   # 合并关键词

            logger.info(f"问题 {index + 1} 处理成功，耗时 {processing_time:.2f}秒")
            logger.info(f"维度关键词: {len(keywords_weidu) if keywords_weidu else 0} 个字段")
            logger.info(f"指标关键词: {len(keywords_zhibiao) if keywords_zhibiao else 0} 个字段")
            logger.info(f"时间关键词: {len(keywords_time) if keywords_time else 0} 个字段")
            logger.info(f"合并关键词: {len(merged_keywords) if merged_keywords else 0} 个字段")

            self.processed_count += 1
            return result

        except Exception as e:
            logger.error(f"处理问题 {index + 1} 失败: {str(e)}")

            # 记录失败结果
            result = {
                'index': index,
                'question': question,
                'keywords_weidu': None,
                'keywords_zhibiao': None,
                'keywords_time': None,
                'merged_keywords': None,
                'model_output': None,
                'processing_time': 0,
                'success': False,
                'error': str(e)
            }

            # 在Excel中标记失败（所有列）
            error_msg = f"处理失败: {str(e)}"
            self.df.iloc[index, 1] = error_msg  # 维度关键词
            self.df.iloc[index, 2] = error_msg  # 指标关键词
            self.df.iloc[index, 3] = error_msg  # 时间关键词
            self.df.iloc[index, 4] = error_msg  # 合并关键词

            self.failed_count += 1
            return result

    def process_all_questions(self, start_index=0, max_questions=None, save_interval=5):
        """
        处理所有问题

        参数:
            start_index (int): 开始处理的索引
            max_questions (int): 最大处理问题数，None表示处理所有
            save_interval (int): 每处理多少个问题保存一次
        """
        questions = self.get_questions_from_first_column()

        if not questions:
            logger.error("没有找到有效的问题")
            return

        # 应用开始索引和最大问题数限制
        if start_index > 0:
            questions = questions[start_index:]
            logger.info(f"从索引 {start_index} 开始处理")

        if max_questions:
            questions = questions[:max_questions]
            logger.info(f"限制处理 {max_questions} 个问题")

        total_questions = len(questions)
        logger.info(f"开始批量处理 {total_questions} 个问题")

        start_time = time.time()

        for i, question_data in enumerate(questions):
            logger.info(f"\n{'='*60}")
            logger.info(f"进度: {i+1}/{total_questions}")

            # 处理单个问题
            result = self.process_single_question(question_data)
            self.results.append(result)

            # 定期保存
            if (i + 1) % save_interval == 0:
                self.save_excel()
                logger.info(f"已保存进度，完成 {i+1}/{total_questions} 个问题")

            # 添加延迟避免API限制
            if i < total_questions - 1:  # 最后一个问题不需要延迟
                time.sleep(1)  # 1秒延迟

        # 最终保存
        self.save_excel()

        total_time = time.time() - start_time
        logger.info(f"\n{'='*60}")
        logger.info(f"批量处理完成！")
        logger.info(f"总问题数: {total_questions}")
        logger.info(f"成功处理: {self.processed_count}")
        logger.info(f"处理失败: {self.failed_count}")
        logger.info(f"总耗时: {total_time:.2f}秒")
        logger.info(f"平均耗时: {total_time/total_questions:.2f}秒/问题")

        # 保存详细结果到JSON文件
        self.save_detailed_results()

    def save_excel(self):
        """保存Excel文件"""
        try:
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"问题清单_处理结果_{timestamp}.xlsx"

            # 保存到新文件
            self.df.to_excel(output_file, index=False)
            logger.info(f"Excel文件已保存: {output_file}")

            # 同时覆盖原文件（可选）
            # self.df.to_excel(self.excel_file, index=False)

        except Exception as e:
            logger.error(f"保存Excel文件失败: {str(e)}")

    def save_detailed_results(self):
        """保存详细结果到JSON文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            json_file = f"问题处理详细结果_{timestamp}.json"

            # 准备保存的数据
            save_data = {
                'processing_info': {
                    'total_questions': len(self.results),
                    'successful': self.processed_count,
                    'failed': self.failed_count,
                    'processing_time': datetime.now().isoformat()
                },
                'results': self.results
            }

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)

            logger.info(f"详细结果已保存: {json_file}")

        except Exception as e:
            logger.error(f"保存详细结果失败: {str(e)}")

    def preview_questions(self, num_questions=5):
        """预览前几个问题"""
        questions = self.get_questions_from_first_column()

        logger.info(f"预览前 {min(num_questions, len(questions))} 个问题:")
        for i, question_data in enumerate(questions[:num_questions]):
            logger.info(f"{i+1}. {question_data['question']}")

def main():
    """主函数"""
    # 检查Excel文件是否存在
    excel_files = ["问题清单.xlsx", "AI验证问题清单.xlsx", "模板清单.xlsx"]

    target_file = None
    for file in excel_files:
        if os.path.exists(file):
            target_file = file
            break

    if not target_file:
        logger.error(f"未找到Excel文件，请确保以下文件之一存在: {excel_files}")
        return

    logger.info(f"使用Excel文件: {target_file}")

    # 创建处理器
    processor = QuestionProcessor(target_file)

    # 加载Excel文件
    if not processor.load_excel():
        return

    # 预览问题
    processor.preview_questions()

    # 询问用户是否继续
    print("\n" + "="*60)
    print("即将开始批量处理问题，这可能需要较长时间...")
    print("每个问题大约需要3-10秒处理时间")

    user_input = input("是否继续？(y/n): ").lower().strip()
    if user_input != 'y':
        logger.info("用户取消操作")
        return

    # 询问处理参数
    try:
        start_index = int(input("开始索引 (默认0): ") or "0")
        max_questions_input = input("最大处理问题数 (默认全部): ").strip()
        max_questions = int(max_questions_input) if max_questions_input else None
        save_interval = int(input("保存间隔 (默认5): ") or "5")
    except ValueError:
        logger.error("输入参数无效，使用默认值")
        start_index = 0
        max_questions = None
        save_interval = 5

    # 开始处理
    processor.process_all_questions(
        start_index=start_index,
        max_questions=max_questions,
        save_interval=save_interval
    )

if __name__ == "__main__":
    main()
