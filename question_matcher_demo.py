"""
问题规则匹配模块使用示例
演示如何使用question_rule_matcher模块
"""

from question_rule_matcher import QuestionRuleMatcher, match_question_simple

def demo_basic_usage():
    """基本使用示例"""
    print("🚀 基本使用示例")
    print("=" * 50)
    
    # 示例问题
    question = "平安理财启元策略日开270天持有4号固收类理财产品B的规模是多少？"
    
    # 方法1：使用简化函数
    result = match_question_simple(question)
    print(f"问题: {question}")
    print(f"匹配结果: {result}")
    
    # 方法2：使用类实例
    matcher = QuestionRuleMatcher()
    detailed_result = matcher.match_question_detailed(question)
    
    print(f"\n详细结果:")
    print(f"匹配项数: {detailed_result['match_count']}")
    print(f"匹配率: {detailed_result['match_rate']:.1%}")
    print(f"匹配内容: {detailed_result['matches']}")

def demo_multiple_questions():
    """批量问题匹配示例"""
    print("\n🔍 批量问题匹配示例")
    print("=" * 50)
    
    questions = [
        "昨天天天成长3号在小招的赎回金额？",
        "新启航三个月定开1号产品净申赎金额是多少？",
        "固定收益类的持仓规模是多少?"
    ]
    
    matcher = QuestionRuleMatcher()
    
    for i, question in enumerate(questions, 1):
        result = matcher.match_question(question)
        print(f"\n问题 {i}: {question}")
        if result:
            for key, value in result.items():
                print(f"  {key}: {value}")
        else:
            print("  未找到匹配项")

def demo_category_exploration():
    """类别探索示例"""
    print("\n📊 类别探索示例")
    print("=" * 50)
    
    matcher = QuestionRuleMatcher()
    
    # 获取所有类别
    categories = matcher.get_all_categories()
    print(f"所有类别: {categories}")
    
    # 查看特定类别的值
    if "产品系列" in categories:
        product_series = matcher.get_category_values("产品系列")
        print(f"\n产品系列 (前10个): {product_series[:10]}")
    
    # 搜索包含特定关键词的值
    search_results = matcher.search_value_in_category("天天成长")
    print(f"\n包含'天天成长'的项目:")
    for category, value in search_results[:5]:  # 只显示前5个
        print(f"  {category}: {value}")

def demo_api_functions():
    """API函数演示"""
    print("\n🛠️ API函数演示")
    print("=" * 50)
    
    # 创建匹配器实例
    matcher = QuestionRuleMatcher("规则测试.xlsx")
    
    # 测试问题
    test_question = "平安理财启元策略日开270天持有4号固收类理财产品B的规模是多少？"
    
    print(f"测试问题: {test_question}")
    print()
    
    # 1. 基本匹配
    print("1. 基本匹配:")
    matches = matcher.match_question(test_question)
    for key, value in matches.items():
        print(f"   {key}: {value}")
    
    # 2. 详细匹配
    print("\n2. 详细匹配:")
    detailed = matcher.match_question_detailed(test_question)
    print(f"   匹配率: {detailed['match_rate']:.1%}")
    print(f"   未匹配类别: {detailed['unmatched_categories']}")
    
    # 3. 类别查询
    print("\n3. 类别查询:")
    all_cats = matcher.get_all_categories()
    print(f"   总类别数: {len(all_cats)}")
    print(f"   类别列表: {all_cats}")
    
    # 4. 值搜索
    print("\n4. 值搜索:")
    search_results = matcher.search_value_in_category("启元策略")
    print(f"   包含'启元策略'的项目数: {len(search_results)}")
    for cat, val in search_results[:3]:
        print(f"   {cat}: {val}")
    
    # 5. 规则摘要
    print("\n5. 规则摘要:")
    summary = matcher.export_rules_summary()
    print(f"   总类别数: {summary['total_categories']}")
    for cat, info in list(summary['categories'].items())[:3]:
        print(f"   {cat}: {info['count']} 个元素")

def demo_real_world_usage():
    """实际应用场景示例"""
    print("\n🌟 实际应用场景示例")
    print("=" * 50)
    
    # 模拟用户输入的各种问题
    user_questions = [
        "我想查询天天成长3号昨天的赎回情况",
        "启元策略日开270天持有4号产品的最新规模",
        "新启航系列产品的净申赎数据",
        "固定收益类产品在各渠道的分布情况"
    ]
    
    matcher = QuestionRuleMatcher()
    
    for question in user_questions:
        print(f"\n用户问题: {question}")
        
        # 匹配规则
        matches = matcher.match_question(question)
        
        if matches:
            print("识别到的实体:")
            for entity_type, entity_value in matches.items():
                print(f"  {entity_type}: {entity_value}")
            
            # 模拟后续处理
            print("可以进行的操作:")
            if "产品名称" in matches or "产品简称" in matches:
                print("  - 查询产品详细信息")
                print("  - 获取产品历史数据")
            if "渠道名称" in matches:
                print("  - 查询渠道相关数据")
            if "组合名称" in matches or "组合简称" in matches:
                print("  - 查询组合投资情况")
        else:
            print("未识别到具体实体，可能需要:")
            print("  - 用户进一步明确问题")
            print("  - 使用通用查询逻辑")

def main():
    """主演示函数"""
    print("🎯 问题规则匹配模块演示")
    print("=" * 80)
    
    try:
        # 基本使用
        demo_basic_usage()
        
        # 批量匹配
        demo_multiple_questions()
        
        # 类别探索
        demo_category_exploration()
        
        # API函数演示
        demo_api_functions()
        
        # 实际应用场景
        demo_real_world_usage()
        
        print("\n" + "=" * 80)
        print("🎉 演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
