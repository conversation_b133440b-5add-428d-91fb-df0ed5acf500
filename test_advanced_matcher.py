"""
测试高级DataFrame匹配器
"""

import pandas as pd
import logging
from dataframe_matcher import DataFrameMatcher, search_dataframe, get_best_dataframe_match

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def create_test_dataframes():
    """
    创建测试用的DataFrame
    """
    # 基础测试数据
    basic_data = {
        '产品代码': ['P001', 'P002', 'P003', 'P004', 'P005'],
        '产品名称': ['天天成长3号', '新启航三个月定开4号A', '添利90天', '稳利多6号', '灵活成长28天'],
        '产品别名': ['天天成长', '启航', '添利', '稳利', '灵活成长']
    }
    
    # 复杂测试数据
    complex_data = {
        '产品代码': ['PATF001', 'PATF002', 'PATF003', 'PATF004', 'PATF005', 'PATF006'],
        '产品全称': [
            '平安理财天天成长3号现金管理类理财产品',
            '平安理财新启航三个月定开4号A混合类理财产品',
            '平安理财添利90天固定收益类理财产品',
            '平安理财稳利多6号固收+理财产品',
            '平安理财灵活成长28天持有固收类理财产品',
            '平安理财智慧增长1年期权益类理财产品'
        ],
        '产品简称': ['天天成长3号', '新启航三个月定开4号A', '添利90天', '稳利多6号', '灵活成长28天', '智慧增长1年期']
    }
    
    return pd.DataFrame(basic_data), pd.DataFrame(complex_data)

def test_exact_matching():
    """
    测试精确匹配
    """
    print("=" * 60)
    print("测试精确匹配")
    print("=" * 60)
    
    basic_df, complex_df = create_test_dataframes()
    matcher = DataFrameMatcher()
    
    test_cases = [
        ("天天成长3号的持仓规模？", basic_df),
        ("启航产品的申购金额？", basic_df),
        ("平安理财添利90天固定收益类理财产品的客户数？", complex_df),
        ("智慧增长1年期的净申赎金额？", complex_df)
    ]
    
    for question, df in test_cases:
        print(f"\n问题: {question}")
        print(f"DataFrame: {df.columns.tolist()}")
        
        matches = matcher.search_matches(df, question, exact_match=True, fuzzy_threshold=0.0)
        print(f"精确匹配结果: {matches}")

def test_fuzzy_matching():
    """
    测试模糊匹配
    """
    print("\n" + "=" * 60)
    print("测试模糊匹配")
    print("=" * 60)
    
    basic_df, complex_df = create_test_dataframes()
    matcher = DataFrameMatcher()
    
    test_cases = [
        ("天天成长的规模", basic_df, 0.5),
        ("启航产品", basic_df, 0.4),
        ("添利产品", basic_df, 0.3),
        ("新启航三个月", complex_df, 0.6),
        ("智慧增长", complex_df, 0.5),
        ("稳利多", complex_df, 0.4)
    ]
    
    for question, df, threshold in test_cases:
        print(f"\n问题: {question} (阈值: {threshold})")
        
        matches = matcher.search_matches(df, question, exact_match=False, fuzzy_threshold=threshold)
        print(f"模糊匹配结果: {matches}")

def test_combined_matching():
    """
    测试组合匹配（精确+模糊）
    """
    print("\n" + "=" * 60)
    print("测试组合匹配（精确+模糊）")
    print("=" * 60)
    
    basic_df, complex_df = create_test_dataframes()
    matcher = DataFrameMatcher()
    
    test_cases = [
        ("天天成长3号", basic_df, 0.6),
        ("启航", basic_df, 0.5),
        ("添利90", complex_df, 0.4),
        ("稳利多6", complex_df, 0.5)
    ]
    
    for question, df, threshold in test_cases:
        print(f"\n问题: {question} (阈值: {threshold})")
        
        matches = matcher.search_matches(df, question, exact_match=True, fuzzy_threshold=threshold)
        print(f"组合匹配结果: {matches}")

def test_best_match():
    """
    测试最佳匹配
    """
    print("\n" + "=" * 60)
    print("测试最佳匹配")
    print("=" * 60)
    
    basic_df, complex_df = create_test_dataframes()
    matcher = DataFrameMatcher()
    
    test_cases = [
        ("天天成长", basic_df, 0.5),
        ("启航产品", basic_df, 0.4),
        ("添利", complex_df, 0.3),
        ("智慧增长", complex_df, 0.5),
        ("不存在的产品", basic_df, 0.5)
    ]
    
    for question, df, threshold in test_cases:
        print(f"\n问题: {question} (阈值: {threshold})")
        
        best_match = matcher.get_best_match(df, question, threshold)
        print(f"最佳匹配: {best_match}")

def test_convenience_functions():
    """
    测试便捷函数
    """
    print("\n" + "=" * 60)
    print("测试便捷函数")
    print("=" * 60)
    
    basic_df, _ = create_test_dataframes()
    
    # 测试便捷搜索函数
    print("\n使用便捷搜索函数:")
    question = "天天成长3号"
    matches = search_dataframe(basic_df, question, exact_match=True, fuzzy_threshold=0.5)
    print(f"问题: {question}")
    print(f"搜索结果: {matches}")
    
    # 测试最佳匹配便捷函数
    print("\n使用最佳匹配便捷函数:")
    question = "启航"
    best_match = get_best_dataframe_match(basic_df, question, fuzzy_threshold=0.4)
    print(f"问题: {question}")
    print(f"最佳匹配: {best_match}")

def test_edge_cases():
    """
    测试边界情况
    """
    print("\n" + "=" * 60)
    print("测试边界情况")
    print("=" * 60)
    
    basic_df, _ = create_test_dataframes()
    matcher = DataFrameMatcher()
    
    # 空DataFrame
    empty_df = pd.DataFrame()
    print("\n测试空DataFrame:")
    matches = matcher.search_matches(empty_df, "测试问题")
    print(f"结果: {matches}")
    
    # 空问题
    print("\n测试空问题:")
    matches = matcher.search_matches(basic_df, "")
    print(f"结果: {matches}")
    
    # 列数不足的DataFrame
    insufficient_df = pd.DataFrame({'col1': [1, 2], 'col2': [3, 4]})
    print("\n测试列数不足的DataFrame:")
    matches = matcher.search_matches(insufficient_df, "测试问题")
    print(f"结果: {matches}")
    
    # 包含NaN值的DataFrame
    nan_data = {
        '产品代码': ['P001', 'P002', 'P003'],
        '产品名称': ['天天成长3号', None, '添利90天'],
        '产品别名': ['天天成长', '', '添利']
    }
    nan_df = pd.DataFrame(nan_data)
    print("\n测试包含NaN值的DataFrame:")
    matches = matcher.search_matches(nan_df, "天天成长")
    print(f"结果: {matches}")

def main():
    """
    主测试函数
    """
    print("开始测试高级DataFrame匹配器")
    
    test_exact_matching()
    test_fuzzy_matching()
    test_combined_matching()
    test_best_match()
    test_convenience_functions()
    test_edge_cases()
    
    print("\n" + "=" * 60)
    print("所有测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
