# KV比对工具使用说明

## 📋 工具概述

本工具包含两个KV比对脚本，用于比较Excel文件中实际输出列和预期输出列的JSON数据是否一致。

## 🛠️ 工具列表

### 1. `compare_kv_outputs.py` - 完整版比对工具

**功能特点：**
- 支持多种JSON提取方法
- 详细的差异分析
- 完整的日志记录
- 支持手动选择列
- 生成详细的比对报告

**使用方法：**
```bash
python compare_kv_outputs.py
```

**输出文件：**
- `KV比较结果_时间戳.xlsx` - 包含比对结果的Excel文件
- `KV比较详细结果_时间戳.json` - 详细的JSON格式结果
- `kv_comparison.log` - 详细的日志文件

### 2. `quick_kv_compare.py` - 快速比对工具

**功能特点：**
- 快速执行，适合日常使用
- 自动识别列
- 简洁的输出结果
- 支持命令行参数

**使用方法：**
```bash
# 自动查找文件
python quick_kv_compare.py

# 指定文件
python quick_kv_compare.py 文件名.xlsx
```

**输出文件：**
- `快速比对结果_时间戳.xlsx` - 包含比对结果的Excel文件

## 📊 输入文件格式要求

### Excel文件结构
文件应包含至少3列：
1. **第1列**：用户问题（可选，用于参考）
2. **第2列**：实际输出（JSON格式的字符串）
3. **第3列**：预期输出（JSON格式的字符串）

### 支持的JSON格式
工具支持多种JSON格式：

1. **标准JSON格式：**
```json
{"数据日期": ["20250528"], "产品简称": ["天天成长3号"], "渠道名称": ["招商银行"], "指标名": ["赎回金额"]}
```

2. **代码块格式：**
```
```json
{"数据日期": ["20250528"], "产品简称": ["天天成长3号"]}
```
```

3. **嵌入在文本中的JSON：**
```
根据分析，提取的关键词为：{"数据日期": ["20250528"], "产品简称": ["天天成长3号"]}
```

## 🔍 比对逻辑

### 1. JSON提取
- 自动从文本中提取JSON内容
- 支持多种JSON格式和包装方式
- 处理常见的格式问题（如单引号转双引号）

### 2. 数据标准化
- **键名标准化**：去除空格，转换为小写
- **值标准化**：
  - 列表：排序并去重
  - 字符串：去除首尾空格
  - 保持其他类型不变

### 3. 比较规则
- **键比较**：检查缺失键和多余键
- **值比较**：逐个比较相同键的值
- **结果判定**：只有键和值都完全一致才判定为"一致"

## 📈 输出结果说明

### 比对结果列
- **一致**：实际输出和预期输出的JSON完全匹配
- **不一致**：存在键或值的差异

### 详细说明列（完整版）
提供具体的差异信息：
- `缺少键: [键名列表]` - 实际输出中缺少的键
- `多余键: [键名列表]` - 实际输出中多出的键
- `键'xxx'值不同` - 特定键的值不匹配

### 统计信息
- **总行数**：处理的数据行数
- **一致行数**：JSON完全匹配的行数
- **不一致行数**：存在差异的行数
- **一致率**：一致行数占总行数的百分比

## 🎯 使用示例

### 示例1：使用快速比对工具
```bash
# 当前目录有预期KV.xlsx文件
python quick_kv_compare.py

# 输出：
# 🚀 开始快速比对: 预期KV.xlsx
# ✅ 成功读取文件，共 545 行
# 📊 使用列: 实际输出='输出', 预期输出='输出.1'
# 🔍 开始比对...
# ❌ 第 3 行不一致: 键'数据日期'值不同
# 📊 比对完成!
# 总行数: 545
# 一致: 544
# 不一致: 1
# 一致率: 99.8%
```

### 示例2：使用完整版比对工具
```bash
python compare_kv_outputs.py

# 会提示选择列：
# 可用的列:
# 1. 用户问题
# 2. 输出
# 3. 输出.1
# 请选择实际输出列的编号: 2
# 请选择预期输出列的编号: 3
```

## ⚠️ 注意事项

1. **文件格式**：确保Excel文件格式正确，建议使用.xlsx格式
2. **JSON格式**：确保JSON数据格式正确，避免语法错误
3. **编码问题**：文件应使用UTF-8编码，避免中文乱码
4. **内存使用**：大文件可能消耗较多内存，建议分批处理
5. **备份数据**：比对前建议备份原始数据

## 🔧 故障排除

### 常见问题

1. **文件读取失败**
   - 检查文件是否存在
   - 确认文件格式是否正确
   - 检查文件是否被其他程序占用

2. **JSON解析失败**
   - 检查JSON格式是否正确
   - 确认引号使用是否规范
   - 查看是否有特殊字符

3. **列识别错误**
   - 使用完整版工具手动选择列
   - 检查列名是否包含特殊字符

4. **比对结果异常**
   - 检查数据是否包含空值
   - 确认JSON结构是否一致
   - 查看详细日志了解具体问题

## 📞 技术支持

如遇到问题，请检查：
1. Python环境是否正确安装
2. 必要的依赖包是否安装（pandas, openpyxl）
3. 文件权限是否足够
4. 查看生成的日志文件获取详细错误信息

---

**版本信息：**
- 完整版比对工具：v1.0
- 快速比对工具：v1.0
- 最后更新：2025-05-28
