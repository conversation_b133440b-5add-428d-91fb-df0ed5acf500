"""
测试新的模板格式
"""

import json
import os
from chatbi_core import extract_keywords, match_template, fill_template

def test_question(user_question):
    """
    测试特定问题
    
    参数:
        user_question (str): 用户问题
    """
    print(f"测试问题: {user_question}")
    
    # 1. 提取关键词
    extracted_keywords = extract_keywords(user_question)
    if not extracted_keywords:
        print("提取关键词失败")
        return
    
    # 2. 匹配模板
    matched_template = match_template(extracted_keywords)
    if not matched_template:
        print("匹配模板失败")
        return
    
    # 3. 填充模板
    filled_template = fill_template(matched_template, extracted_keywords)
    if not filled_template:
        print("填充模板失败")
        return
    
    # 4. 打印结果
    print("\n" + "=" * 50)
    print(f"用户问题: {user_question}")
    print(f"提取关键词: {json.dumps(extracted_keywords, ensure_ascii=False, indent=2)}")
    print(f"匹配模板: {matched_template}")
    print(f"填充模板: {filled_template}")
    print("=" * 50)

if __name__ == "__main__":
    # 关闭本地模拟模式
    os.environ["USE_LOCAL_SIMULATION"] = "0"
    
    # 测试特定问题
    test_question("今天招商银行渠道的净申赎金额是多少?")
