"""
产品召回功能使用示例
"""

from product_recommender import recall_similar_products


def main():
    """
    演示如何使用产品召回功能
    """
    print("=" * 60)
    print("产品召回功能使用示例")
    print("=" * 60)

    # 基本使用方法
    print("基本使用方法:")
    print("-" * 30)

    user_question = "我想要低风险的理财产品"
    print(f"用户问题: {user_question}")

    # 调用召回函数，返回最相似的100个产品内容列表
    similar_products = recall_similar_products(
        user_question=user_question,
        excel_path="../knowledge_bases/产品库.xlsx",
        top_k=100  # 召回最相似的100个产品
    )

    print(f"召回的产品数量: {len(similar_products)}")
    print(f"前5个最相似的产品:")
    for i, product in enumerate(similar_products[:5], 1):
        print(f"  {i}. {product}")

    print("\n" + "=" * 60)

    # 测试不同的问题
    print("测试不同的问题:")
    print("-" * 30)

    test_questions = [
        "高收益投资产品",
        "基金推荐",
        "现金管理产品",
        "定期理财",
        "低风险投资"
    ]

    for question in test_questions:
        print(f"\n问题: {question}")

        # 召回前3个最相似的产品
        results = recall_similar_products(question, "../knowledge_bases/产品库.xlsx", top_k=3)

        print("召回结果:")
        for i, product in enumerate(results, 1):
            print(f"  {i}. {product}")

    print("\n" + "=" * 60)

    # 演示不同的top_k值
    print("演示不同的top_k值:")
    print("-" * 30)

    question = "理财产品"
    top_k_values = [5, 10, 50, 100]

    for top_k in top_k_values:
        results = recall_similar_products(question, "../knowledge_bases/产品库.xlsx", top_k=top_k)
        print(f"top_k={top_k}: 召回 {len(results)} 个产品")

    print("\n" + "=" * 60)
    print("使用示例完成!")
    print("\n使用说明:")
    print("1. 函数名: recall_similar_products")
    print("2. 参数:")
    print("   - user_question: 用户问题 (必需)")
    print("   - excel_path: 产品库Excel文件路径 (可选，默认'knowledge_bases/产品库.xlsx')")
    print("   - top_k: 返回最相似的前k个产品 (可选，默认100)")
    print("3. 返回: List[str] - 最相似的产品内容列表")


if __name__ == "__main__":
    main()
