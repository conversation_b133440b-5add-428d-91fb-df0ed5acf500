"""
问题规则匹配模块简单使用示例
"""

from question_rule_matcher import match_question_simple, QuestionRuleMatcher

def example_1_basic_usage():
    """示例1：基本使用"""
    print("示例1：基本使用")
    print("-" * 30)
    
    question = "平安理财启元策略日开270天持有4号固收类理财产品B的规模是多少？"
    result = match_question_simple(question)
    
    print(f"问题: {question}")
    print("匹配结果:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    print()

def example_2_multiple_questions():
    """示例2：批量处理多个问题"""
    print("示例2：批量处理多个问题")
    print("-" * 30)
    
    questions = [
        "昨天天天成长3号在小招的赎回金额？",
        "新启航三个月定开1号产品净申赎金额是多少？",
        "固定收益类的持仓规模是多少?"
    ]
    
    for i, question in enumerate(questions, 1):
        result = match_question_simple(question)
        print(f"问题{i}: {question}")
        if result:
            print("匹配结果:")
            for key, value in result.items():
                print(f"  {key}: {value}")
        else:
            print("  未找到匹配项")
        print()

def example_3_detailed_analysis():
    """示例3：详细分析"""
    print("示例3：详细分析")
    print("-" * 30)
    
    matcher = QuestionRuleMatcher()
    question = "天天成长3号的赎回金额"
    
    # 获取详细结果
    detailed_result = matcher.match_question_detailed(question)
    
    print(f"问题: {question}")
    print(f"匹配率: {detailed_result['match_rate']:.1%}")
    print(f"匹配项数: {detailed_result['match_count']}/{detailed_result['total_categories']}")
    print("匹配结果:")
    for key, value in detailed_result['matches'].items():
        print(f"  {key}: {value}")
    
    if detailed_result['unmatched_categories']:
        print("未匹配的类别:")
        for category in detailed_result['unmatched_categories']:
            print(f"  - {category}")
    
    print()

def example_4_search_entities():
    """示例4：搜索实体"""
    print("示例4：搜索实体")
    print("-" * 30)
    
    matcher = QuestionRuleMatcher()
    
    # 搜索包含"天天成长"的实体
    search_results = matcher.search_value_in_category("天天成长")
    
    print("包含'天天成长'的实体（前10个）:")
    for category, value in search_results[:10]:
        print(f"  {category}: {value}")
    
    print(f"\n总共找到 {len(search_results)} 个相关实体")
    print()

def example_5_category_exploration():
    """示例5：类别探索"""
    print("示例5：类别探索")
    print("-" * 30)
    
    matcher = QuestionRuleMatcher()
    
    # 获取所有类别
    categories = matcher.get_all_categories()
    print("所有可用类别:")
    for category in categories:
        count = len(matcher.get_category_values(category))
        print(f"  {category}: {count} 个元素")
    
    # 查看产品系列的具体值
    if "产品系列" in categories:
        product_series = matcher.get_category_values("产品系列")
        print(f"\n产品系列列表:")
        for series in product_series:
            print(f"  - {series}")
    
    print()

def main():
    """主函数"""
    print("🚀 问题规则匹配模块使用示例")
    print("=" * 50)
    
    try:
        example_1_basic_usage()
        example_2_multiple_questions()
        example_3_detailed_analysis()
        example_4_search_entities()
        example_5_category_exploration()
        
        print("🎉 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 运行示例时出现错误: {str(e)}")

if __name__ == "__main__":
    main()
