"""
简单的运行脚本
"""

try:
    from generate_random_names import ProductNameGenerator

    print("🚀 开始运行产品库随机名称生成器")
    print("=" * 50)

    # 创建生成器
    generator = ProductNameGenerator("prod_reco/产品库测试.xlsx")

    # 加载Excel文件
    print("📁 正在加载Excel文件...")
    if not generator.load_excel():
        print("❌ 加载Excel文件失败")
        exit(1)

    print("✅ Excel文件加载成功")
    print(f"数据行数: {len(generator.df)}")
    print(f"列名: {list(generator.df.columns)}")

    # 预览前5行
    print("\n📊 数据预览:")
    for i in range(min(5, len(generator.df))):
        if len(generator.df.columns) >= 2:
            real_value = generator.df.iloc[i, 1]
            print(f"  {i+1}. {real_value}")

    # 询问是否继续
    print(f"\n即将为 {len(generator.df)} 行数据生成随机名称...")
    user_input = input("是否继续？(y/n): ").lower().strip()
    if user_input != 'y':
        print("用户取消操作")
        exit(0)

    # 生成随机名称
    print("\n🔄 正在生成随机名称...")
    if not generator.generate_all_random_names():
        print("❌ 生成随机名称失败")
        exit(1)

    print("✅ 随机名称生成成功")

    # 预览结果
    print("\n📋 生成结果预览:")
    for i in range(min(10, len(generator.df))):
        if len(generator.df.columns) >= 3:
            real_value = generator.df.iloc[i, 1]
            random_name = generator.df.iloc[i, 2]
            print(f"  {i+1}. '{real_value}' -> '{random_name}'")

    # 保存文件
    print("\n💾 正在保存文件...")
    if generator.save_excel():
        print("✅ 文件保存成功")
    else:
        print("❌ 文件保存失败")

    print("\n🎉 处理完成！")
    print(f"生成的随机名称数量: {len(generator.generated_names)}")

except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所需的库已安装：pip install pandas openpyxl")
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc()
