"""
测试产品召回功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from product_recommender import recall_similar_products
import pandas as pd


def create_sample_data():
    """
    创建示例产品数据用于测试
    """
    sample_data = {
        '产品代码': [
            'P001', 'P002', 'P003', 'P004', 'P005',
            'P006', 'P007', 'P008', 'P009', 'P010'
        ],
        '产品名称': [
            '天天成长3号现金管理类理财产品',
            '新启航三个月定开混合类理财产品',
            '添利90天固定收益类理财产品',
            '稳利多6号固收+理财产品',
            '灵活成长28天持有期理财产品',
            '智慧增长1年期权益类理财产品',
            '安心理财180天定期产品',
            '稳健收益365天理财计划',
            '高收益股票型基金产品',
            '保本型货币市场基金'
        ],
        '产品类型': [
            '现金管理', '混合类', '固定收益', '固收+', '持有期',
            '权益类', '定期', '理财计划', '股票型', '货币市场'
        ],
        '风险等级': [
            '低风险', '中等风险', '低风险', '中等风险', '低风险',
            '高风险', '低风险', '低风险', '高风险', '低风险'
        ]
    }

    df = pd.DataFrame(sample_data)

    # 确保目录存在
    os.makedirs('../knowledge_bases', exist_ok=True)

    # 保存为Excel文件
    df.to_excel('../knowledge_bases/产品库.xlsx', index=False)
    print("已创建示例产品数据文件: ../knowledge_bases/产品库.xlsx")
    return df


def test_recall_functionality():
    """
    测试召回功能
    """
    print("=" * 60)
    print("测试产品召回功能")
    print("=" * 60)

    # 创建示例数据
    df = create_sample_data()
    print(f"创建了 {len(df)} 条示例产品数据\n")

    # 测试问题
    test_questions = [
        "我想要低风险的理财产品",
        "有什么高收益的投资",
        "现金管理产品推荐",
        "定期理财产品",
        "基金产品有哪些"
    ]

    for question in test_questions:
        print(f"问题: {question}")
        print("-" * 40)

        # 召回相似产品
        results = recall_similar_products(question, '../knowledge_bases/产品库.xlsx', top_k=5)

        if results:
            print("召回的产品:")
            for i, product in enumerate(results, 1):
                print(f"  {i}. {product}")
        else:
            print("没有找到相关产品")
        print()


def test_different_top_k():
    """
    测试不同的top_k值
    """
    print("=" * 60)
    print("测试不同的top_k值")
    print("=" * 60)

    question = "理财产品"
    top_k_values = [3, 5, 10, 100]

    for top_k in top_k_values:
        print(f"问题: {question}, top_k: {top_k}")
        print("-" * 40)

        results = recall_similar_products(question, '../knowledge_bases/产品库.xlsx', top_k=top_k)

        print(f"召回结果数量: {len(results)}")
        if results:
            print("前3个结果:")
            for i, product in enumerate(results[:3], 1):
                print(f"  {i}. {product}")
        print()


def test_edge_cases():
    """
    测试边界情况
    """
    print("=" * 60)
    print("测试边界情况")
    print("=" * 60)

    # 测试用例
    edge_cases = [
        ("", "空字符串"),
        ("   ", "空白字符串"),
        ("xyz123", "无关内容"),
        ("产品", "单个词"),
        ("我想要一个非常特殊的不存在的产品类型", "长查询")
    ]

    for query, description in edge_cases:
        print(f"测试: {description}")
        print(f"查询: '{query}'")

        results = recall_similar_products(query, '../knowledge_bases/产品库.xlsx', top_k=3)
        print(f"结果数量: {len(results)}")

        if results:
            print("结果:")
            for i, product in enumerate(results, 1):
                print(f"  {i}. {product}")
        print()


def test_file_not_found():
    """
    测试文件不存在的情况
    """
    print("=" * 60)
    print("测试文件不存在的情况")
    print("=" * 60)

    question = "理财产品"
    non_existent_file = "不存在的文件.xlsx"

    print(f"问题: {question}")
    print(f"文件: {non_existent_file}")

    results = recall_similar_products(question, non_existent_file, top_k=5)
    print(f"结果数量: {len(results)}")


def main():
    """
    主测试函数
    """
    print("开始测试产品召回功能...")

    try:
        # 基本召回功能测试
        test_recall_functionality()

        # 不同top_k值测试
        test_different_top_k()

        # 边界情况测试
        test_edge_cases()

        # 文件不存在测试
        test_file_not_found()

        print("=" * 60)
        print("所有测试完成!")
        print("=" * 60)

    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
