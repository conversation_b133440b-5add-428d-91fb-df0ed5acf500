"""
简化版BM25产品召回模块
无需预训练，实时计算BM25分数
"""

import pandas as pd
import math
from collections import Counter
from typing import List


def simple_chinese_tokenize(text: str) -> List[str]:
    """
    简单的中文分词函数
    """
    if not text:
        return []
    
    tokens = []
    current_word = ""
    
    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符
            if current_word:
                tokens.append(current_word)
                current_word = ""
            tokens.append(char)
        elif char.isalnum():  # 英文字母或数字
            current_word += char
        else:  # 其他字符（标点符号等）
            if current_word:
                tokens.append(current_word)
                current_word = ""
    
    if current_word:
        tokens.append(current_word)
    
    return [token for token in tokens if token.strip()]


def calculate_bm25_score(query_tokens: List[str], doc_tokens: List[str], 
                        all_docs: List[List[str]], k1: float = 1.5, b: float = 0.75) -> float:
    """
    计算单个文档的BM25分数
    
    参数:
        query_tokens: 查询词列表
        doc_tokens: 文档词列表
        all_docs: 所有文档的词列表（用于计算IDF）
        k1, b: BM25参数
        
    返回:
        float: BM25分数
    """
    if not query_tokens or not doc_tokens:
        return 0.0
    
    # 计算文档长度和平均文档长度
    doc_len = len(doc_tokens)
    avg_doc_len = sum(len(doc) for doc in all_docs) / len(all_docs) if all_docs else 1
    
    # 计算词频
    doc_freq = Counter(doc_tokens)
    
    # 计算总文档数
    N = len(all_docs)
    
    score = 0.0
    for term in query_tokens:
        if term in doc_freq:
            # 计算词频
            tf = doc_freq[term]
            
            # 计算文档频率（包含该词的文档数）
            df = sum(1 for doc in all_docs if term in doc)
            
            # 计算IDF
            idf = math.log((N - df + 0.5) / (df + 0.5)) if df > 0 else 0
            
            # 计算BM25分数
            numerator = tf * (k1 + 1)
            denominator = tf + k1 * (1 - b + b * doc_len / avg_doc_len) if avg_doc_len > 0 else tf + k1
            
            score += idf * (numerator / denominator)
    
    return score


def recall_products_simple(user_question: str, excel_path: str = "prod_reco/产品库.xlsx", 
                          top_k: int = 100) -> List[str]:
    """
    简化版产品召回函数，无需预训练
    
    参数:
        user_question (str): 用户问题
        excel_path (str): 产品库Excel文件路径
        top_k (int): 返回最相似的前k条数据
        
    返回:
        List[str]: 最相似的产品内容列表
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        
        if len(df.columns) < 2:
            print("错误: Excel文件至少需要2列数据")
            return []
        
        # 提取第二列数据
        second_column = df.iloc[:, 1]
        
        # 预处理所有文档
        all_docs = []
        original_texts = []
        
        for text in second_column:
            if pd.isna(text):
                all_docs.append([])
                original_texts.append("")
            else:
                original_text = str(text)
                original_texts.append(original_text)
                tokens = simple_chinese_tokenize(original_text)
                all_docs.append(tokens)
        
        if not all_docs:
            print("警告: 没有找到有效的产品数据")
            return []
        
        # 预处理用户问题
        if not user_question.strip():
            print("错误: 用户问题不能为空")
            return []
        
        query_tokens = simple_chinese_tokenize(user_question)
        
        if not query_tokens:
            print("警告: 预处理后的查询为空")
            return []
        
        # 计算每个文档的BM25分数
        scored_docs = []
        for i, doc_tokens in enumerate(all_docs):
            score = calculate_bm25_score(query_tokens, doc_tokens, all_docs)
            if original_texts[i]:  # 只包含非空文本
                scored_docs.append((score, original_texts[i]))
        
        # 按分数降序排序
        scored_docs.sort(key=lambda x: x[0], reverse=True)
        
        # 返回前top_k个结果
        result_texts = [text for score, text in scored_docs[:top_k]]
        
        return result_texts
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {excel_path}")
        return []
    except Exception as e:
        print(f"处理过程中出错: {str(e)}")
        return []


def recall_products_with_scores(user_question: str, excel_path: str = "knowledge_bases/产品库.xlsx", 
                               top_k: int = 100) -> List[tuple]:
    """
    带分数的产品召回函数
    
    参数:
        user_question (str): 用户问题
        excel_path (str): 产品库Excel文件路径
        top_k (int): 返回最相似的前k条数据
        
    返回:
        List[tuple]: (分数, 产品内容) 的元组列表
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        
        if len(df.columns) < 2:
            print("错误: Excel文件至少需要2列数据")
            return []
        
        # 提取第二列数据
        second_column = df.iloc[:, 1]
        
        # 预处理所有文档
        all_docs = []
        original_texts = []
        
        for text in second_column:
            if pd.isna(text):
                all_docs.append([])
                original_texts.append("")
            else:
                original_text = str(text)
                original_texts.append(original_text)
                tokens = simple_chinese_tokenize(original_text)
                all_docs.append(tokens)
        
        if not all_docs:
            print("警告: 没有找到有效的产品数据")
            return []
        
        # 预处理用户问题
        if not user_question.strip():
            print("错误: 用户问题不能为空")
            return []
        
        query_tokens = simple_chinese_tokenize(user_question)
        
        if not query_tokens:
            print("警告: 预处理后的查询为空")
            return []
        
        # 计算每个文档的BM25分数
        scored_docs = []
        for i, doc_tokens in enumerate(all_docs):
            score = calculate_bm25_score(query_tokens, doc_tokens, all_docs)
            if original_texts[i]:  # 只包含非空文本
                scored_docs.append((score, original_texts[i]))
        
        # 按分数降序排序
        scored_docs.sort(key=lambda x: x[0], reverse=True)
        
        # 返回前top_k个结果
        return scored_docs[:top_k]
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {excel_path}")
        return []
    except Exception as e:
        print(f"处理过程中出错: {str(e)}")
        return []


# 示例使用
if __name__ == "__main__":
    def test_simple_recall():
        """
        测试简化版召回功能
        """
        print("=" * 60)
        print("测试简化版BM25产品召回")
        print("=" * 60)
        
        # 测试问题
        test_questions = [
            "我想要理财产品",
            "有什么基金推荐",
            "低风险的投资产品",
            "高收益理财",
            "定期存款"
        ]
        
        for question in test_questions:
            print(f"\n问题: {question}")
            print("-" * 40)
            
            # 召回产品（仅文本）
            products = recall_products_simple(question, top_k=3)
            print("召回结果:")
            for i, product in enumerate(products, 1):
                print(f"  {i}. {product}")
            
            # 召回产品（带分数）
            scored_products = recall_products_with_scores(question, top_k=3)
            print("带分数的结果:")
            for i, (score, product) in enumerate(scored_products, 1):
                print(f"  {i}. {product} (分数: {score:.4f})")
            print()
    
    # 运行测试
    test_simple_recall()
