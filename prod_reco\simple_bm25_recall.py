"""
简化版BM25产品召回模块
无需预训练，实时计算BM25分数
"""

import pandas as pd
import math
from collections import Counter
from typing import List


def simple_chinese_tokenize(text: str) -> List[str]:
    """
    简单的中文分词函数
    """
    if not text:
        return []
    
    tokens = []
    current_word = ""
    
    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符
            if current_word:
                tokens.append(current_word)
                current_word = ""
            tokens.append(char)
        elif char.isalnum():  # 英文字母或数字
            current_word += char
        else:  # 其他字符（标点符号等）
            if current_word:
                tokens.append(current_word)
                current_word = ""
    
    if current_word:
        tokens.append(current_word)
    
    return [token for token in tokens if token.strip()]


def calculate_bm25_scores_optimized(query_tokens: List[str], all_docs: List[List[str]],
                                   k1: float = 1.5, b: float = 0.75) -> List[float]:
    """
    高度优化的批量BM25分数计算

    参数:
        query_tokens: 查询词列表
        all_docs: 所有文档的词列表
        k1, b: BM25参数

    返回:
        List[float]: 每个文档的BM25分数
    """
    if not query_tokens or not all_docs:
        return [0.0] * len(all_docs)

    N = len(all_docs)
    if N == 0:
        return []

    # 去重查询词，提高效率
    unique_query_terms = list(set(query_tokens))

    # 如果没有有效查询词，直接返回零分数
    if not unique_query_terms:
        return [0.0] * N

    # 预计算所有文档长度
    doc_lengths = [len(doc) for doc in all_docs]
    avg_doc_len = sum(doc_lengths) / N

    # 预计算查询词的文档频率和IDF
    term_stats = {}
    for term in unique_query_terms:
        # 使用集合操作快速计算文档频率
        df = sum(1 for doc in all_docs if term in doc)
        if df > 0:
            idf = math.log((N - df + 0.5) / (df + 0.5))
            term_stats[term] = idf

    # 如果没有任何查询词在文档中出现，返回零分数
    if not term_stats:
        return [0.0] * N

    # 预计算常用值
    k1_plus_1 = k1 + 1
    one_minus_b = 1 - b

    # 批量计算分数
    scores = []
    for i, doc_tokens in enumerate(all_docs):
        if not doc_tokens:
            scores.append(0.0)
            continue

        # 只计算出现在查询中的词的频率
        doc_freq = {}
        for token in doc_tokens:
            if token in term_stats:
                doc_freq[token] = doc_freq.get(token, 0) + 1

        # 如果文档中没有查询词，跳过
        if not doc_freq:
            scores.append(0.0)
            continue

        doc_len = doc_lengths[i]
        len_norm = one_minus_b + b * doc_len / avg_doc_len

        score = 0.0
        for term in query_tokens:  # 保持查询词的重复权重
            if term in doc_freq:
                tf = doc_freq[term]
                idf = term_stats[term]

                # 优化的BM25公式
                score += idf * (tf * k1_plus_1) / (tf + k1 * len_norm)

        scores.append(score)

    return scores


def recall_products_simple(user_question: str, excel_path: str = "prod_reco/产品库.xlsx",
                          top_k: int = 100) -> pd.DataFrame:
    """
    高性能产品召回函数，返回DataFrame

    参数:
        user_question (str): 用户问题
        excel_path (str): 产品库Excel文件路径
        top_k (int): 返回最相似的前k条数据

    返回:
        pd.DataFrame: 包含第一列和第二列的DataFrame，按相似度排序
    """
    try:
        # 预处理用户问题
        if not user_question.strip():
            return pd.DataFrame()

        query_tokens = simple_chinese_tokenize(user_question)
        if not query_tokens:
            return pd.DataFrame()

        # 读取Excel文件
        df = pd.read_excel(excel_path)

        if len(df.columns) < 2:
            return pd.DataFrame()

        # 获取列名
        col1_name = df.columns[0]  # 第一列列名
        col2_name = df.columns[1]  # 第二列列名

        # 提取第二列数据并过滤空值
        second_column = df.iloc[:, 1]
        valid_data = []
        valid_indices = []

        for i, text in enumerate(second_column):
            if not pd.isna(text) and str(text).strip():
                valid_data.append({
                    col1_name: df.iloc[i, 0],  # 第一列的值
                    col2_name: str(text),      # 第二列的值
                    'original_index': i        # 原始索引
                })
                valid_indices.append(i)

        if not valid_data:
            return pd.DataFrame()

        # 只对有效文本进行分词
        valid_texts = [item[col2_name] for item in valid_data]
        all_docs = [simple_chinese_tokenize(text) for text in valid_texts]

        # 批量计算BM25分数
        scores = calculate_bm25_scores_optimized(query_tokens, all_docs)

        # 为每个数据项添加分数
        for i, score in enumerate(scores):
            valid_data[i]['score'] = score

        # 按分数降序排序
        valid_data.sort(key=lambda x: x['score'], reverse=True)

        # 取前top_k个结果
        top_data = valid_data[:top_k]

        # 构建结果DataFrame
        result_data = []
        for item in top_data:
            result_data.append({
                col1_name: item[col1_name],
                col2_name: item[col2_name]
            })

        result_df = pd.DataFrame(result_data)
        return result_df

    except FileNotFoundError:
        return pd.DataFrame()
    except Exception as e:
        return pd.DataFrame()


# 示例使用
if __name__ == "__main__":
    def test_simple_recall():
        """
        测试简化版召回功能
        """
        print("=" * 60)
        print("测试简化版BM25产品召回")
        print("=" * 60)
        
        # 测试问题
        test_questions = [
            "天天成长",
            "有什么基金推荐",
            "低风险的投资产品",
            "高收益理财",
            "定期存款"
        ]
        
        for question in test_questions:
            print(f"\n问题: {question}")
            print("-" * 40)
            
            # 召回产品（仅文本）
            products = recall_products_simple(question, top_k=100)
            print("召回结果:")
            for i, product in enumerate(products, 1):
                print(f"  {i}. {product}")
    
    # 运行测试
    test_simple_recall()
