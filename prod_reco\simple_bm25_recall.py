"""
简化版BM25产品召回模块
降低认知复杂度的重构版本
"""

import pandas as pd
import math
from collections import Counter
from typing import List, Dict, <PERSON><PERSON>


def tokenize_text(text: str) -> List[str]:
    """简单分词：中文按字符，英文按词"""
    if not text:
        return []

    tokens = []
    current_word = ""

    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符
            if current_word:
                tokens.append(current_word)
                current_word = ""
            tokens.append(char)
        elif char.isalnum():  # 英文字母或数字
            current_word += char
        else:  # 其他字符
            if current_word:
                tokens.append(current_word)
                current_word = ""

    if current_word:
        tokens.append(current_word)

    return [token for token in tokens if token.strip()]


def calculate_idf_scores(query_terms: List[str], all_docs: List[List[str]]) -> Dict[str, float]:
    """计算查询词的IDF分数"""
    N = len(all_docs)
    term_idf = {}

    for term in query_terms:
        doc_freq = sum(1 for doc in all_docs if term in doc)
        if doc_freq > 0:
            idf = math.log((N - doc_freq + 0.5) / (doc_freq + 0.5))
            term_idf[term] = idf

    return term_idf


def calculate_doc_score(query_tokens: List[str], doc_tokens: List[str],
                       term_idf: Dict[str, float], doc_len: int, avg_doc_len: float,
                       k1: float = 1.5, b: float = 0.75) -> float:
    """计算单个文档的BM25分数"""
    if not doc_tokens:
        return 0.0

    # 计算文档中查询词的频率
    doc_freq = Counter(token for token in doc_tokens if token in term_idf)
    if not doc_freq:
        return 0.0

    # BM25计算
    len_norm = (1 - b) + b * doc_len / avg_doc_len
    score = 0.0

    for term in query_tokens:
        if term in doc_freq:
            tf = doc_freq[term]
            idf = term_idf[term]
            score += idf * (tf * (k1 + 1)) / (tf + k1 * len_norm)

    return score


def calculate_bm25_scores(query_tokens: List[str], all_docs: List[List[str]]) -> List[float]:
    """批量计算BM25分数"""
    if not query_tokens or not all_docs:
        return [0.0] * len(all_docs)

    # 去重查询词
    unique_terms = list(set(query_tokens))

    # 计算IDF
    term_idf = calculate_idf_scores(unique_terms, all_docs)
    if not term_idf:
        return [0.0] * len(all_docs)

    # 计算文档长度统计
    doc_lengths = [len(doc) for doc in all_docs]
    avg_doc_len = sum(doc_lengths) / len(doc_lengths) if doc_lengths else 1

    # 计算每个文档的分数
    scores = []
    for i, doc_tokens in enumerate(all_docs):
        score = calculate_doc_score(query_tokens, doc_tokens, term_idf,
                                  doc_lengths[i], avg_doc_len)
        scores.append(score)

    return scores


def validate_inputs(user_question: str, df: pd.DataFrame) -> bool:
    """验证输入参数"""
    if not user_question.strip():
        return False
    if df is None or df.empty:
        return False
    if len(df.columns) < 2:
        return False
    return True


def extract_valid_data(df: pd.DataFrame) -> List[Dict]:
    """提取有效的数据行"""
    col1_name = df.columns[0]
    col2_name = df.columns[1]
    valid_data = []

    for i, text in enumerate(df.iloc[:, 1]):
        if not pd.isna(text) and str(text).strip():
            valid_data.append({
                col1_name: df.iloc[i, 0],
                col2_name: str(text),
                'index': i
            })

    return valid_data


def build_result_dataframe(scored_data: List[Dict], top_k: int) -> pd.DataFrame:
    """构建结果DataFrame"""
    if not scored_data:
        return pd.DataFrame()

    # 排序并取前top_k个
    scored_data.sort(key=lambda x: x['score'], reverse=True)
    top_data = scored_data[:top_k]

    # 构建结果
    col1_name = list(top_data[0].keys())[0]  # 第一个非score键
    col2_name = list(top_data[0].keys())[1]  # 第二个非score键

    result_data = []
    for item in top_data:
        result_data.append({
            col1_name: item[col1_name],
            col2_name: item[col2_name]
        })

    return pd.DataFrame(result_data)


def recall_products_simple(user_question: str, df: pd.DataFrame, top_k: int = 100) -> pd.DataFrame:
    """产品召回主函数"""
    try:
        # 1. 验证输入
        if not validate_inputs(user_question, df):
            return pd.DataFrame()

        # 2. 分词
        query_tokens = tokenize_text(user_question)
        if not query_tokens:
            return pd.DataFrame()

        # 3. 提取有效数据
        valid_data = extract_valid_data(df)
        if not valid_data:
            return pd.DataFrame()

        # 4. 文档分词
        col2_name = df.columns[1]
        texts = [item[col2_name] for item in valid_data]
        docs = [tokenize_text(text) for text in texts]

        # 5. 计算BM25分数
        scores = calculate_bm25_scores(query_tokens, docs)

        # 6. 添加分数到数据
        for i, score in enumerate(scores):
            valid_data[i]['score'] = score

        # 7. 构建结果
        return build_result_dataframe(valid_data, top_k)

    except Exception:
        return pd.DataFrame()


# 示例使用
if __name__ == "__main__":
    def test_simple_recall():
        """
        测试简化版召回功能
        """
        print("=" * 60)
        print("测试简化版BM25产品召回")
        print("=" * 60)
        
        # 测试问题
        test_questions = [
            "天天成长",
            "和盈资产管理",
            "优选安享",
            "灵活成长"
        ]
        
        # 读取测试DataFrame:
        test_df = pd.read_excel("prod_reco/产品库.xlsx")
        print("从文件读取的测试数据:")
        print(test_df.head())
        print(f"数据形状: {test_df.shape}")

        for question in test_questions:
            print(f"\n问题: {question}")
            print("-" * 40)

            # 召回产品（返回DataFrame）
            result_df = recall_products_simple(question, test_df, top_k=20)
            print("召回结果:")
            if not result_df.empty:
                print(result_df)

            else:
                print("  没有找到匹配结果")
    
    # 运行测试
    test_simple_recall()
