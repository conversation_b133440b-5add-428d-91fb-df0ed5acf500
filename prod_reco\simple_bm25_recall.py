"""
简化版BM25产品召回模块
无需预训练，实时计算BM25分数
"""

import pandas as pd
import math
from collections import Counter
from typing import List


def simple_chinese_tokenize(text: str) -> List[str]:
    """
    简单的中文分词函数
    """
    if not text:
        return []
    
    tokens = []
    current_word = ""
    
    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符
            if current_word:
                tokens.append(current_word)
                current_word = ""
            tokens.append(char)
        elif char.isalnum():  # 英文字母或数字
            current_word += char
        else:  # 其他字符（标点符号等）
            if current_word:
                tokens.append(current_word)
                current_word = ""
    
    if current_word:
        tokens.append(current_word)
    
    return [token for token in tokens if token.strip()]


def calculate_bm25_scores_optimized(query_tokens: List[str], all_docs: List[List[str]],
                                   k1: float = 1.5, b: float = 0.75) -> List[float]:
    """
    高度优化的批量BM25分数计算

    参数:
        query_tokens: 查询词列表
        all_docs: 所有文档的词列表
        k1, b: BM25参数

    返回:
        List[float]: 每个文档的BM25分数
    """
    if not query_tokens or not all_docs:
        return [0.0] * len(all_docs)

    N = len(all_docs)
    if N == 0:
        return []

    # 去重查询词，提高效率
    unique_query_terms = list(set(query_tokens))

    # 如果没有有效查询词，直接返回零分数
    if not unique_query_terms:
        return [0.0] * N

    # 预计算所有文档长度
    doc_lengths = [len(doc) for doc in all_docs]
    avg_doc_len = sum(doc_lengths) / N

    # 预计算查询词的文档频率和IDF
    term_stats = {}
    for term in unique_query_terms:
        # 使用集合操作快速计算文档频率
        df = sum(1 for doc in all_docs if term in doc)
        if df > 0:
            idf = math.log((N - df + 0.5) / (df + 0.5))
            term_stats[term] = idf

    # 如果没有任何查询词在文档中出现，返回零分数
    if not term_stats:
        return [0.0] * N

    # 预计算常用值
    k1_plus_1 = k1 + 1
    one_minus_b = 1 - b

    # 批量计算分数
    scores = []
    for i, doc_tokens in enumerate(all_docs):
        if not doc_tokens:
            scores.append(0.0)
            continue

        # 只计算出现在查询中的词的频率
        doc_freq = {}
        for token in doc_tokens:
            if token in term_stats:
                doc_freq[token] = doc_freq.get(token, 0) + 1

        # 如果文档中没有查询词，跳过
        if not doc_freq:
            scores.append(0.0)
            continue

        doc_len = doc_lengths[i]
        len_norm = one_minus_b + b * doc_len / avg_doc_len

        score = 0.0
        for term in query_tokens:  # 保持查询词的重复权重
            if term in doc_freq:
                tf = doc_freq[term]
                idf = term_stats[term]

                # 优化的BM25公式
                score += idf * (tf * k1_plus_1) / (tf + k1 * len_norm)

        scores.append(score)

    return scores


def recall_products_simple(user_question: str, excel_path: str = "prod_reco/产品库.xlsx",
                          top_k: int = 100) -> List[str]:
    """
    高性能产品召回函数

    参数:
        user_question (str): 用户问题
        excel_path (str): 产品库Excel文件路径
        top_k (int): 返回最相似的前k条数据

    返回:
        List[str]: 最相似的产品内容列表
    """
    try:
        # 预处理用户问题
        if not user_question.strip():
            return []

        query_tokens = simple_chinese_tokenize(user_question)
        if not query_tokens:
            return []

        # 读取Excel文件
        df = pd.read_excel(excel_path)

        if len(df.columns) < 2:
            return []

        # 提取第二列数据并过滤空值
        second_column = df.iloc[:, 1]
        valid_texts = []
        valid_indices = []

        for i, text in enumerate(second_column):
            if not pd.isna(text) and str(text).strip():
                valid_texts.append(str(text))
                valid_indices.append(i)

        if not valid_texts:
            return []

        # 只对有效文本进行分词
        all_docs = [simple_chinese_tokenize(text) for text in valid_texts]

        # 批量计算BM25分数
        scores = calculate_bm25_scores_optimized(query_tokens, all_docs)

        # 使用堆排序优化top-k选择
        if top_k >= len(scores):
            # 如果需要所有结果，直接排序
            scored_docs = list(zip(scores, valid_texts))
            scored_docs.sort(key=lambda x: x[0], reverse=True)
            return [text for _, text in scored_docs]
        else:
            # 使用部分排序优化
            import heapq
            # 使用负分数来实现最大堆
            top_items = heapq.nlargest(top_k, zip(scores, valid_texts), key=lambda x: x[0])
            return [text for _, text in top_items]

    except FileNotFoundError:
        return []
    except Exception as e:
        return []


def recall_products_with_scores(user_question: str, excel_path: str = "prod_reco/产品库.xlsx", 
                               top_k: int = 100) -> List[tuple]:
    """
    带分数的产品召回函数
    
    参数:
        user_question (str): 用户问题
        excel_path (str): 产品库Excel文件路径
        top_k (int): 返回最相似的前k条数据
        
    返回:
        List[tuple]: (分数, 产品内容) 的元组列表
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        
        if len(df.columns) < 2:
            print("错误: Excel文件至少需要2列数据")
            return []
        
        # 提取第二列数据
        second_column = df.iloc[:, 1]
        
        # 预处理所有文档
        all_docs = []
        original_texts = []
        
        for text in second_column:
            if pd.isna(text):
                all_docs.append([])
                original_texts.append("")
            else:
                original_text = str(text)
                original_texts.append(original_text)
                tokens = simple_chinese_tokenize(original_text)
                all_docs.append(tokens)
        
        if not all_docs:
            print("警告: 没有找到有效的产品数据")
            return []
        
        # 预处理用户问题
        if not user_question.strip():
            print("错误: 用户问题不能为空")
            return []
        
        query_tokens = simple_chinese_tokenize(user_question)
        
        if not query_tokens:
            print("警告: 预处理后的查询为空")
            return []
        
        # 批量计算所有文档的BM25分数
        scores = calculate_bm25_scores_optimized(query_tokens, all_docs)

        # 创建(分数, 文本)的元组列表，只包含非空文本
        scored_docs = []
        for i, score in enumerate(scores):
            if original_texts[i]:  # 只包含非空文本
                scored_docs.append((score, original_texts[i]))

        # 按分数降序排序
        scored_docs.sort(key=lambda x: x[0], reverse=True)

        # 返回前top_k个结果
        return scored_docs[:top_k]
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {excel_path}")
        return []
    except Exception as e:
        print(f"处理过程中出错: {str(e)}")
        return []


# 示例使用
if __name__ == "__main__":
    def test_simple_recall():
        """
        测试简化版召回功能
        """
        print("=" * 60)
        print("测试简化版BM25产品召回")
        print("=" * 60)
        
        # 测试问题
        test_questions = [
            "我想要理财产品",
            "有什么基金推荐",
            "低风险的投资产品",
            "高收益理财",
            "定期存款"
        ]
        
        for question in test_questions:
            print(f"\n问题: {question}")
            print("-" * 40)
            
            # 召回产品（仅文本）
            products = recall_products_simple(question, top_k=3)
            print("召回结果:")
            for i, product in enumerate(products, 1):
                print(f"  {i}. {product}")
            
            # 召回产品（带分数）
            scored_products = recall_products_with_scores(question, top_k=3)
            print("带分数的结果:")
            for i, (score, product) in enumerate(scored_products, 1):
                print(f"  {i}. {product} (分数: {score:.4f})")
            print()
    
    # 运行测试
    test_simple_recall()
