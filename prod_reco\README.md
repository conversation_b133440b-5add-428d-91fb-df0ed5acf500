# 产品库随机名称生成器

## 📋 功能概述

本工具用于为产品库.xlsx文件的第三列生成随机简化名称。根据第二列的真实值（如"福建上杭农村商业银行"），自动生成简化的随机名称（如"福建杭农"）。

## 🚀 主要功能

1. **智能名称提取**：根据不同类型的机构名称使用不同的提取规则
2. **重复避免**：确保生成的随机名称不重复
3. **自动备份**：处理前自动创建备份文件
4. **批量处理**：一次性处理整个Excel文件

## 📁 文件结构

```
prod_reco/
├── generate_random_names.py    # 主生成器脚本
├── test_name_generator.py      # 测试脚本
├── README.md                   # 使用说明
└── 产品库.xlsx                 # 目标Excel文件（需要用户提供）
```

## 🛠️ 使用方法

### 1. 准备工作

确保产品库.xlsx文件存在于prod_reco目录中，文件格式要求：
- 第一列：序号或其他标识
- 第二列：真实值（机构全名）
- 第三列：将被填充随机名称（可以为空）

### 2. 运行生成器

```bash
cd prod_reco
python generate_random_names.py
```

### 3. 运行测试

```bash
cd prod_reco
python test_name_generator.py
```

## 🔧 生成规则

### 银行类机构

| 真实值 | 生成规则 | 示例结果 |
|--------|----------|----------|
| 福建上杭农村商业银行 | 地名+类型简化 | 福建杭农 |
| 中国工商银行股份有限公司 | 去除通用词+简化 | 工商银行 |
| 招商银行股份有限公司 | 保留核心名称 | 招商银行 |

### 理财公司

| 真实值 | 生成规则 | 示例结果 |
|--------|----------|----------|
| 平安理财有限责任公司 | 保留品牌名 | 平安理财 |
| 建信理财有限责任公司 | 保留品牌名 | 建信理财 |

### 基金公司

| 真实值 | 生成规则 | 示例结果 |
|--------|----------|----------|
| 天弘基金管理有限公司 | 保留品牌名 | 天弘基金 |
| 华夏基金管理有限公司 | 保留品牌名 | 华夏基金 |

### 其他机构

对于不匹配特定模式的机构名称，使用通用规则：
1. 移除常见后缀（有限公司、股份有限公司等）
2. 移除常见前缀（中国、中华等）
3. 提取关键字符（2-4个字符）

## 📊 示例输出

```
🚀 产品库随机名称生成器
==================================================

📊 原始数据预览:
  1. '福建上杭农村商业银行' -> '福建杭农'
  2. '中国工商银行股份有限公司' -> '工商银行'
  3. '招商银行股份有限公司' -> '招商银行'
  4. '平安理财有限责任公司' -> '平安理财'
  5. '天弘基金管理有限公司' -> '天弘基金'

即将为 100 行数据生成随机名称...
是否继续？(y/n): y

随机名称生成完成，共生成 100 个名称

📋 生成结果预览:
  1. '福建上杭农村商业银行' -> '福建杭农'
  2. '中国工商银行股份有限公司' -> '工商银行'
  ...

✅ 文件保存成功

🎉 随机名称生成完成！
生成的随机名称数量: 100
```

## ⚙️ 配置选项

### 修改生成规则

在`generate_random_names.py`中可以自定义生成规则：

```python
def _extract_bank_name(self, name):
    """自定义银行名称提取规则"""
    # 在这里修改银行名称的提取逻辑
    pass
```

### 调整名称长度

```python
def _extract_generic_name(self, name):
    # 修改这里的数字来调整生成名称的长度
    if len(clean_name) >= 4:
        return clean_name[:4]  # 改为[:3]生成3个字符
```

## 🔍 测试功能

### 运行完整测试

```bash
python test_name_generator.py
```

### 测试内容

1. **名称提取测试**：验证各种类型机构名称的提取效果
2. **重复处理测试**：验证重复名称的处理机制
3. **边界情况测试**：验证空值、特殊字符等边界情况
4. **模式匹配测试**：验证不同类型机构的匹配规则
5. **完整流程测试**：验证从加载到保存的完整流程

## 📝 日志记录

程序运行时会生成详细的日志文件：

- `generate_random_names.log`：生成器运行日志
- 包含每个名称的转换过程
- 记录错误和警告信息

## ⚠️ 注意事项

1. **备份文件**：程序会自动创建备份文件，格式为`产品库_备份_时间戳.xlsx`
2. **重复检查**：程序会自动避免生成重复的随机名称
3. **编码问题**：确保Excel文件使用UTF-8编码
4. **文件权限**：确保对Excel文件有读写权限

## 🔧 故障排除

### 常见问题

1. **文件不存在**
   ```
   错误: 加载Excel文件失败
   解决: 确保产品库.xlsx文件存在于prod_reco目录中
   ```

2. **权限不足**
   ```
   错误: 保存Excel文件失败
   解决: 关闭Excel文件，确保文件未被其他程序占用
   ```

3. **编码错误**
   ```
   错误: UnicodeDecodeError
   解决: 确保Excel文件使用正确的编码格式
   ```

### 调试模式

启用详细日志：

```python
logging.basicConfig(level=logging.DEBUG)
```

## 🚀 扩展功能

### 添加新的机构类型

在`ProductNameGenerator`类中添加新的提取方法：

```python
def _extract_new_type_name(self, name):
    """新机构类型的提取规则"""
    # 实现新的提取逻辑
    pass
```

然后在`extract_key_chars`方法中添加新规则：

```python
rules = [
    self._extract_bank_name,
    self._extract_company_name,
    self._extract_new_type_name,  # 添加新规则
    # ...
]
```

### 自定义输出格式

修改`save_excel`方法来自定义输出格式，例如添加更多列或修改文件名格式。

---

**版本信息：**
- 版本：v1.0
- 创建日期：2025-06-04
- 兼容性：Python 3.6+
- 依赖：pandas, openpyxl
