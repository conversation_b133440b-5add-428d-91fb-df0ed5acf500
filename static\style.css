/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    height: 100vh;
    margin: 0;
    padding: 0;
}

.container-fluid {
    height: 100%;
    padding: 0;
}

.row {
    height: 100%;
    margin: 0;
}

/* 侧边栏样式 */
.sidebar {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 20px;
    background-color: #1a2530;
    border-bottom: 1px solid #34495e;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.sidebar-header p {
    margin: 5px 0 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.history-header h5 {
    margin: 0;
    font-size: 1.1rem;
}

.history-list {
    margin-top: 10px;
}

.history-item {
    padding: 10px;
    margin-bottom: 8px;
    background-color: #34495e;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s;
    word-break: break-word;
}

.history-item:hover {
    background-color: #3d5a74;
}

.sidebar-footer {
    padding: 15px;
    text-align: center;
    font-size: 0.8rem;
    opacity: 0.7;
    background-color: #1a2530;
}

/* 主内容区样式 */
.main-content {
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    background-color: #fff;
    border-radius: 0 0 5px 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    margin-bottom: 20px;
}

.message {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

.user-message {
    align-items: flex-end;
}

.system-message {
    align-items: flex-start;
}

.message-content {
    max-width: 80%;
    padding: 12px 15px;
    border-radius: 10px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-message .message-content {
    background-color: #3498db;
    color: white;
}

.system-message .message-content {
    background-color: #f1f1f1;
    color: #333;
}

.message-time {
    font-size: 0.75rem;
    margin-top: 5px;
    opacity: 0.7;
}

.chat-input {
    padding: 15px 0 0;
    border-top: 1px solid #eee;
}

.chat-input .form-control {
    border-radius: 20px 0 0 20px;
    padding: 10px 15px;
}

.chat-input .btn {
    border-radius: 0 20px 20px 0;
    padding: 10px 20px;
}

/* 结果容器样式 */
.result-container {
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 0 20px;
}

.result-section {
    margin-bottom: 15px;
}

.result-section h6 {
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.result-code {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #e9ecef;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9rem;
    overflow-x: auto;
    margin: 0;
}

.result-info {
    text-align: right;
    font-size: 0.8rem;
    color: #6c757d;
}

/* 加载中提示样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    color: white;
}

.loading-overlay p {
    margin-top: 15px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        width: 80%;
        z-index: 100;
        transform: translateX(-100%);
        transition: transform 0.3s;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        width: 100%;
    }
    
    .message-content {
        max-width: 90%;
    }
}
