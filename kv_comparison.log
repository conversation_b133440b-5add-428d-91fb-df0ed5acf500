2025-05-28 15:44:00,538 - KVComparison - INFO - 正在加载Excel文件: 预期KV.xlsx
2025-05-28 15:44:00,767 - KVComparison - INFO - 成功加载Excel文件，共 545 行数据
2025-05-28 15:44:00,768 - KVComparison - INFO - 列名: ['用户问题', '输出', '输出.1']
2025-05-28 15:44:00,768 - KVComparison - INFO - 自动识别列失败，请手动选择:
2025-05-28 15:46:50,301 - KVComparison - INFO - 实际输出列: 输出
2025-05-28 15:46:50,301 - KVComparison - INFO - 预期输出列: 输出.1
2025-05-28 15:46:50,301 - KVComparison - INFO - 开始比较所有行的数据...
2025-05-28 15:46:50,310 - KVComparison - INFO - 处理第 1 行...
2025-05-28 15:46:50,311 - <PERSON>VComparison - INFO - 处理第 2 行...
2025-05-28 15:46:50,311 - <PERSON>VComparison - INFO - 处理第 3 行...
2025-05-28 15:46:50,312 - KVComparison - WARNING - 第 3 行不一致: 键'数据日期'值不同: 实际=['20250425', '20250426', '20250427', '20250428', '20250429', '20250430', '20250501', '20250502', '20250503', '20250504', '20250505', '20250506', '20250507', '20250508', '20250509', '20250510', '20250511', '20250512', '20250513', '20250514'], 预期=['20250425', '20250426', '20250427', '20250428', '20250506', '20250507', '20250508', '20250509', '20250510', '20250511', '20250512', '20250513', '20250514']
2025-05-28 15:46:50,313 - KVComparison - INFO - 处理第 4 行...
2025-05-28 15:46:50,313 - KVComparison - INFO - 处理第 5 行...
2025-05-28 15:46:50,314 - KVComparison - INFO - 处理第 6 行...
2025-05-28 15:46:50,314 - KVComparison - INFO - 处理第 7 行...
2025-05-28 15:46:50,315 - KVComparison - INFO - 处理第 8 行...
2025-05-28 15:46:50,315 - KVComparison - INFO - 处理第 9 行...
2025-05-28 15:46:50,316 - KVComparison - INFO - 处理第 10 行...
2025-05-28 15:46:50,316 - KVComparison - INFO - 处理第 11 行...
2025-05-28 15:46:50,317 - KVComparison - INFO - 处理第 12 行...
2025-05-28 15:46:50,318 - KVComparison - INFO - 处理第 13 行...
2025-05-28 15:46:50,318 - KVComparison - INFO - 处理第 14 行...
2025-05-28 15:46:50,319 - KVComparison - INFO - 处理第 15 行...
2025-05-28 15:46:50,320 - KVComparison - INFO - 处理第 16 行...
2025-05-28 15:46:50,321 - KVComparison - INFO - 处理第 17 行...
2025-05-28 15:46:50,321 - KVComparison - INFO - 处理第 18 行...
2025-05-28 15:46:50,322 - KVComparison - INFO - 处理第 19 行...
2025-05-28 15:46:50,322 - KVComparison - INFO - 处理第 20 行...
2025-05-28 15:46:50,323 - KVComparison - INFO - 处理第 21 行...
2025-05-28 15:46:50,324 - KVComparison - INFO - 处理第 22 行...
2025-05-28 15:46:50,324 - KVComparison - INFO - 处理第 23 行...
2025-05-28 15:46:50,325 - KVComparison - INFO - 处理第 24 行...
2025-05-28 15:46:50,325 - KVComparison - INFO - 处理第 25 行...
2025-05-28 15:46:50,326 - KVComparison - INFO - 处理第 26 行...
2025-05-28 15:46:50,327 - KVComparison - INFO - 处理第 27 行...
2025-05-28 15:46:50,327 - KVComparison - INFO - 处理第 28 行...
2025-05-28 15:46:50,328 - KVComparison - INFO - 处理第 29 行...
2025-05-28 15:46:50,329 - KVComparison - INFO - 处理第 30 行...
2025-05-28 15:46:50,329 - KVComparison - INFO - 处理第 31 行...
2025-05-28 15:46:50,330 - KVComparison - INFO - 处理第 32 行...
2025-05-28 15:46:50,330 - KVComparison - INFO - 处理第 33 行...
2025-05-28 15:46:50,331 - KVComparison - INFO - 处理第 34 行...
2025-05-28 15:46:50,332 - KVComparison - INFO - 处理第 35 行...
2025-05-28 15:46:50,333 - KVComparison - INFO - 处理第 36 行...
2025-05-28 15:46:50,333 - KVComparison - INFO - 处理第 37 行...
2025-05-28 15:46:50,333 - KVComparison - INFO - 处理第 38 行...
2025-05-28 15:46:50,334 - KVComparison - INFO - 处理第 39 行...
2025-05-28 15:46:50,334 - KVComparison - INFO - 处理第 40 行...
2025-05-28 15:46:50,335 - KVComparison - INFO - 处理第 41 行...
2025-05-28 15:46:50,335 - KVComparison - INFO - 处理第 42 行...
2025-05-28 15:46:50,336 - KVComparison - INFO - 处理第 43 行...
2025-05-28 15:46:50,336 - KVComparison - INFO - 处理第 44 行...
2025-05-28 15:46:50,337 - KVComparison - INFO - 处理第 45 行...
2025-05-28 15:46:50,337 - KVComparison - INFO - 处理第 46 行...
2025-05-28 15:46:50,338 - KVComparison - INFO - 处理第 47 行...
2025-05-28 15:46:50,338 - KVComparison - INFO - 处理第 48 行...
2025-05-28 15:46:50,339 - KVComparison - INFO - 处理第 49 行...
2025-05-28 15:46:50,340 - KVComparison - INFO - 处理第 50 行...
2025-05-28 15:46:50,340 - KVComparison - INFO - 处理第 51 行...
2025-05-28 15:46:50,341 - KVComparison - INFO - 处理第 52 行...
2025-05-28 15:46:50,341 - KVComparison - INFO - 处理第 53 行...
2025-05-28 15:46:50,341 - KVComparison - INFO - 处理第 54 行...
2025-05-28 15:46:50,343 - KVComparison - INFO - 处理第 55 行...
2025-05-28 15:46:50,344 - KVComparison - INFO - 处理第 56 行...
2025-05-28 15:46:50,345 - KVComparison - INFO - 处理第 57 行...
2025-05-28 15:46:50,345 - KVComparison - INFO - 处理第 58 行...
2025-05-28 15:46:50,346 - KVComparison - INFO - 处理第 59 行...
2025-05-28 15:46:50,347 - KVComparison - INFO - 处理第 60 行...
2025-05-28 15:46:50,347 - KVComparison - INFO - 处理第 61 行...
2025-05-28 15:46:50,348 - KVComparison - INFO - 处理第 62 行...
2025-05-28 15:46:50,348 - KVComparison - INFO - 处理第 63 行...
2025-05-28 15:46:50,349 - KVComparison - INFO - 处理第 64 行...
2025-05-28 15:46:50,349 - KVComparison - INFO - 处理第 65 行...
2025-05-28 15:46:50,350 - KVComparison - INFO - 处理第 66 行...
2025-05-28 15:46:50,351 - KVComparison - INFO - 处理第 67 行...
2025-05-28 15:46:50,351 - KVComparison - INFO - 处理第 68 行...
2025-05-28 15:46:50,352 - KVComparison - INFO - 处理第 69 行...
2025-05-28 15:46:50,353 - KVComparison - INFO - 处理第 70 行...
2025-05-28 15:46:50,353 - KVComparison - INFO - 处理第 71 行...
2025-05-28 15:46:50,354 - KVComparison - INFO - 处理第 72 行...
2025-05-28 15:46:50,355 - KVComparison - INFO - 处理第 73 行...
2025-05-28 15:46:50,355 - KVComparison - INFO - 处理第 74 行...
2025-05-28 15:46:50,356 - KVComparison - INFO - 处理第 75 行...
2025-05-28 15:46:50,356 - KVComparison - INFO - 处理第 76 行...
2025-05-28 15:46:50,357 - KVComparison - INFO - 处理第 77 行...
2025-05-28 15:46:50,357 - KVComparison - INFO - 处理第 78 行...
2025-05-28 15:46:50,358 - KVComparison - INFO - 处理第 79 行...
2025-05-28 15:46:50,358 - KVComparison - INFO - 处理第 80 行...
2025-05-28 15:46:50,359 - KVComparison - INFO - 处理第 81 行...
2025-05-28 15:46:50,360 - KVComparison - INFO - 处理第 82 行...
2025-05-28 15:46:50,360 - KVComparison - INFO - 处理第 83 行...
2025-05-28 15:46:50,361 - KVComparison - INFO - 处理第 84 行...
2025-05-28 15:46:50,361 - KVComparison - INFO - 处理第 85 行...
2025-05-28 15:46:50,362 - KVComparison - INFO - 处理第 86 行...
2025-05-28 15:46:50,362 - KVComparison - INFO - 处理第 87 行...
2025-05-28 15:46:50,363 - KVComparison - INFO - 处理第 88 行...
2025-05-28 15:46:50,363 - KVComparison - INFO - 处理第 89 行...
2025-05-28 15:46:50,364 - KVComparison - INFO - 处理第 90 行...
2025-05-28 15:46:50,365 - KVComparison - INFO - 处理第 91 行...
2025-05-28 15:46:50,365 - KVComparison - INFO - 处理第 92 行...
2025-05-28 15:46:50,366 - KVComparison - INFO - 处理第 93 行...
2025-05-28 15:46:50,366 - KVComparison - INFO - 处理第 94 行...
2025-05-28 15:46:50,367 - KVComparison - INFO - 处理第 95 行...
2025-05-28 15:46:50,367 - KVComparison - INFO - 处理第 96 行...
2025-05-28 15:46:50,368 - KVComparison - INFO - 处理第 97 行...
2025-05-28 15:46:50,368 - KVComparison - INFO - 处理第 98 行...
2025-05-28 15:46:50,369 - KVComparison - INFO - 处理第 99 行...
2025-05-28 15:46:50,370 - KVComparison - INFO - 处理第 100 行...
2025-05-28 15:46:50,370 - KVComparison - INFO - 处理第 101 行...
2025-05-28 15:46:50,371 - KVComparison - INFO - 处理第 102 行...
2025-05-28 15:46:50,371 - KVComparison - INFO - 处理第 103 行...
2025-05-28 15:46:50,372 - KVComparison - INFO - 处理第 104 行...
2025-05-28 15:46:50,372 - KVComparison - INFO - 处理第 105 行...
2025-05-28 15:46:50,373 - KVComparison - INFO - 处理第 106 行...
2025-05-28 15:46:50,373 - KVComparison - INFO - 处理第 107 行...
2025-05-28 15:46:50,374 - KVComparison - INFO - 处理第 108 行...
2025-05-28 15:46:50,375 - KVComparison - INFO - 处理第 109 行...
2025-05-28 15:46:50,375 - KVComparison - INFO - 处理第 110 行...
2025-05-28 15:46:50,376 - KVComparison - INFO - 处理第 111 行...
2025-05-28 15:46:50,376 - KVComparison - INFO - 处理第 112 行...
2025-05-28 15:46:50,377 - KVComparison - INFO - 处理第 113 行...
2025-05-28 15:46:50,378 - KVComparison - INFO - 处理第 114 行...
2025-05-28 15:46:50,378 - KVComparison - INFO - 处理第 115 行...
2025-05-28 15:46:50,379 - KVComparison - INFO - 处理第 116 行...
2025-05-28 15:46:50,379 - KVComparison - INFO - 处理第 117 行...
2025-05-28 15:46:50,380 - KVComparison - INFO - 处理第 118 行...
2025-05-28 15:46:50,380 - KVComparison - INFO - 处理第 119 行...
2025-05-28 15:46:50,381 - KVComparison - INFO - 处理第 120 行...
2025-05-28 15:46:50,382 - KVComparison - INFO - 处理第 121 行...
2025-05-28 15:46:50,382 - KVComparison - INFO - 处理第 122 行...
2025-05-28 15:46:50,383 - KVComparison - INFO - 处理第 123 行...
2025-05-28 15:46:50,384 - KVComparison - INFO - 处理第 124 行...
2025-05-28 15:46:50,384 - KVComparison - INFO - 处理第 125 行...
2025-05-28 15:46:50,385 - KVComparison - INFO - 处理第 126 行...
2025-05-28 15:46:50,385 - KVComparison - INFO - 处理第 127 行...
2025-05-28 15:46:50,386 - KVComparison - INFO - 处理第 128 行...
2025-05-28 15:46:50,387 - KVComparison - INFO - 处理第 129 行...
2025-05-28 15:46:50,387 - KVComparison - INFO - 处理第 130 行...
2025-05-28 15:46:50,388 - KVComparison - INFO - 处理第 131 行...
2025-05-28 15:46:50,388 - KVComparison - INFO - 处理第 132 行...
2025-05-28 15:46:50,389 - KVComparison - INFO - 处理第 133 行...
2025-05-28 15:46:50,389 - KVComparison - INFO - 处理第 134 行...
2025-05-28 15:46:50,390 - KVComparison - INFO - 处理第 135 行...
2025-05-28 15:46:50,390 - KVComparison - INFO - 处理第 136 行...
2025-05-28 15:46:50,391 - KVComparison - INFO - 处理第 137 行...
2025-05-28 15:46:50,391 - KVComparison - INFO - 处理第 138 行...
2025-05-28 15:46:50,392 - KVComparison - INFO - 处理第 139 行...
2025-05-28 15:46:50,392 - KVComparison - INFO - 处理第 140 行...
2025-05-28 15:46:50,392 - KVComparison - INFO - 处理第 141 行...
2025-05-28 15:46:50,393 - KVComparison - INFO - 处理第 142 行...
2025-05-28 15:46:50,393 - KVComparison - INFO - 处理第 143 行...
2025-05-28 15:46:50,394 - KVComparison - INFO - 处理第 144 行...
2025-05-28 15:46:50,394 - KVComparison - INFO - 处理第 145 行...
2025-05-28 15:46:50,395 - KVComparison - INFO - 处理第 146 行...
2025-05-28 15:46:50,395 - KVComparison - INFO - 处理第 147 行...
2025-05-28 15:46:50,396 - KVComparison - INFO - 处理第 148 行...
2025-05-28 15:46:50,396 - KVComparison - INFO - 处理第 149 行...
2025-05-28 15:46:50,397 - KVComparison - INFO - 处理第 150 行...
2025-05-28 15:46:50,397 - KVComparison - INFO - 处理第 151 行...
2025-05-28 15:46:50,398 - KVComparison - INFO - 处理第 152 行...
2025-05-28 15:46:50,398 - KVComparison - INFO - 处理第 153 行...
2025-05-28 15:46:50,399 - KVComparison - INFO - 处理第 154 行...
2025-05-28 15:46:50,399 - KVComparison - INFO - 处理第 155 行...
2025-05-28 15:46:50,400 - KVComparison - INFO - 处理第 156 行...
2025-05-28 15:46:50,401 - KVComparison - INFO - 处理第 157 行...
2025-05-28 15:46:50,401 - KVComparison - INFO - 处理第 158 行...
2025-05-28 15:46:50,402 - KVComparison - INFO - 处理第 159 行...
2025-05-28 15:46:50,402 - KVComparison - INFO - 处理第 160 行...
2025-05-28 15:46:50,403 - KVComparison - INFO - 处理第 161 行...
2025-05-28 15:46:50,403 - KVComparison - INFO - 处理第 162 行...
2025-05-28 15:46:50,404 - KVComparison - INFO - 处理第 163 行...
2025-05-28 15:46:50,404 - KVComparison - INFO - 处理第 164 行...
2025-05-28 15:46:50,405 - KVComparison - INFO - 处理第 165 行...
2025-05-28 15:46:50,405 - KVComparison - INFO - 处理第 166 行...
2025-05-28 15:46:50,406 - KVComparison - INFO - 处理第 167 行...
2025-05-28 15:46:50,407 - KVComparison - INFO - 处理第 168 行...
2025-05-28 15:46:50,407 - KVComparison - INFO - 处理第 169 行...
2025-05-28 15:46:50,408 - KVComparison - INFO - 处理第 170 行...
2025-05-28 15:46:50,408 - KVComparison - INFO - 处理第 171 行...
2025-05-28 15:46:50,409 - KVComparison - INFO - 处理第 172 行...
2025-05-28 15:46:50,409 - KVComparison - INFO - 处理第 173 行...
2025-05-28 15:46:50,410 - KVComparison - INFO - 处理第 174 行...
2025-05-28 15:46:50,411 - KVComparison - INFO - 处理第 175 行...
2025-05-28 15:46:50,411 - KVComparison - INFO - 处理第 176 行...
2025-05-28 15:46:50,412 - KVComparison - INFO - 处理第 177 行...
2025-05-28 15:46:50,412 - KVComparison - INFO - 处理第 178 行...
2025-05-28 15:46:50,413 - KVComparison - INFO - 处理第 179 行...
2025-05-28 15:46:50,413 - KVComparison - INFO - 处理第 180 行...
2025-05-28 15:46:50,414 - KVComparison - INFO - 处理第 181 行...
2025-05-28 15:46:50,414 - KVComparison - INFO - 处理第 182 行...
2025-05-28 15:46:50,415 - KVComparison - INFO - 处理第 183 行...
2025-05-28 15:46:50,416 - KVComparison - INFO - 处理第 184 行...
2025-05-28 15:46:50,416 - KVComparison - INFO - 处理第 185 行...
2025-05-28 15:46:50,417 - KVComparison - INFO - 处理第 186 行...
2025-05-28 15:46:50,417 - KVComparison - INFO - 处理第 187 行...
2025-05-28 15:46:50,418 - KVComparison - INFO - 处理第 188 行...
2025-05-28 15:46:50,418 - KVComparison - INFO - 处理第 189 行...
2025-05-28 15:46:50,419 - KVComparison - INFO - 处理第 190 行...
2025-05-28 15:46:50,419 - KVComparison - INFO - 处理第 191 行...
2025-05-28 15:46:50,419 - KVComparison - INFO - 处理第 192 行...
2025-05-28 15:46:50,420 - KVComparison - INFO - 处理第 193 行...
2025-05-28 15:46:50,420 - KVComparison - INFO - 处理第 194 行...
2025-05-28 15:46:50,421 - KVComparison - INFO - 处理第 195 行...
2025-05-28 15:46:50,421 - KVComparison - INFO - 处理第 196 行...
2025-05-28 15:46:50,422 - KVComparison - INFO - 处理第 197 行...
2025-05-28 15:46:50,422 - KVComparison - INFO - 处理第 198 行...
2025-05-28 15:46:50,423 - KVComparison - INFO - 处理第 199 行...
2025-05-28 15:46:50,423 - KVComparison - INFO - 处理第 200 行...
2025-05-28 15:46:50,423 - KVComparison - INFO - 处理第 201 行...
2025-05-28 15:46:50,424 - KVComparison - INFO - 处理第 202 行...
2025-05-28 15:46:50,424 - KVComparison - INFO - 处理第 203 行...
2025-05-28 15:46:50,425 - KVComparison - INFO - 处理第 204 行...
2025-05-28 15:46:50,425 - KVComparison - INFO - 处理第 205 行...
2025-05-28 15:46:50,426 - KVComparison - INFO - 处理第 206 行...
2025-05-28 15:46:50,426 - KVComparison - WARNING - 无法从文本中提取JSON: 提取失败...
2025-05-28 15:46:50,427 - KVComparison - WARNING - 无法从文本中提取JSON: 提取失败...
2025-05-28 15:46:50,427 - KVComparison - INFO - 处理第 207 行...
2025-05-28 15:46:50,428 - KVComparison - INFO - 处理第 208 行...
2025-05-28 15:46:50,428 - KVComparison - INFO - 处理第 209 行...
2025-05-28 15:46:50,428 - KVComparison - INFO - 处理第 210 行...
2025-05-28 15:46:50,429 - KVComparison - INFO - 处理第 211 行...
2025-05-28 15:46:50,429 - KVComparison - INFO - 处理第 212 行...
2025-05-28 15:46:50,429 - KVComparison - INFO - 处理第 213 行...
2025-05-28 15:46:50,430 - KVComparison - INFO - 处理第 214 行...
2025-05-28 15:46:50,430 - KVComparison - INFO - 处理第 215 行...
2025-05-28 15:46:50,431 - KVComparison - INFO - 处理第 216 行...
2025-05-28 15:46:50,431 - KVComparison - INFO - 处理第 217 行...
2025-05-28 15:46:50,432 - KVComparison - INFO - 处理第 218 行...
2025-05-28 15:46:50,432 - KVComparison - INFO - 处理第 219 行...
2025-05-28 15:46:50,433 - KVComparison - INFO - 处理第 220 行...
2025-05-28 15:46:50,433 - KVComparison - INFO - 处理第 221 行...
2025-05-28 15:46:50,434 - KVComparison - INFO - 处理第 222 行...
2025-05-28 15:46:50,434 - KVComparison - INFO - 处理第 223 行...
2025-05-28 15:46:50,435 - KVComparison - INFO - 处理第 224 行...
2025-05-28 15:46:50,435 - KVComparison - INFO - 处理第 225 行...
2025-05-28 15:46:50,435 - KVComparison - INFO - 处理第 226 行...
2025-05-28 15:46:50,436 - KVComparison - INFO - 处理第 227 行...
2025-05-28 15:46:50,436 - KVComparison - INFO - 处理第 228 行...
2025-05-28 15:46:50,437 - KVComparison - INFO - 处理第 229 行...
2025-05-28 15:46:50,437 - KVComparison - INFO - 处理第 230 行...
2025-05-28 15:46:50,438 - KVComparison - INFO - 处理第 231 行...
2025-05-28 15:46:50,438 - KVComparison - INFO - 处理第 232 行...
2025-05-28 15:46:50,438 - KVComparison - INFO - 处理第 233 行...
2025-05-28 15:46:50,439 - KVComparison - INFO - 处理第 234 行...
2025-05-28 15:46:50,439 - KVComparison - INFO - 处理第 235 行...
2025-05-28 15:46:50,440 - KVComparison - INFO - 处理第 236 行...
2025-05-28 15:46:50,440 - KVComparison - INFO - 处理第 237 行...
2025-05-28 15:46:50,441 - KVComparison - INFO - 处理第 238 行...
2025-05-28 15:46:50,441 - KVComparison - INFO - 处理第 239 行...
2025-05-28 15:46:50,442 - KVComparison - INFO - 处理第 240 行...
2025-05-28 15:46:50,442 - KVComparison - INFO - 处理第 241 行...
2025-05-28 15:46:50,443 - KVComparison - INFO - 处理第 242 行...
2025-05-28 15:46:50,443 - KVComparison - INFO - 处理第 243 行...
2025-05-28 15:46:50,444 - KVComparison - INFO - 处理第 244 行...
2025-05-28 15:46:50,444 - KVComparison - INFO - 处理第 245 行...
2025-05-28 15:46:50,445 - KVComparison - INFO - 处理第 246 行...
2025-05-28 15:46:50,445 - KVComparison - INFO - 处理第 247 行...
2025-05-28 15:46:50,445 - KVComparison - INFO - 处理第 248 行...
2025-05-28 15:46:50,446 - KVComparison - INFO - 处理第 249 行...
2025-05-28 15:46:50,446 - KVComparison - INFO - 处理第 250 行...
2025-05-28 15:46:50,446 - KVComparison - INFO - 处理第 251 行...
2025-05-28 15:46:50,447 - KVComparison - INFO - 处理第 252 行...
2025-05-28 15:46:50,447 - KVComparison - INFO - 处理第 253 行...
2025-05-28 15:46:50,447 - KVComparison - INFO - 处理第 254 行...
2025-05-28 15:46:50,448 - KVComparison - INFO - 处理第 255 行...
2025-05-28 15:46:50,448 - KVComparison - INFO - 处理第 256 行...
2025-05-28 15:46:50,449 - KVComparison - INFO - 处理第 257 行...
2025-05-28 15:46:50,449 - KVComparison - INFO - 处理第 258 行...
2025-05-28 15:46:50,450 - KVComparison - INFO - 处理第 259 行...
2025-05-28 15:46:50,450 - KVComparison - INFO - 处理第 260 行...
2025-05-28 15:46:50,451 - KVComparison - INFO - 处理第 261 行...
2025-05-28 15:46:50,451 - KVComparison - INFO - 处理第 262 行...
2025-05-28 15:46:50,453 - KVComparison - INFO - 处理第 263 行...
2025-05-28 15:46:50,453 - KVComparison - INFO - 处理第 264 行...
2025-05-28 15:46:50,454 - KVComparison - INFO - 处理第 265 行...
2025-05-28 15:46:50,454 - KVComparison - INFO - 处理第 266 行...
2025-05-28 15:46:50,455 - KVComparison - INFO - 处理第 267 行...
2025-05-28 15:46:50,455 - KVComparison - INFO - 处理第 268 行...
2025-05-28 15:46:50,456 - KVComparison - INFO - 处理第 269 行...
2025-05-28 15:46:50,456 - KVComparison - WARNING - 无法从文本中提取JSON: 提取失败...
2025-05-28 15:46:50,457 - KVComparison - WARNING - 无法从文本中提取JSON: 提取失败...
2025-05-28 15:46:50,457 - KVComparison - INFO - 处理第 270 行...
2025-05-28 15:46:50,458 - KVComparison - INFO - 处理第 271 行...
2025-05-28 15:46:50,459 - KVComparison - INFO - 处理第 272 行...
2025-05-28 15:46:50,459 - KVComparison - INFO - 处理第 273 行...
2025-05-28 15:46:50,460 - KVComparison - INFO - 处理第 274 行...
2025-05-28 15:46:50,461 - KVComparison - INFO - 处理第 275 行...
2025-05-28 15:46:50,461 - KVComparison - INFO - 处理第 276 行...
2025-05-28 15:46:50,462 - KVComparison - INFO - 处理第 277 行...
2025-05-28 15:46:50,462 - KVComparison - INFO - 处理第 278 行...
2025-05-28 15:46:50,463 - KVComparison - INFO - 处理第 279 行...
2025-05-28 15:46:50,463 - KVComparison - INFO - 处理第 280 行...
2025-05-28 15:46:50,463 - KVComparison - INFO - 处理第 281 行...
2025-05-28 15:46:50,464 - KVComparison - INFO - 处理第 282 行...
2025-05-28 15:46:50,464 - KVComparison - INFO - 处理第 283 行...
2025-05-28 15:46:50,465 - KVComparison - INFO - 处理第 284 行...
2025-05-28 15:46:50,465 - KVComparison - INFO - 处理第 285 行...
2025-05-28 15:46:50,466 - KVComparison - INFO - 处理第 286 行...
2025-05-28 15:46:50,467 - KVComparison - INFO - 处理第 287 行...
2025-05-28 15:46:50,467 - KVComparison - INFO - 处理第 288 行...
2025-05-28 15:46:50,468 - KVComparison - INFO - 处理第 289 行...
2025-05-28 15:46:50,468 - KVComparison - INFO - 处理第 290 行...
2025-05-28 15:46:50,468 - KVComparison - INFO - 处理第 291 行...
2025-05-28 15:46:50,469 - KVComparison - INFO - 处理第 292 行...
2025-05-28 15:46:50,469 - KVComparison - INFO - 处理第 293 行...
2025-05-28 15:46:50,469 - KVComparison - INFO - 处理第 294 行...
2025-05-28 15:46:50,470 - KVComparison - INFO - 处理第 295 行...
2025-05-28 15:46:50,470 - KVComparison - INFO - 处理第 296 行...
2025-05-28 15:46:50,471 - KVComparison - INFO - 处理第 297 行...
2025-05-28 15:46:50,471 - KVComparison - INFO - 处理第 298 行...
2025-05-28 15:46:50,472 - KVComparison - INFO - 处理第 299 行...
2025-05-28 15:46:50,472 - KVComparison - INFO - 处理第 300 行...
2025-05-28 15:46:50,473 - KVComparison - INFO - 处理第 301 行...
2025-05-28 15:46:50,473 - KVComparison - INFO - 处理第 302 行...
2025-05-28 15:46:50,473 - KVComparison - INFO - 处理第 303 行...
2025-05-28 15:46:50,474 - KVComparison - INFO - 处理第 304 行...
2025-05-28 15:46:50,474 - KVComparison - INFO - 处理第 305 行...
2025-05-28 15:46:50,475 - KVComparison - INFO - 处理第 306 行...
2025-05-28 15:46:50,475 - KVComparison - INFO - 处理第 307 行...
2025-05-28 15:46:50,476 - KVComparison - INFO - 处理第 308 行...
2025-05-28 15:46:50,476 - KVComparison - INFO - 处理第 309 行...
2025-05-28 15:46:50,477 - KVComparison - INFO - 处理第 310 行...
2025-05-28 15:46:50,478 - KVComparison - INFO - 处理第 311 行...
2025-05-28 15:46:50,478 - KVComparison - INFO - 处理第 312 行...
2025-05-28 15:46:50,479 - KVComparison - INFO - 处理第 313 行...
2025-05-28 15:46:50,479 - KVComparison - INFO - 处理第 314 行...
2025-05-28 15:46:50,480 - KVComparison - INFO - 处理第 315 行...
2025-05-28 15:46:50,480 - KVComparison - INFO - 处理第 316 行...
2025-05-28 15:46:50,481 - KVComparison - INFO - 处理第 317 行...
2025-05-28 15:46:50,481 - KVComparison - INFO - 处理第 318 行...
2025-05-28 15:46:50,481 - KVComparison - INFO - 处理第 319 行...
2025-05-28 15:46:50,482 - KVComparison - INFO - 处理第 320 行...
2025-05-28 15:46:50,482 - KVComparison - INFO - 处理第 321 行...
2025-05-28 15:46:50,483 - KVComparison - INFO - 处理第 322 行...
2025-05-28 15:46:50,483 - KVComparison - INFO - 处理第 323 行...
2025-05-28 15:46:50,483 - KVComparison - INFO - 处理第 324 行...
2025-05-28 15:46:50,484 - KVComparison - INFO - 处理第 325 行...
2025-05-28 15:46:50,484 - KVComparison - INFO - 处理第 326 行...
2025-05-28 15:46:50,485 - KVComparison - INFO - 处理第 327 行...
2025-05-28 15:46:50,485 - KVComparison - INFO - 处理第 328 行...
2025-05-28 15:46:50,486 - KVComparison - INFO - 处理第 329 行...
2025-05-28 15:46:50,486 - KVComparison - INFO - 处理第 330 行...
2025-05-28 15:46:50,486 - KVComparison - INFO - 处理第 331 行...
2025-05-28 15:46:50,487 - KVComparison - INFO - 处理第 332 行...
2025-05-28 15:46:50,488 - KVComparison - INFO - 处理第 333 行...
2025-05-28 15:46:50,488 - KVComparison - INFO - 处理第 334 行...
2025-05-28 15:46:50,489 - KVComparison - INFO - 处理第 335 行...
2025-05-28 15:46:50,489 - KVComparison - INFO - 处理第 336 行...
2025-05-28 15:46:50,489 - KVComparison - INFO - 处理第 337 行...
2025-05-28 15:46:50,490 - KVComparison - INFO - 处理第 338 行...
2025-05-28 15:46:50,490 - KVComparison - INFO - 处理第 339 行...
2025-05-28 15:46:50,491 - KVComparison - INFO - 处理第 340 行...
2025-05-28 15:46:50,491 - KVComparison - INFO - 处理第 341 行...
2025-05-28 15:46:50,492 - KVComparison - INFO - 处理第 342 行...
2025-05-28 15:46:50,492 - KVComparison - INFO - 处理第 343 行...
2025-05-28 15:46:50,492 - KVComparison - INFO - 处理第 344 行...
2025-05-28 15:46:50,493 - KVComparison - INFO - 处理第 345 行...
2025-05-28 15:46:50,494 - KVComparison - INFO - 处理第 346 行...
2025-05-28 15:46:50,495 - KVComparison - INFO - 处理第 347 行...
2025-05-28 15:46:50,495 - KVComparison - INFO - 处理第 348 行...
2025-05-28 15:46:50,496 - KVComparison - INFO - 处理第 349 行...
2025-05-28 15:46:50,496 - KVComparison - INFO - 处理第 350 行...
2025-05-28 15:46:50,497 - KVComparison - INFO - 处理第 351 行...
2025-05-28 15:46:50,497 - KVComparison - INFO - 处理第 352 行...
2025-05-28 15:46:50,497 - KVComparison - INFO - 处理第 353 行...
2025-05-28 15:46:50,498 - KVComparison - INFO - 处理第 354 行...
2025-05-28 15:46:50,498 - KVComparison - INFO - 处理第 355 行...
2025-05-28 15:46:50,499 - KVComparison - INFO - 处理第 356 行...
2025-05-28 15:46:50,499 - KVComparison - INFO - 处理第 357 行...
2025-05-28 15:46:50,500 - KVComparison - INFO - 处理第 358 行...
2025-05-28 15:46:50,500 - KVComparison - INFO - 处理第 359 行...
2025-05-28 15:46:50,501 - KVComparison - INFO - 处理第 360 行...
2025-05-28 15:46:50,501 - KVComparison - INFO - 处理第 361 行...
2025-05-28 15:46:50,501 - KVComparison - INFO - 处理第 362 行...
2025-05-28 15:46:50,501 - KVComparison - INFO - 处理第 363 行...
2025-05-28 15:46:50,502 - KVComparison - INFO - 处理第 364 行...
2025-05-28 15:46:50,502 - KVComparison - INFO - 处理第 365 行...
2025-05-28 15:46:50,503 - KVComparison - INFO - 处理第 366 行...
2025-05-28 15:46:50,503 - KVComparison - INFO - 处理第 367 行...
2025-05-28 15:46:50,504 - KVComparison - INFO - 处理第 368 行...
2025-05-28 15:46:50,505 - KVComparison - INFO - 处理第 369 行...
2025-05-28 15:46:50,506 - KVComparison - INFO - 处理第 370 行...
2025-05-28 15:46:50,506 - KVComparison - INFO - 处理第 371 行...
2025-05-28 15:46:50,507 - KVComparison - INFO - 处理第 372 行...
2025-05-28 15:46:50,507 - KVComparison - INFO - 处理第 373 行...
2025-05-28 15:46:50,507 - KVComparison - INFO - 处理第 374 行...
2025-05-28 15:46:50,508 - KVComparison - INFO - 处理第 375 行...
2025-05-28 15:46:50,508 - KVComparison - INFO - 处理第 376 行...
2025-05-28 15:46:50,509 - KVComparison - INFO - 处理第 377 行...
2025-05-28 15:46:50,509 - KVComparison - INFO - 处理第 378 行...
2025-05-28 15:46:50,510 - KVComparison - INFO - 处理第 379 行...
2025-05-28 15:46:50,510 - KVComparison - INFO - 处理第 380 行...
2025-05-28 15:46:50,510 - KVComparison - INFO - 处理第 381 行...
2025-05-28 15:46:50,511 - KVComparison - INFO - 处理第 382 行...
2025-05-28 15:46:50,511 - KVComparison - INFO - 处理第 383 行...
2025-05-28 15:46:50,512 - KVComparison - INFO - 处理第 384 行...
2025-05-28 15:46:50,512 - KVComparison - INFO - 处理第 385 行...
2025-05-28 15:46:50,512 - KVComparison - INFO - 处理第 386 行...
2025-05-28 15:46:50,513 - KVComparison - INFO - 处理第 387 行...
2025-05-28 15:46:50,513 - KVComparison - INFO - 处理第 388 行...
2025-05-28 15:46:50,514 - KVComparison - INFO - 处理第 389 行...
2025-05-28 15:46:50,514 - KVComparison - INFO - 处理第 390 行...
2025-05-28 15:46:50,515 - KVComparison - INFO - 处理第 391 行...
2025-05-28 15:46:50,515 - KVComparison - INFO - 处理第 392 行...
2025-05-28 15:46:50,516 - KVComparison - INFO - 处理第 393 行...
2025-05-28 15:46:50,516 - KVComparison - INFO - 处理第 394 行...
2025-05-28 15:46:50,516 - KVComparison - INFO - 处理第 395 行...
2025-05-28 15:46:50,517 - KVComparison - INFO - 处理第 396 行...
2025-05-28 15:46:50,517 - KVComparison - INFO - 处理第 397 行...
2025-05-28 15:46:50,518 - KVComparison - INFO - 处理第 398 行...
2025-05-28 15:46:50,518 - KVComparison - INFO - 处理第 399 行...
2025-05-28 15:46:50,519 - KVComparison - INFO - 处理第 400 行...
2025-05-28 15:46:50,519 - KVComparison - INFO - 处理第 401 行...
2025-05-28 15:46:50,519 - KVComparison - INFO - 处理第 402 行...
2025-05-28 15:46:50,519 - KVComparison - INFO - 处理第 403 行...
2025-05-28 15:46:50,520 - KVComparison - INFO - 处理第 404 行...
2025-05-28 15:46:50,520 - KVComparison - INFO - 处理第 405 行...
2025-05-28 15:46:50,520 - KVComparison - INFO - 处理第 406 行...
2025-05-28 15:46:50,521 - KVComparison - INFO - 处理第 407 行...
2025-05-28 15:46:50,521 - KVComparison - INFO - 处理第 408 行...
2025-05-28 15:46:50,522 - KVComparison - INFO - 处理第 409 行...
2025-05-28 15:46:50,522 - KVComparison - INFO - 处理第 410 行...
2025-05-28 15:46:50,522 - KVComparison - INFO - 处理第 411 行...
2025-05-28 15:46:50,522 - KVComparison - INFO - 处理第 412 行...
2025-05-28 15:46:50,523 - KVComparison - INFO - 处理第 413 行...
2025-05-28 15:46:50,523 - KVComparison - INFO - 处理第 414 行...
2025-05-28 15:46:50,523 - KVComparison - INFO - 处理第 415 行...
2025-05-28 15:46:50,524 - KVComparison - INFO - 处理第 416 行...
2025-05-28 15:46:50,524 - KVComparison - INFO - 处理第 417 行...
2025-05-28 15:46:50,524 - KVComparison - INFO - 处理第 418 行...
2025-05-28 15:46:50,525 - KVComparison - INFO - 处理第 419 行...
2025-05-28 15:46:50,525 - KVComparison - INFO - 处理第 420 行...
2025-05-28 15:46:50,525 - KVComparison - INFO - 处理第 421 行...
2025-05-28 15:46:50,526 - KVComparison - INFO - 处理第 422 行...
2025-05-28 15:46:50,526 - KVComparison - INFO - 处理第 423 行...
2025-05-28 15:46:50,526 - KVComparison - INFO - 处理第 424 行...
2025-05-28 15:46:50,527 - KVComparison - INFO - 处理第 425 行...
2025-05-28 15:46:50,527 - KVComparison - INFO - 处理第 426 行...
2025-05-28 15:46:50,527 - KVComparison - INFO - 处理第 427 行...
2025-05-28 15:46:50,528 - KVComparison - INFO - 处理第 428 行...
2025-05-28 15:46:50,528 - KVComparison - INFO - 处理第 429 行...
2025-05-28 15:46:50,528 - KVComparison - INFO - 处理第 430 行...
2025-05-28 15:46:50,529 - KVComparison - INFO - 处理第 431 行...
2025-05-28 15:46:50,529 - KVComparison - INFO - 处理第 432 行...
2025-05-28 15:46:50,529 - KVComparison - INFO - 处理第 433 行...
2025-05-28 15:46:50,530 - KVComparison - INFO - 处理第 434 行...
2025-05-28 15:46:50,530 - KVComparison - INFO - 处理第 435 行...
2025-05-28 15:46:50,530 - KVComparison - INFO - 处理第 436 行...
2025-05-28 15:46:50,531 - KVComparison - INFO - 处理第 437 行...
2025-05-28 15:46:50,531 - KVComparison - INFO - 处理第 438 行...
2025-05-28 15:46:50,531 - KVComparison - INFO - 处理第 439 行...
2025-05-28 15:46:50,532 - KVComparison - INFO - 处理第 440 行...
2025-05-28 15:46:50,532 - KVComparison - INFO - 处理第 441 行...
2025-05-28 15:46:50,532 - KVComparison - INFO - 处理第 442 行...
2025-05-28 15:46:50,533 - KVComparison - INFO - 处理第 443 行...
2025-05-28 15:46:50,533 - KVComparison - INFO - 处理第 444 行...
2025-05-28 15:46:50,534 - KVComparison - INFO - 处理第 445 行...
2025-05-28 15:46:50,534 - KVComparison - INFO - 处理第 446 行...
2025-05-28 15:46:50,535 - KVComparison - INFO - 处理第 447 行...
2025-05-28 15:46:50,536 - KVComparison - INFO - 处理第 448 行...
2025-05-28 15:46:50,536 - KVComparison - INFO - 处理第 449 行...
2025-05-28 15:46:50,536 - KVComparison - INFO - 处理第 450 行...
2025-05-28 15:46:50,537 - KVComparison - INFO - 处理第 451 行...
2025-05-28 15:46:50,537 - KVComparison - INFO - 处理第 452 行...
2025-05-28 15:46:50,537 - KVComparison - INFO - 处理第 453 行...
2025-05-28 15:46:50,538 - KVComparison - INFO - 处理第 454 行...
2025-05-28 15:46:50,538 - KVComparison - INFO - 处理第 455 行...
2025-05-28 15:46:50,538 - KVComparison - INFO - 处理第 456 行...
2025-05-28 15:46:50,539 - KVComparison - INFO - 处理第 457 行...
2025-05-28 15:46:50,539 - KVComparison - INFO - 处理第 458 行...
2025-05-28 15:46:50,539 - KVComparison - INFO - 处理第 459 行...
2025-05-28 15:46:50,540 - KVComparison - INFO - 处理第 460 行...
2025-05-28 15:46:50,541 - KVComparison - INFO - 处理第 461 行...
2025-05-28 15:46:50,541 - KVComparison - INFO - 处理第 462 行...
2025-05-28 15:46:50,541 - KVComparison - INFO - 处理第 463 行...
2025-05-28 15:46:50,541 - KVComparison - INFO - 处理第 464 行...
2025-05-28 15:46:50,541 - KVComparison - INFO - 处理第 465 行...
2025-05-28 15:46:50,542 - KVComparison - INFO - 处理第 466 行...
2025-05-28 15:46:50,542 - KVComparison - INFO - 处理第 467 行...
2025-05-28 15:46:50,543 - KVComparison - INFO - 处理第 468 行...
2025-05-28 15:46:50,543 - KVComparison - INFO - 处理第 469 行...
2025-05-28 15:46:50,543 - KVComparison - INFO - 处理第 470 行...
2025-05-28 15:46:50,544 - KVComparison - INFO - 处理第 471 行...
2025-05-28 15:46:50,545 - KVComparison - INFO - 处理第 472 行...
2025-05-28 15:46:50,545 - KVComparison - INFO - 处理第 473 行...
2025-05-28 15:46:50,546 - KVComparison - INFO - 处理第 474 行...
2025-05-28 15:46:50,546 - KVComparison - INFO - 处理第 475 行...
2025-05-28 15:46:50,546 - KVComparison - INFO - 处理第 476 行...
2025-05-28 15:46:50,546 - KVComparison - INFO - 处理第 477 行...
2025-05-28 15:46:50,547 - KVComparison - INFO - 处理第 478 行...
2025-05-28 15:46:50,548 - KVComparison - INFO - 处理第 479 行...
2025-05-28 15:46:50,548 - KVComparison - INFO - 处理第 480 行...
2025-05-28 15:46:50,548 - KVComparison - INFO - 处理第 481 行...
2025-05-28 15:46:50,549 - KVComparison - INFO - 处理第 482 行...
2025-05-28 15:46:50,550 - KVComparison - INFO - 处理第 483 行...
2025-05-28 15:46:50,550 - KVComparison - INFO - 处理第 484 行...
2025-05-28 15:46:50,551 - KVComparison - INFO - 处理第 485 行...
2025-05-28 15:46:50,552 - KVComparison - INFO - 处理第 486 行...
2025-05-28 15:46:50,552 - KVComparison - INFO - 处理第 487 行...
2025-05-28 15:46:50,552 - KVComparison - INFO - 处理第 488 行...
2025-05-28 15:46:50,553 - KVComparison - INFO - 处理第 489 行...
2025-05-28 15:46:50,553 - KVComparison - INFO - 处理第 490 行...
2025-05-28 15:46:50,554 - KVComparison - INFO - 处理第 491 行...
2025-05-28 15:46:50,555 - KVComparison - INFO - 处理第 492 行...
2025-05-28 15:46:50,555 - KVComparison - INFO - 处理第 493 行...
2025-05-28 15:46:50,555 - KVComparison - INFO - 处理第 494 行...
2025-05-28 15:46:50,556 - KVComparison - INFO - 处理第 495 行...
2025-05-28 15:46:50,556 - KVComparison - INFO - 处理第 496 行...
2025-05-28 15:46:50,556 - KVComparison - INFO - 处理第 497 行...
2025-05-28 15:46:50,557 - KVComparison - INFO - 处理第 498 行...
2025-05-28 15:46:50,557 - KVComparison - INFO - 处理第 499 行...
2025-05-28 15:46:50,557 - KVComparison - INFO - 处理第 500 行...
2025-05-28 15:46:50,558 - KVComparison - INFO - 处理第 501 行...
2025-05-28 15:46:50,558 - KVComparison - INFO - 处理第 502 行...
2025-05-28 15:46:50,558 - KVComparison - INFO - 处理第 503 行...
2025-05-28 15:46:50,559 - KVComparison - INFO - 处理第 504 行...
2025-05-28 15:46:50,559 - KVComparison - INFO - 处理第 505 行...
2025-05-28 15:46:50,560 - KVComparison - INFO - 处理第 506 行...
2025-05-28 15:46:50,560 - KVComparison - INFO - 处理第 507 行...
2025-05-28 15:46:50,561 - KVComparison - INFO - 处理第 508 行...
2025-05-28 15:46:50,561 - KVComparison - INFO - 处理第 509 行...
2025-05-28 15:46:50,561 - KVComparison - INFO - 处理第 510 行...
2025-05-28 15:46:50,562 - KVComparison - INFO - 处理第 511 行...
2025-05-28 15:46:50,562 - KVComparison - INFO - 处理第 512 行...
2025-05-28 15:46:50,563 - KVComparison - INFO - 处理第 513 行...
2025-05-28 15:46:50,563 - KVComparison - INFO - 处理第 514 行...
2025-05-28 15:46:50,564 - KVComparison - INFO - 处理第 515 行...
2025-05-28 15:46:50,564 - KVComparison - INFO - 处理第 516 行...
2025-05-28 15:46:50,564 - KVComparison - INFO - 处理第 517 行...
2025-05-28 15:46:50,565 - KVComparison - INFO - 处理第 518 行...
2025-05-28 15:46:50,566 - KVComparison - INFO - 处理第 519 行...
2025-05-28 15:46:50,566 - KVComparison - INFO - 处理第 520 行...
2025-05-28 15:46:50,566 - KVComparison - INFO - 处理第 521 行...
2025-05-28 15:46:50,567 - KVComparison - INFO - 处理第 522 行...
2025-05-28 15:46:50,567 - KVComparison - INFO - 处理第 523 行...
2025-05-28 15:46:50,567 - KVComparison - INFO - 处理第 524 行...
2025-05-28 15:46:50,568 - KVComparison - INFO - 处理第 525 行...
2025-05-28 15:46:50,568 - KVComparison - INFO - 处理第 526 行...
2025-05-28 15:46:50,568 - KVComparison - INFO - 处理第 527 行...
2025-05-28 15:46:50,569 - KVComparison - INFO - 处理第 528 行...
2025-05-28 15:46:50,569 - KVComparison - INFO - 处理第 529 行...
2025-05-28 15:46:50,570 - KVComparison - INFO - 处理第 530 行...
2025-05-28 15:46:50,570 - KVComparison - INFO - 处理第 531 行...
2025-05-28 15:46:50,570 - KVComparison - INFO - 处理第 532 行...
2025-05-28 15:46:50,571 - KVComparison - INFO - 处理第 533 行...
2025-05-28 15:46:50,571 - KVComparison - INFO - 处理第 534 行...
2025-05-28 15:46:50,572 - KVComparison - INFO - 处理第 535 行...
2025-05-28 15:46:50,572 - KVComparison - INFO - 处理第 536 行...
2025-05-28 15:46:50,572 - KVComparison - INFO - 处理第 537 行...
2025-05-28 15:46:50,573 - KVComparison - INFO - 处理第 538 行...
2025-05-28 15:46:50,573 - KVComparison - INFO - 处理第 539 行...
2025-05-28 15:46:50,573 - KVComparison - INFO - 处理第 540 行...
2025-05-28 15:46:50,574 - KVComparison - INFO - 处理第 541 行...
2025-05-28 15:46:50,574 - KVComparison - INFO - 处理第 542 行...
2025-05-28 15:46:50,574 - KVComparison - INFO - 处理第 543 行...
2025-05-28 15:46:50,575 - KVComparison - INFO - 处理第 544 行...
2025-05-28 15:46:50,575 - KVComparison - INFO - 处理第 545 行...
2025-05-28 15:46:50,575 - KVComparison - INFO - 比较完成: 一致 544 行, 不一致 1 行
2025-05-28 15:46:50,722 - KVComparison - INFO - 比较结果已保存到: KV比较结果_20250528_154650.xlsx
2025-05-28 15:46:50,752 - KVComparison - INFO - 详细结果已保存到: KV比较详细结果_20250528_154650.json
2025-05-29 09:20:29,537 - KVComparison - INFO - 正在加载Excel文件: 预期KV.xlsx
2025-05-29 09:20:30,037 - KVComparison - INFO - 成功加载Excel文件，共 545 行数据
2025-05-29 09:20:30,037 - KVComparison - INFO - 列名: ['用户问题', '输出', '输出.1']
2025-05-29 09:20:30,037 - KVComparison - INFO - 自动识别列失败，请手动选择:
2025-05-29 09:22:48,566 - KVComparison - INFO - 实际输出列: 输出
2025-05-29 09:22:48,567 - KVComparison - INFO - 预期输出列: 输出.1
2025-05-29 09:22:48,567 - KVComparison - INFO - 开始比较所有行的数据...
2025-05-29 09:22:48,573 - KVComparison - INFO - 处理第 1 行...
2025-05-29 09:22:48,574 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,574 - KVComparison - INFO - 处理第 2 行...
2025-05-29 09:22:48,574 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,575 - KVComparison - INFO - 处理第 3 行...
2025-05-29 09:22:48,575 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,575 - KVComparison - INFO - 处理第 4 行...
2025-05-29 09:22:48,576 - KVComparison - INFO - 处理第 5 行...
2025-05-29 09:22:48,576 - KVComparison - INFO - 处理第 6 行...
2025-05-29 09:22:48,576 - KVComparison - INFO - 处理第 7 行...
2025-05-29 09:22:48,577 - KVComparison - INFO - 处理第 8 行...
2025-05-29 09:22:48,577 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,577 - KVComparison - INFO - 处理第 9 行...
2025-05-29 09:22:48,577 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,577 - KVComparison - INFO - 处理第 10 行...
2025-05-29 09:22:48,578 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,578 - KVComparison - INFO - 处理第 11 行...
2025-05-29 09:22:48,578 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,578 - KVComparison - INFO - 处理第 12 行...
2025-05-29 09:22:48,578 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,579 - KVComparison - INFO - 处理第 13 行...
2025-05-29 09:22:48,579 - KVComparison - INFO - 处理第 14 行...
2025-05-29 09:22:48,579 - KVComparison - INFO - 处理第 15 行...
2025-05-29 09:22:48,579 - KVComparison - INFO - 处理第 16 行...
2025-05-29 09:22:48,580 - KVComparison - INFO - 处理第 17 行...
2025-05-29 09:22:48,580 - KVComparison - INFO - 处理第 18 行...
2025-05-29 09:22:48,580 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,580 - KVComparison - INFO - 处理第 19 行...
2025-05-29 09:22:48,580 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,581 - KVComparison - INFO - 处理第 20 行...
2025-05-29 09:22:48,581 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,581 - KVComparison - INFO - 处理第 21 行...
2025-05-29 09:22:48,581 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,581 - KVComparison - INFO - 处理第 22 行...
2025-05-29 09:22:48,582 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,582 - KVComparison - INFO - 处理第 23 行...
2025-05-29 09:22:48,582 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,582 - KVComparison - INFO - 处理第 24 行...
2025-05-29 09:22:48,582 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,583 - KVComparison - INFO - 处理第 25 行...
2025-05-29 09:22:48,583 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,583 - KVComparison - INFO - 处理第 26 行...
2025-05-29 09:22:48,583 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,583 - KVComparison - INFO - 处理第 27 行...
2025-05-29 09:22:48,584 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,584 - KVComparison - INFO - 处理第 28 行...
2025-05-29 09:22:48,584 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,584 - KVComparison - INFO - 处理第 29 行...
2025-05-29 09:22:48,584 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,585 - KVComparison - INFO - 处理第 30 行...
2025-05-29 09:22:48,585 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,585 - KVComparison - INFO - 处理第 31 行...
2025-05-29 09:22:48,585 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,585 - KVComparison - INFO - 处理第 32 行...
2025-05-29 09:22:48,586 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,586 - KVComparison - INFO - 处理第 33 行...
2025-05-29 09:22:48,586 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,586 - KVComparison - INFO - 处理第 34 行...
2025-05-29 09:22:48,586 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,587 - KVComparison - INFO - 处理第 35 行...
2025-05-29 09:22:48,587 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,587 - KVComparison - INFO - 处理第 36 行...
2025-05-29 09:22:48,587 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,588 - KVComparison - INFO - 处理第 37 行...
2025-05-29 09:22:48,588 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,588 - KVComparison - INFO - 处理第 38 行...
2025-05-29 09:22:48,588 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,588 - KVComparison - INFO - 处理第 39 行...
2025-05-29 09:22:48,588 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,589 - KVComparison - INFO - 处理第 40 行...
2025-05-29 09:22:48,589 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,589 - KVComparison - INFO - 处理第 41 行...
2025-05-29 09:22:48,589 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,589 - KVComparison - INFO - 处理第 42 行...
2025-05-29 09:22:48,590 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,590 - KVComparison - INFO - 处理第 43 行...
2025-05-29 09:22:48,590 - KVComparison - INFO - 处理第 44 行...
2025-05-29 09:22:48,590 - KVComparison - INFO - 处理第 45 行...
2025-05-29 09:22:48,591 - KVComparison - INFO - 处理第 46 行...
2025-05-29 09:22:48,591 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,591 - KVComparison - INFO - 处理第 47 行...
2025-05-29 09:22:48,591 - KVComparison - INFO - 处理第 48 行...
2025-05-29 09:22:48,592 - KVComparison - INFO - 处理第 49 行...
2025-05-29 09:22:48,592 - KVComparison - INFO - 处理第 50 行...
2025-05-29 09:22:48,592 - KVComparison - INFO - 处理第 51 行...
2025-05-29 09:22:48,592 - KVComparison - INFO - 处理第 52 行...
2025-05-29 09:22:48,592 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,593 - KVComparison - INFO - 处理第 53 行...
2025-05-29 09:22:48,593 - KVComparison - INFO - 处理第 54 行...
2025-05-29 09:22:48,593 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,594 - KVComparison - INFO - 处理第 55 行...
2025-05-29 09:22:48,594 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,595 - KVComparison - INFO - 处理第 56 行...
2025-05-29 09:22:48,595 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,595 - KVComparison - INFO - 处理第 57 行...
2025-05-29 09:22:48,595 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,595 - KVComparison - INFO - 处理第 58 行...
2025-05-29 09:22:48,596 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,596 - KVComparison - INFO - 处理第 59 行...
2025-05-29 09:22:48,596 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,596 - KVComparison - INFO - 处理第 60 行...
2025-05-29 09:22:48,596 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,596 - KVComparison - INFO - 处理第 61 行...
2025-05-29 09:22:48,597 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,597 - KVComparison - INFO - 处理第 62 行...
2025-05-29 09:22:48,597 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,597 - KVComparison - INFO - 处理第 63 行...
2025-05-29 09:22:48,597 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,598 - KVComparison - INFO - 处理第 64 行...
2025-05-29 09:22:48,598 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,598 - KVComparison - INFO - 处理第 65 行...
2025-05-29 09:22:48,598 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,598 - KVComparison - INFO - 处理第 66 行...
2025-05-29 09:22:48,598 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,599 - KVComparison - INFO - 处理第 67 行...
2025-05-29 09:22:48,599 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,599 - KVComparison - INFO - 处理第 68 行...
2025-05-29 09:22:48,599 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,599 - KVComparison - INFO - 处理第 69 行...
2025-05-29 09:22:48,599 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,599 - KVComparison - INFO - 处理第 70 行...
2025-05-29 09:22:48,600 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,600 - KVComparison - INFO - 处理第 71 行...
2025-05-29 09:22:48,600 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,600 - KVComparison - INFO - 处理第 72 行...
2025-05-29 09:22:48,600 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,601 - KVComparison - INFO - 处理第 73 行...
2025-05-29 09:22:48,601 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,601 - KVComparison - INFO - 处理第 74 行...
2025-05-29 09:22:48,601 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,601 - KVComparison - INFO - 处理第 75 行...
2025-05-29 09:22:48,601 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,602 - KVComparison - INFO - 处理第 76 行...
2025-05-29 09:22:48,602 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,602 - KVComparison - INFO - 处理第 77 行...
2025-05-29 09:22:48,602 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,602 - KVComparison - INFO - 处理第 78 行...
2025-05-29 09:22:48,603 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,603 - KVComparison - INFO - 处理第 79 行...
2025-05-29 09:22:48,603 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,603 - KVComparison - INFO - 处理第 80 行...
2025-05-29 09:22:48,603 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,604 - KVComparison - INFO - 处理第 81 行...
2025-05-29 09:22:48,604 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,604 - KVComparison - INFO - 处理第 82 行...
2025-05-29 09:22:48,604 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,604 - KVComparison - INFO - 处理第 83 行...
2025-05-29 09:22:48,605 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,605 - KVComparison - INFO - 处理第 84 行...
2025-05-29 09:22:48,605 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,606 - KVComparison - INFO - 处理第 85 行...
2025-05-29 09:22:48,606 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,606 - KVComparison - INFO - 处理第 86 行...
2025-05-29 09:22:48,606 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,606 - KVComparison - INFO - 处理第 87 行...
2025-05-29 09:22:48,607 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,607 - KVComparison - INFO - 处理第 88 行...
2025-05-29 09:22:48,607 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,607 - KVComparison - INFO - 处理第 89 行...
2025-05-29 09:22:48,607 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,608 - KVComparison - INFO - 处理第 90 行...
2025-05-29 09:22:48,608 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,608 - KVComparison - INFO - 处理第 91 行...
2025-05-29 09:22:48,608 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,608 - KVComparison - INFO - 处理第 92 行...
2025-05-29 09:22:48,608 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,608 - KVComparison - INFO - 处理第 93 行...
2025-05-29 09:22:48,609 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,609 - KVComparison - INFO - 处理第 94 行...
2025-05-29 09:22:48,609 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,609 - KVComparison - INFO - 处理第 95 行...
2025-05-29 09:22:48,609 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,610 - KVComparison - INFO - 处理第 96 行...
2025-05-29 09:22:48,610 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,610 - KVComparison - INFO - 处理第 97 行...
2025-05-29 09:22:48,610 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,610 - KVComparison - INFO - 处理第 98 行...
2025-05-29 09:22:48,611 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,611 - KVComparison - INFO - 处理第 99 行...
2025-05-29 09:22:48,611 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,611 - KVComparison - INFO - 处理第 100 行...
2025-05-29 09:22:48,612 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,612 - KVComparison - INFO - 处理第 101 行...
2025-05-29 09:22:48,612 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,612 - KVComparison - INFO - 处理第 102 行...
2025-05-29 09:22:48,612 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,612 - KVComparison - INFO - 处理第 103 行...
2025-05-29 09:22:48,613 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,613 - KVComparison - INFO - 处理第 104 行...
2025-05-29 09:22:48,613 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,613 - KVComparison - INFO - 处理第 105 行...
2025-05-29 09:22:48,613 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,613 - KVComparison - INFO - 处理第 106 行...
2025-05-29 09:22:48,614 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,614 - KVComparison - INFO - 处理第 107 行...
2025-05-29 09:22:48,614 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,614 - KVComparison - INFO - 处理第 108 行...
2025-05-29 09:22:48,614 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,614 - KVComparison - INFO - 处理第 109 行...
2025-05-29 09:22:48,614 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,614 - KVComparison - INFO - 处理第 110 行...
2025-05-29 09:22:48,615 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,615 - KVComparison - INFO - 处理第 111 行...
2025-05-29 09:22:48,615 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,615 - KVComparison - INFO - 处理第 112 行...
2025-05-29 09:22:48,615 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,615 - KVComparison - INFO - 处理第 113 行...
2025-05-29 09:22:48,615 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,616 - KVComparison - INFO - 处理第 114 行...
2025-05-29 09:22:48,616 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,616 - KVComparison - INFO - 处理第 115 行...
2025-05-29 09:22:48,616 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,616 - KVComparison - INFO - 处理第 116 行...
2025-05-29 09:22:48,616 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,616 - KVComparison - INFO - 处理第 117 行...
2025-05-29 09:22:48,617 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,617 - KVComparison - INFO - 处理第 118 行...
2025-05-29 09:22:48,617 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,617 - KVComparison - INFO - 处理第 119 行...
2025-05-29 09:22:48,617 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,618 - KVComparison - INFO - 处理第 120 行...
2025-05-29 09:22:48,618 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,618 - KVComparison - INFO - 处理第 121 行...
2025-05-29 09:22:48,618 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,618 - KVComparison - INFO - 处理第 122 行...
2025-05-29 09:22:48,619 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,619 - KVComparison - INFO - 处理第 123 行...
2025-05-29 09:22:48,619 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,619 - KVComparison - INFO - 处理第 124 行...
2025-05-29 09:22:48,619 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,619 - KVComparison - INFO - 处理第 125 行...
2025-05-29 09:22:48,619 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,620 - KVComparison - INFO - 处理第 126 行...
2025-05-29 09:22:48,620 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,620 - KVComparison - INFO - 处理第 127 行...
2025-05-29 09:22:48,620 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,621 - KVComparison - INFO - 处理第 128 行...
2025-05-29 09:22:48,621 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,621 - KVComparison - INFO - 处理第 129 行...
2025-05-29 09:22:48,621 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,621 - KVComparison - INFO - 处理第 130 行...
2025-05-29 09:22:48,621 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,622 - KVComparison - INFO - 处理第 131 行...
2025-05-29 09:22:48,622 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,622 - KVComparison - INFO - 处理第 132 行...
2025-05-29 09:22:48,622 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,622 - KVComparison - INFO - 处理第 133 行...
2025-05-29 09:22:48,623 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,623 - KVComparison - INFO - 处理第 134 行...
2025-05-29 09:22:48,623 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,623 - KVComparison - INFO - 处理第 135 行...
2025-05-29 09:22:48,624 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,624 - KVComparison - INFO - 处理第 136 行...
2025-05-29 09:22:48,624 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,624 - KVComparison - INFO - 处理第 137 行...
2025-05-29 09:22:48,624 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,625 - KVComparison - INFO - 处理第 138 行...
2025-05-29 09:22:48,625 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,625 - KVComparison - INFO - 处理第 139 行...
2025-05-29 09:22:48,625 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,626 - KVComparison - INFO - 处理第 140 行...
2025-05-29 09:22:48,626 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,626 - KVComparison - INFO - 处理第 141 行...
2025-05-29 09:22:48,626 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,626 - KVComparison - INFO - 处理第 142 行...
2025-05-29 09:22:48,627 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,627 - KVComparison - INFO - 处理第 143 行...
2025-05-29 09:22:48,627 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,627 - KVComparison - INFO - 处理第 144 行...
2025-05-29 09:22:48,627 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,628 - KVComparison - INFO - 处理第 145 行...
2025-05-29 09:22:48,628 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,628 - KVComparison - INFO - 处理第 146 行...
2025-05-29 09:22:48,628 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,628 - KVComparison - INFO - 处理第 147 行...
2025-05-29 09:22:48,629 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,629 - KVComparison - INFO - 处理第 148 行...
2025-05-29 09:22:48,629 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,631 - KVComparison - INFO - 处理第 149 行...
2025-05-29 09:22:48,631 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,631 - KVComparison - INFO - 处理第 150 行...
2025-05-29 09:22:48,631 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,631 - KVComparison - INFO - 处理第 151 行...
2025-05-29 09:22:48,632 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,632 - KVComparison - INFO - 处理第 152 行...
2025-05-29 09:22:48,632 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,632 - KVComparison - INFO - 处理第 153 行...
2025-05-29 09:22:48,632 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,633 - KVComparison - INFO - 处理第 154 行...
2025-05-29 09:22:48,633 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,633 - KVComparison - INFO - 处理第 155 行...
2025-05-29 09:22:48,633 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,633 - KVComparison - INFO - 处理第 156 行...
2025-05-29 09:22:48,633 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,634 - KVComparison - INFO - 处理第 157 行...
2025-05-29 09:22:48,634 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,634 - KVComparison - INFO - 处理第 158 行...
2025-05-29 09:22:48,634 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,634 - KVComparison - INFO - 处理第 159 行...
2025-05-29 09:22:48,635 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,635 - KVComparison - INFO - 处理第 160 行...
2025-05-29 09:22:48,635 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,635 - KVComparison - INFO - 处理第 161 行...
2025-05-29 09:22:48,636 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,636 - KVComparison - INFO - 处理第 162 行...
2025-05-29 09:22:48,636 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,637 - KVComparison - INFO - 处理第 163 行...
2025-05-29 09:22:48,637 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,637 - KVComparison - INFO - 处理第 164 行...
2025-05-29 09:22:48,637 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,637 - KVComparison - INFO - 处理第 165 行...
2025-05-29 09:22:48,638 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,638 - KVComparison - INFO - 处理第 166 行...
2025-05-29 09:22:48,638 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,638 - KVComparison - INFO - 处理第 167 行...
2025-05-29 09:22:48,638 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,638 - KVComparison - INFO - 处理第 168 行...
2025-05-29 09:22:48,639 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,639 - KVComparison - INFO - 处理第 169 行...
2025-05-29 09:22:48,639 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,639 - KVComparison - INFO - 处理第 170 行...
2025-05-29 09:22:48,639 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,639 - KVComparison - INFO - 处理第 171 行...
2025-05-29 09:22:48,640 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,640 - KVComparison - INFO - 处理第 172 行...
2025-05-29 09:22:48,640 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,640 - KVComparison - INFO - 处理第 173 行...
2025-05-29 09:22:48,640 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,641 - KVComparison - INFO - 处理第 174 行...
2025-05-29 09:22:48,641 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,641 - KVComparison - INFO - 处理第 175 行...
2025-05-29 09:22:48,641 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,642 - KVComparison - INFO - 处理第 176 行...
2025-05-29 09:22:48,642 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,642 - KVComparison - INFO - 处理第 177 行...
2025-05-29 09:22:48,642 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,643 - KVComparison - INFO - 处理第 178 行...
2025-05-29 09:22:48,643 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,643 - KVComparison - INFO - 处理第 179 行...
2025-05-29 09:22:48,643 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,643 - KVComparison - INFO - 处理第 180 行...
2025-05-29 09:22:48,644 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,644 - KVComparison - INFO - 处理第 181 行...
2025-05-29 09:22:48,644 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,644 - KVComparison - INFO - 处理第 182 行...
2025-05-29 09:22:48,644 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,645 - KVComparison - INFO - 处理第 183 行...
2025-05-29 09:22:48,645 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,645 - KVComparison - INFO - 处理第 184 行...
2025-05-29 09:22:48,645 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,645 - KVComparison - INFO - 处理第 185 行...
2025-05-29 09:22:48,645 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,646 - KVComparison - INFO - 处理第 186 行...
2025-05-29 09:22:48,646 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,646 - KVComparison - INFO - 处理第 187 行...
2025-05-29 09:22:48,646 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,646 - KVComparison - INFO - 处理第 188 行...
2025-05-29 09:22:48,647 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,647 - KVComparison - INFO - 处理第 189 行...
2025-05-29 09:22:48,647 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,647 - KVComparison - INFO - 处理第 190 行...
2025-05-29 09:22:48,647 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,648 - KVComparison - INFO - 处理第 191 行...
2025-05-29 09:22:48,648 - KVComparison - INFO - 处理第 192 行...
2025-05-29 09:22:48,648 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,648 - KVComparison - INFO - 处理第 193 行...
2025-05-29 09:22:48,649 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,649 - KVComparison - INFO - 处理第 194 行...
2025-05-29 09:22:48,649 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,649 - KVComparison - INFO - 处理第 195 行...
2025-05-29 09:22:48,649 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,650 - KVComparison - INFO - 处理第 196 行...
2025-05-29 09:22:48,650 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,650 - KVComparison - INFO - 处理第 197 行...
2025-05-29 09:22:48,650 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,650 - KVComparison - INFO - 处理第 198 行...
2025-05-29 09:22:48,651 - KVComparison - INFO - 处理第 199 行...
2025-05-29 09:22:48,651 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,651 - KVComparison - INFO - 处理第 200 行...
2025-05-29 09:22:48,651 - KVComparison - INFO - 处理第 201 行...
2025-05-29 09:22:48,651 - KVComparison - INFO - 处理第 202 行...
2025-05-29 09:22:48,651 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,652 - KVComparison - INFO - 处理第 203 行...
2025-05-29 09:22:48,652 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,652 - KVComparison - INFO - 处理第 204 行...
2025-05-29 09:22:48,652 - KVComparison - INFO - 处理第 205 行...
2025-05-29 09:22:48,652 - KVComparison - INFO - 处理第 206 行...
2025-05-29 09:22:48,653 - KVComparison - WARNING - 无法从文本中提取JSON: 提取失败...
2025-05-29 09:22:48,653 - KVComparison - WARNING - 无法从文本中提取JSON: 提取失败...
2025-05-29 09:22:48,653 - KVComparison - INFO - 处理第 207 行...
2025-05-29 09:22:48,653 - KVComparison - INFO - 处理第 208 行...
2025-05-29 09:22:48,653 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,654 - KVComparison - INFO - 处理第 209 行...
2025-05-29 09:22:48,654 - KVComparison - INFO - 处理第 210 行...
2025-05-29 09:22:48,654 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,654 - KVComparison - INFO - 处理第 211 行...
2025-05-29 09:22:48,655 - KVComparison - INFO - 处理第 212 行...
2025-05-29 09:22:48,655 - KVComparison - INFO - 处理第 213 行...
2025-05-29 09:22:48,655 - KVComparison - INFO - 处理第 214 行...
2025-05-29 09:22:48,655 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,655 - KVComparison - INFO - 处理第 215 行...
2025-05-29 09:22:48,656 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,656 - KVComparison - INFO - 处理第 216 行...
2025-05-29 09:22:48,656 - KVComparison - INFO - 处理第 217 行...
2025-05-29 09:22:48,656 - KVComparison - INFO - 处理第 218 行...
2025-05-29 09:22:48,656 - KVComparison - INFO - 处理第 219 行...
2025-05-29 09:22:48,656 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,657 - KVComparison - INFO - 处理第 220 行...
2025-05-29 09:22:48,657 - KVComparison - INFO - 处理第 221 行...
2025-05-29 09:22:48,657 - KVComparison - INFO - 处理第 222 行...
2025-05-29 09:22:48,657 - KVComparison - INFO - 处理第 223 行...
2025-05-29 09:22:48,657 - KVComparison - INFO - 处理第 224 行...
2025-05-29 09:22:48,658 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,658 - KVComparison - INFO - 处理第 225 行...
2025-05-29 09:22:48,658 - KVComparison - INFO - 处理第 226 行...
2025-05-29 09:22:48,658 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,658 - KVComparison - INFO - 处理第 227 行...
2025-05-29 09:22:48,659 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,659 - KVComparison - INFO - 处理第 228 行...
2025-05-29 09:22:48,659 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,659 - KVComparison - INFO - 处理第 229 行...
2025-05-29 09:22:48,660 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,660 - KVComparison - INFO - 处理第 230 行...
2025-05-29 09:22:48,660 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,661 - KVComparison - INFO - 处理第 231 行...
2025-05-29 09:22:48,661 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,661 - KVComparison - INFO - 处理第 232 行...
2025-05-29 09:22:48,661 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,662 - KVComparison - INFO - 处理第 233 行...
2025-05-29 09:22:48,662 - KVComparison - INFO - 处理第 234 行...
2025-05-29 09:22:48,662 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,662 - KVComparison - INFO - 处理第 235 行...
2025-05-29 09:22:48,663 - KVComparison - INFO - 处理第 236 行...
2025-05-29 09:22:48,663 - KVComparison - INFO - 处理第 237 行...
2025-05-29 09:22:48,663 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,663 - KVComparison - INFO - 处理第 238 行...
2025-05-29 09:22:48,663 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,664 - KVComparison - INFO - 处理第 239 行...
2025-05-29 09:22:48,664 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,664 - KVComparison - INFO - 处理第 240 行...
2025-05-29 09:22:48,664 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,664 - KVComparison - INFO - 处理第 241 行...
2025-05-29 09:22:48,664 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,665 - KVComparison - INFO - 处理第 242 行...
2025-05-29 09:22:48,665 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,665 - KVComparison - INFO - 处理第 243 行...
2025-05-29 09:22:48,666 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,666 - KVComparison - INFO - 处理第 244 行...
2025-05-29 09:22:48,666 - KVComparison - INFO - 处理第 245 行...
2025-05-29 09:22:48,667 - KVComparison - INFO - 处理第 246 行...
2025-05-29 09:22:48,667 - KVComparison - INFO - 处理第 247 行...
2025-05-29 09:22:48,667 - KVComparison - INFO - 处理第 248 行...
2025-05-29 09:22:48,667 - KVComparison - INFO - 处理第 249 行...
2025-05-29 09:22:48,668 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,668 - KVComparison - INFO - 处理第 250 行...
2025-05-29 09:22:48,668 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,668 - KVComparison - INFO - 处理第 251 行...
2025-05-29 09:22:48,668 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,669 - KVComparison - INFO - 处理第 252 行...
2025-05-29 09:22:48,669 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,669 - KVComparison - INFO - 处理第 253 行...
2025-05-29 09:22:48,670 - KVComparison - INFO - 处理第 254 行...
2025-05-29 09:22:48,670 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,670 - KVComparison - INFO - 处理第 255 行...
2025-05-29 09:22:48,670 - KVComparison - INFO - 处理第 256 行...
2025-05-29 09:22:48,671 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,671 - KVComparison - INFO - 处理第 257 行...
2025-05-29 09:22:48,671 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,671 - KVComparison - INFO - 处理第 258 行...
2025-05-29 09:22:48,671 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,672 - KVComparison - INFO - 处理第 259 行...
2025-05-29 09:22:48,672 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,672 - KVComparison - INFO - 处理第 260 行...
2025-05-29 09:22:48,672 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,673 - KVComparison - INFO - 处理第 261 行...
2025-05-29 09:22:48,673 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,673 - KVComparison - INFO - 处理第 262 行...
2025-05-29 09:22:48,673 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,674 - KVComparison - INFO - 处理第 263 行...
2025-05-29 09:22:48,674 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,674 - KVComparison - INFO - 处理第 264 行...
2025-05-29 09:22:48,674 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,674 - KVComparison - INFO - 处理第 265 行...
2025-05-29 09:22:48,675 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,675 - KVComparison - INFO - 处理第 266 行...
2025-05-29 09:22:48,675 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,675 - KVComparison - INFO - 处理第 267 行...
2025-05-29 09:22:48,675 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,676 - KVComparison - INFO - 处理第 268 行...
2025-05-29 09:22:48,676 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,676 - KVComparison - INFO - 处理第 269 行...
2025-05-29 09:22:48,676 - KVComparison - WARNING - 无法从文本中提取JSON: 提取失败...
2025-05-29 09:22:48,676 - KVComparison - WARNING - 无法从文本中提取JSON: 提取失败...
2025-05-29 09:22:48,677 - KVComparison - INFO - 处理第 270 行...
2025-05-29 09:22:48,677 - KVComparison - INFO - 处理第 271 行...
2025-05-29 09:22:48,677 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,677 - KVComparison - INFO - 处理第 272 行...
2025-05-29 09:22:48,677 - KVComparison - INFO - 处理第 273 行...
2025-05-29 09:22:48,678 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,678 - KVComparison - INFO - 处理第 274 行...
2025-05-29 09:22:48,678 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,678 - KVComparison - INFO - 处理第 275 行...
2025-05-29 09:22:48,679 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,679 - KVComparison - INFO - 处理第 276 行...
2025-05-29 09:22:48,679 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,679 - KVComparison - INFO - 处理第 277 行...
2025-05-29 09:22:48,680 - KVComparison - INFO - 处理第 278 行...
2025-05-29 09:22:48,680 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,680 - KVComparison - INFO - 处理第 279 行...
2025-05-29 09:22:48,680 - KVComparison - INFO - 处理第 280 行...
2025-05-29 09:22:48,681 - KVComparison - INFO - 处理第 281 行...
2025-05-29 09:22:48,681 - KVComparison - INFO - 处理第 282 行...
2025-05-29 09:22:48,681 - KVComparison - INFO - 处理第 283 行...
2025-05-29 09:22:48,681 - KVComparison - INFO - 处理第 284 行...
2025-05-29 09:22:48,681 - KVComparison - INFO - 处理第 285 行...
2025-05-29 09:22:48,681 - KVComparison - INFO - 处理第 286 行...
2025-05-29 09:22:48,682 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,682 - KVComparison - INFO - 处理第 287 行...
2025-05-29 09:22:48,682 - KVComparison - INFO - 处理第 288 行...
2025-05-29 09:22:48,682 - KVComparison - INFO - 处理第 289 行...
2025-05-29 09:22:48,683 - KVComparison - INFO - 处理第 290 行...
2025-05-29 09:22:48,683 - KVComparison - INFO - 处理第 291 行...
2025-05-29 09:22:48,683 - KVComparison - INFO - 处理第 292 行...
2025-05-29 09:22:48,683 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,683 - KVComparison - INFO - 处理第 293 行...
2025-05-29 09:22:48,684 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,684 - KVComparison - INFO - 处理第 294 行...
2025-05-29 09:22:48,684 - KVComparison - INFO - 处理第 295 行...
2025-05-29 09:22:48,684 - KVComparison - INFO - 处理第 296 行...
2025-05-29 09:22:48,685 - KVComparison - INFO - 处理第 297 行...
2025-05-29 09:22:48,685 - KVComparison - INFO - 处理第 298 行...
2025-05-29 09:22:48,685 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,685 - KVComparison - INFO - 处理第 299 行...
2025-05-29 09:22:48,686 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,686 - KVComparison - INFO - 处理第 300 行...
2025-05-29 09:22:48,686 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,686 - KVComparison - INFO - 处理第 301 行...
2025-05-29 09:22:48,686 - KVComparison - INFO - 处理第 302 行...
2025-05-29 09:22:48,687 - KVComparison - INFO - 处理第 303 行...
2025-05-29 09:22:48,687 - KVComparison - INFO - 处理第 304 行...
2025-05-29 09:22:48,687 - KVComparison - INFO - 处理第 305 行...
2025-05-29 09:22:48,687 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,688 - KVComparison - INFO - 处理第 306 行...
2025-05-29 09:22:48,688 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,688 - KVComparison - INFO - 处理第 307 行...
2025-05-29 09:22:48,688 - KVComparison - INFO - 处理第 308 行...
2025-05-29 09:22:48,688 - KVComparison - INFO - 处理第 309 行...
2025-05-29 09:22:48,688 - KVComparison - INFO - 处理第 310 行...
2025-05-29 09:22:48,689 - KVComparison - INFO - 处理第 311 行...
2025-05-29 09:22:48,689 - KVComparison - INFO - 处理第 312 行...
2025-05-29 09:22:48,689 - KVComparison - INFO - 处理第 313 行...
2025-05-29 09:22:48,689 - KVComparison - INFO - 处理第 314 行...
2025-05-29 09:22:48,689 - KVComparison - INFO - 处理第 315 行...
2025-05-29 09:22:48,690 - KVComparison - INFO - 处理第 316 行...
2025-05-29 09:22:48,690 - KVComparison - INFO - 处理第 317 行...
2025-05-29 09:22:48,690 - KVComparison - INFO - 处理第 318 行...
2025-05-29 09:22:48,691 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,691 - KVComparison - INFO - 处理第 319 行...
2025-05-29 09:22:48,691 - KVComparison - INFO - 处理第 320 行...
2025-05-29 09:22:48,691 - KVComparison - INFO - 处理第 321 行...
2025-05-29 09:22:48,691 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,692 - KVComparison - INFO - 处理第 322 行...
2025-05-29 09:22:48,692 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,692 - KVComparison - INFO - 处理第 323 行...
2025-05-29 09:22:48,692 - KVComparison - INFO - 处理第 324 行...
2025-05-29 09:22:48,693 - KVComparison - INFO - 处理第 325 行...
2025-05-29 09:22:48,693 - KVComparison - INFO - 处理第 326 行...
2025-05-29 09:22:48,693 - KVComparison - INFO - 处理第 327 行...
2025-05-29 09:22:48,693 - KVComparison - INFO - 处理第 328 行...
2025-05-29 09:22:48,693 - KVComparison - INFO - 处理第 329 行...
2025-05-29 09:22:48,694 - KVComparison - INFO - 处理第 330 行...
2025-05-29 09:22:48,694 - KVComparison - INFO - 处理第 331 行...
2025-05-29 09:22:48,694 - KVComparison - INFO - 处理第 332 行...
2025-05-29 09:22:48,694 - KVComparison - INFO - 处理第 333 行...
2025-05-29 09:22:48,694 - KVComparison - INFO - 处理第 334 行...
2025-05-29 09:22:48,695 - KVComparison - INFO - 处理第 335 行...
2025-05-29 09:22:48,695 - KVComparison - INFO - 处理第 336 行...
2025-05-29 09:22:48,695 - KVComparison - INFO - 处理第 337 行...
2025-05-29 09:22:48,695 - KVComparison - INFO - 处理第 338 行...
2025-05-29 09:22:48,696 - KVComparison - INFO - 处理第 339 行...
2025-05-29 09:22:48,696 - KVComparison - INFO - 处理第 340 行...
2025-05-29 09:22:48,696 - KVComparison - INFO - 处理第 341 行...
2025-05-29 09:22:48,696 - KVComparison - INFO - 处理第 342 行...
2025-05-29 09:22:48,697 - KVComparison - INFO - 处理第 343 行...
2025-05-29 09:22:48,697 - KVComparison - INFO - 处理第 344 行...
2025-05-29 09:22:48,697 - KVComparison - INFO - 处理第 345 行...
2025-05-29 09:22:48,697 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,698 - KVComparison - INFO - 处理第 346 行...
2025-05-29 09:22:48,698 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,698 - KVComparison - INFO - 处理第 347 行...
2025-05-29 09:22:48,698 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,698 - KVComparison - INFO - 处理第 348 行...
2025-05-29 09:22:48,698 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,699 - KVComparison - INFO - 处理第 349 行...
2025-05-29 09:22:48,699 - KVComparison - INFO - 处理第 350 行...
2025-05-29 09:22:48,699 - KVComparison - INFO - 处理第 351 行...
2025-05-29 09:22:48,699 - KVComparison - INFO - 处理第 352 行...
2025-05-29 09:22:48,700 - KVComparison - INFO - 处理第 353 行...
2025-05-29 09:22:48,700 - KVComparison - INFO - 处理第 354 行...
2025-05-29 09:22:48,700 - KVComparison - INFO - 处理第 355 行...
2025-05-29 09:22:48,700 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,701 - KVComparison - INFO - 处理第 356 行...
2025-05-29 09:22:48,701 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,701 - KVComparison - INFO - 处理第 357 行...
2025-05-29 09:22:48,701 - KVComparison - INFO - 处理第 358 行...
2025-05-29 09:22:48,701 - KVComparison - INFO - 处理第 359 行...
2025-05-29 09:22:48,702 - KVComparison - INFO - 处理第 360 行...
2025-05-29 09:22:48,702 - KVComparison - INFO - 处理第 361 行...
2025-05-29 09:22:48,702 - KVComparison - INFO - 处理第 362 行...
2025-05-29 09:22:48,702 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,703 - KVComparison - INFO - 处理第 363 行...
2025-05-29 09:22:48,703 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,703 - KVComparison - INFO - 处理第 364 行...
2025-05-29 09:22:48,703 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,703 - KVComparison - INFO - 处理第 365 行...
2025-05-29 09:22:48,704 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,704 - KVComparison - INFO - 处理第 366 行...
2025-05-29 09:22:48,704 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,704 - KVComparison - INFO - 处理第 367 行...
2025-05-29 09:22:48,704 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,705 - KVComparison - INFO - 处理第 368 行...
2025-05-29 09:22:48,705 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,705 - KVComparison - INFO - 处理第 369 行...
2025-05-29 09:22:48,705 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,705 - KVComparison - INFO - 处理第 370 行...
2025-05-29 09:22:48,706 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,706 - KVComparison - INFO - 处理第 371 行...
2025-05-29 09:22:48,706 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,706 - KVComparison - INFO - 处理第 372 行...
2025-05-29 09:22:48,706 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,706 - KVComparison - INFO - 处理第 373 行...
2025-05-29 09:22:48,707 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,707 - KVComparison - INFO - 处理第 374 行...
2025-05-29 09:22:48,707 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,707 - KVComparison - INFO - 处理第 375 行...
2025-05-29 09:22:48,707 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,708 - KVComparison - INFO - 处理第 376 行...
2025-05-29 09:22:48,708 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,708 - KVComparison - INFO - 处理第 377 行...
2025-05-29 09:22:48,708 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,708 - KVComparison - INFO - 处理第 378 行...
2025-05-29 09:22:48,709 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,709 - KVComparison - INFO - 处理第 379 行...
2025-05-29 09:22:48,709 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,709 - KVComparison - INFO - 处理第 380 行...
2025-05-29 09:22:48,709 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,710 - KVComparison - INFO - 处理第 381 行...
2025-05-29 09:22:48,710 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,710 - KVComparison - INFO - 处理第 382 行...
2025-05-29 09:22:48,710 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,711 - KVComparison - INFO - 处理第 383 行...
2025-05-29 09:22:48,711 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,711 - KVComparison - INFO - 处理第 384 行...
2025-05-29 09:22:48,711 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,711 - KVComparison - INFO - 处理第 385 行...
2025-05-29 09:22:48,712 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,712 - KVComparison - INFO - 处理第 386 行...
2025-05-29 09:22:48,712 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,712 - KVComparison - INFO - 处理第 387 行...
2025-05-29 09:22:48,712 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,712 - KVComparison - INFO - 处理第 388 行...
2025-05-29 09:22:48,713 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,713 - KVComparison - INFO - 处理第 389 行...
2025-05-29 09:22:48,713 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,713 - KVComparison - INFO - 处理第 390 行...
2025-05-29 09:22:48,713 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,714 - KVComparison - INFO - 处理第 391 行...
2025-05-29 09:22:48,714 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,714 - KVComparison - INFO - 处理第 392 行...
2025-05-29 09:22:48,715 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,715 - KVComparison - INFO - 处理第 393 行...
2025-05-29 09:22:48,715 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,715 - KVComparison - INFO - 处理第 394 行...
2025-05-29 09:22:48,715 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,716 - KVComparison - INFO - 处理第 395 行...
2025-05-29 09:22:48,716 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,716 - KVComparison - INFO - 处理第 396 行...
2025-05-29 09:22:48,716 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,716 - KVComparison - INFO - 处理第 397 行...
2025-05-29 09:22:48,716 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,717 - KVComparison - INFO - 处理第 398 行...
2025-05-29 09:22:48,717 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,717 - KVComparison - INFO - 处理第 399 行...
2025-05-29 09:22:48,717 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,717 - KVComparison - INFO - 处理第 400 行...
2025-05-29 09:22:48,717 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:22:48,717 - KVComparison - INFO - 处理第 401 行...
2025-05-29 09:22:48,718 - KVComparison - INFO - 处理第 402 行...
2025-05-29 09:22:48,718 - KVComparison - INFO - 处理第 403 行...
2025-05-29 09:22:48,718 - KVComparison - INFO - 处理第 404 行...
2025-05-29 09:22:48,718 - KVComparison - INFO - 处理第 405 行...
2025-05-29 09:22:48,718 - KVComparison - INFO - 处理第 406 行...
2025-05-29 09:22:48,719 - KVComparison - INFO - 处理第 407 行...
2025-05-29 09:22:48,719 - KVComparison - INFO - 处理第 408 行...
2025-05-29 09:22:48,719 - KVComparison - INFO - 处理第 409 行...
2025-05-29 09:22:48,719 - KVComparison - INFO - 处理第 410 行...
2025-05-29 09:22:48,720 - KVComparison - INFO - 处理第 411 行...
2025-05-29 09:22:48,720 - KVComparison - INFO - 处理第 412 行...
2025-05-29 09:22:48,720 - KVComparison - INFO - 处理第 413 行...
2025-05-29 09:22:48,721 - KVComparison - INFO - 处理第 414 行...
2025-05-29 09:22:48,721 - KVComparison - INFO - 处理第 415 行...
2025-05-29 09:22:48,721 - KVComparison - INFO - 处理第 416 行...
2025-05-29 09:22:48,721 - KVComparison - INFO - 处理第 417 行...
2025-05-29 09:22:48,721 - KVComparison - INFO - 处理第 418 行...
2025-05-29 09:22:48,722 - KVComparison - INFO - 处理第 419 行...
2025-05-29 09:22:48,722 - KVComparison - INFO - 处理第 420 行...
2025-05-29 09:22:48,722 - KVComparison - INFO - 处理第 421 行...
2025-05-29 09:22:48,722 - KVComparison - INFO - 处理第 422 行...
2025-05-29 09:22:48,722 - KVComparison - INFO - 处理第 423 行...
2025-05-29 09:22:48,722 - KVComparison - INFO - 处理第 424 行...
2025-05-29 09:22:48,723 - KVComparison - INFO - 处理第 425 行...
2025-05-29 09:22:48,723 - KVComparison - INFO - 处理第 426 行...
2025-05-29 09:22:48,723 - KVComparison - INFO - 处理第 427 行...
2025-05-29 09:22:48,723 - KVComparison - INFO - 处理第 428 行...
2025-05-29 09:22:48,723 - KVComparison - INFO - 处理第 429 行...
2025-05-29 09:22:48,723 - KVComparison - INFO - 处理第 430 行...
2025-05-29 09:22:48,724 - KVComparison - INFO - 处理第 431 行...
2025-05-29 09:22:48,724 - KVComparison - INFO - 处理第 432 行...
2025-05-29 09:22:48,724 - KVComparison - INFO - 处理第 433 行...
2025-05-29 09:22:48,724 - KVComparison - INFO - 处理第 434 行...
2025-05-29 09:22:48,725 - KVComparison - INFO - 处理第 435 行...
2025-05-29 09:22:48,725 - KVComparison - INFO - 处理第 436 行...
2025-05-29 09:22:48,725 - KVComparison - INFO - 处理第 437 行...
2025-05-29 09:22:48,725 - KVComparison - INFO - 处理第 438 行...
2025-05-29 09:22:48,725 - KVComparison - INFO - 处理第 439 行...
2025-05-29 09:22:48,725 - KVComparison - INFO - 处理第 440 行...
2025-05-29 09:22:48,726 - KVComparison - INFO - 处理第 441 行...
2025-05-29 09:22:48,726 - KVComparison - INFO - 处理第 442 行...
2025-05-29 09:22:48,726 - KVComparison - INFO - 处理第 443 行...
2025-05-29 09:22:48,727 - KVComparison - INFO - 处理第 444 行...
2025-05-29 09:22:48,727 - KVComparison - INFO - 处理第 445 行...
2025-05-29 09:22:48,727 - KVComparison - INFO - 处理第 446 行...
2025-05-29 09:22:48,727 - KVComparison - INFO - 处理第 447 行...
2025-05-29 09:22:48,727 - KVComparison - INFO - 处理第 448 行...
2025-05-29 09:22:48,728 - KVComparison - INFO - 处理第 449 行...
2025-05-29 09:22:48,728 - KVComparison - INFO - 处理第 450 行...
2025-05-29 09:22:48,728 - KVComparison - INFO - 处理第 451 行...
2025-05-29 09:22:48,728 - KVComparison - INFO - 处理第 452 行...
2025-05-29 09:22:48,728 - KVComparison - INFO - 处理第 453 行...
2025-05-29 09:22:48,729 - KVComparison - INFO - 处理第 454 行...
2025-05-29 09:22:48,729 - KVComparison - INFO - 处理第 455 行...
2025-05-29 09:22:48,729 - KVComparison - INFO - 处理第 456 行...
2025-05-29 09:22:48,729 - KVComparison - INFO - 处理第 457 行...
2025-05-29 09:22:48,730 - KVComparison - INFO - 处理第 458 行...
2025-05-29 09:22:48,730 - KVComparison - INFO - 处理第 459 行...
2025-05-29 09:22:48,731 - KVComparison - INFO - 处理第 460 行...
2025-05-29 09:22:48,731 - KVComparison - INFO - 处理第 461 行...
2025-05-29 09:22:48,731 - KVComparison - INFO - 处理第 462 行...
2025-05-29 09:22:48,731 - KVComparison - INFO - 处理第 463 行...
2025-05-29 09:22:48,731 - KVComparison - INFO - 处理第 464 行...
2025-05-29 09:22:48,732 - KVComparison - INFO - 处理第 465 行...
2025-05-29 09:22:48,732 - KVComparison - INFO - 处理第 466 行...
2025-05-29 09:22:48,732 - KVComparison - INFO - 处理第 467 行...
2025-05-29 09:22:48,732 - KVComparison - INFO - 处理第 468 行...
2025-05-29 09:22:48,733 - KVComparison - INFO - 处理第 469 行...
2025-05-29 09:22:48,733 - KVComparison - INFO - 处理第 470 行...
2025-05-29 09:22:48,733 - KVComparison - INFO - 处理第 471 行...
2025-05-29 09:22:48,733 - KVComparison - INFO - 处理第 472 行...
2025-05-29 09:22:48,733 - KVComparison - INFO - 处理第 473 行...
2025-05-29 09:22:48,734 - KVComparison - INFO - 处理第 474 行...
2025-05-29 09:22:48,734 - KVComparison - INFO - 处理第 475 行...
2025-05-29 09:22:48,734 - KVComparison - INFO - 处理第 476 行...
2025-05-29 09:22:48,734 - KVComparison - INFO - 处理第 477 行...
2025-05-29 09:22:48,734 - KVComparison - INFO - 处理第 478 行...
2025-05-29 09:22:48,735 - KVComparison - INFO - 处理第 479 行...
2025-05-29 09:22:48,735 - KVComparison - INFO - 处理第 480 行...
2025-05-29 09:22:48,735 - KVComparison - INFO - 处理第 481 行...
2025-05-29 09:22:48,735 - KVComparison - INFO - 处理第 482 行...
2025-05-29 09:22:48,735 - KVComparison - INFO - 处理第 483 行...
2025-05-29 09:22:48,736 - KVComparison - INFO - 处理第 484 行...
2025-05-29 09:22:48,736 - KVComparison - INFO - 处理第 485 行...
2025-05-29 09:22:48,736 - KVComparison - INFO - 处理第 486 行...
2025-05-29 09:22:48,736 - KVComparison - INFO - 处理第 487 行...
2025-05-29 09:22:48,736 - KVComparison - INFO - 处理第 488 行...
2025-05-29 09:22:48,736 - KVComparison - INFO - 处理第 489 行...
2025-05-29 09:22:48,737 - KVComparison - INFO - 处理第 490 行...
2025-05-29 09:22:48,737 - KVComparison - INFO - 处理第 491 行...
2025-05-29 09:22:48,737 - KVComparison - INFO - 处理第 492 行...
2025-05-29 09:22:48,737 - KVComparison - INFO - 处理第 493 行...
2025-05-29 09:22:48,738 - KVComparison - INFO - 处理第 494 行...
2025-05-29 09:22:48,738 - KVComparison - INFO - 处理第 495 行...
2025-05-29 09:22:48,738 - KVComparison - INFO - 处理第 496 行...
2025-05-29 09:22:48,738 - KVComparison - INFO - 处理第 497 行...
2025-05-29 09:22:48,738 - KVComparison - INFO - 处理第 498 行...
2025-05-29 09:22:48,739 - KVComparison - INFO - 处理第 499 行...
2025-05-29 09:22:48,739 - KVComparison - INFO - 处理第 500 行...
2025-05-29 09:22:48,739 - KVComparison - INFO - 处理第 501 行...
2025-05-29 09:22:48,739 - KVComparison - INFO - 处理第 502 行...
2025-05-29 09:22:48,739 - KVComparison - INFO - 处理第 503 行...
2025-05-29 09:22:48,739 - KVComparison - INFO - 处理第 504 行...
2025-05-29 09:22:48,739 - KVComparison - INFO - 处理第 505 行...
2025-05-29 09:22:48,739 - KVComparison - INFO - 处理第 506 行...
2025-05-29 09:22:48,741 - KVComparison - INFO - 处理第 507 行...
2025-05-29 09:22:48,741 - KVComparison - INFO - 处理第 508 行...
2025-05-29 09:22:48,741 - KVComparison - INFO - 处理第 509 行...
2025-05-29 09:22:48,741 - KVComparison - INFO - 处理第 510 行...
2025-05-29 09:22:48,741 - KVComparison - INFO - 处理第 511 行...
2025-05-29 09:22:48,741 - KVComparison - INFO - 处理第 512 行...
2025-05-29 09:22:48,741 - KVComparison - INFO - 处理第 513 行...
2025-05-29 09:22:48,741 - KVComparison - INFO - 处理第 514 行...
2025-05-29 09:22:48,742 - KVComparison - INFO - 处理第 515 行...
2025-05-29 09:22:48,742 - KVComparison - INFO - 处理第 516 行...
2025-05-29 09:22:48,742 - KVComparison - INFO - 处理第 517 行...
2025-05-29 09:22:48,742 - KVComparison - INFO - 处理第 518 行...
2025-05-29 09:22:48,742 - KVComparison - INFO - 处理第 519 行...
2025-05-29 09:22:48,742 - KVComparison - INFO - 处理第 520 行...
2025-05-29 09:22:48,742 - KVComparison - INFO - 处理第 521 行...
2025-05-29 09:22:48,742 - KVComparison - INFO - 处理第 522 行...
2025-05-29 09:22:48,743 - KVComparison - INFO - 处理第 523 行...
2025-05-29 09:22:48,743 - KVComparison - INFO - 处理第 524 行...
2025-05-29 09:22:48,743 - KVComparison - INFO - 处理第 525 行...
2025-05-29 09:22:48,743 - KVComparison - INFO - 处理第 526 行...
2025-05-29 09:22:48,743 - KVComparison - INFO - 处理第 527 行...
2025-05-29 09:22:48,743 - KVComparison - INFO - 处理第 528 行...
2025-05-29 09:22:48,744 - KVComparison - INFO - 处理第 529 行...
2025-05-29 09:22:48,744 - KVComparison - INFO - 处理第 530 行...
2025-05-29 09:22:48,744 - KVComparison - INFO - 处理第 531 行...
2025-05-29 09:22:48,744 - KVComparison - INFO - 处理第 532 行...
2025-05-29 09:22:48,744 - KVComparison - INFO - 处理第 533 行...
2025-05-29 09:22:48,745 - KVComparison - INFO - 处理第 534 行...
2025-05-29 09:22:48,745 - KVComparison - INFO - 处理第 535 行...
2025-05-29 09:22:48,745 - KVComparison - INFO - 处理第 536 行...
2025-05-29 09:22:48,745 - KVComparison - INFO - 处理第 537 行...
2025-05-29 09:22:48,746 - KVComparison - INFO - 处理第 538 行...
2025-05-29 09:22:48,746 - KVComparison - INFO - 处理第 539 行...
2025-05-29 09:22:48,746 - KVComparison - INFO - 处理第 540 行...
2025-05-29 09:22:48,746 - KVComparison - INFO - 处理第 541 行...
2025-05-29 09:22:48,746 - KVComparison - INFO - 处理第 542 行...
2025-05-29 09:22:48,747 - KVComparison - INFO - 处理第 543 行...
2025-05-29 09:22:48,747 - KVComparison - INFO - 处理第 544 行...
2025-05-29 09:22:48,747 - KVComparison - INFO - 处理第 545 行...
2025-05-29 09:22:48,747 - KVComparison - INFO - 比较完成: 一致 545 行, 不一致 0 行
2025-05-29 09:22:48,844 - KVComparison - INFO - 比较结果已保存到: KV比较结果_20250529_092248.xlsx
2025-05-29 09:22:48,861 - KVComparison - INFO - 详细结果已保存到: KV比较详细结果_20250529_092248.json
2025-05-29 09:24:45,427 - KVComparison - INFO - 正在加载Excel文件: test_skip_date.xlsx
2025-05-29 09:24:45,433 - KVComparison - INFO - 成功加载Excel文件，共 4 行数据
2025-05-29 09:24:45,434 - KVComparison - INFO - 列名: ['用户问题', '实际输出', '预期输出']
2025-05-29 09:24:45,434 - KVComparison - INFO - 开始比较所有行的数据...
2025-05-29 09:24:45,434 - KVComparison - INFO - 处理第 1 行...
2025-05-29 09:24:45,434 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:24:45,435 - KVComparison - INFO - 处理第 2 行...
2025-05-29 09:24:45,435 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:24:45,435 - KVComparison - INFO - 处理第 3 行...
2025-05-29 09:24:45,435 - KVComparison - INFO - 处理第 4 行...
2025-05-29 09:24:45,435 - KVComparison - INFO - 跳过数据日期键的比较: 实际输出=['数据日期'], 预期输出=['数据日期']
2025-05-29 09:24:45,436 - KVComparison - WARNING - 第 4 行不一致: 键'产品简称'值不同: 实际=['天天成长A'], 预期=['天天成长B']; 已跳过数据日期键的比较
2025-05-29 09:24:45,436 - KVComparison - INFO - 比较完成: 一致 3 行, 不一致 1 行
