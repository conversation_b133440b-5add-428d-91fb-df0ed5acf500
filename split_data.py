"""
将问题清单中的问题和模板拆分成两个独立的JSON文件
用于平安理财ChatBI问答系统
"""

import json
import os
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chatbi_data.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ChatBI-Data")

def load_json_data(json_file="AI验证问题清单.json"):
    """
    加载JSON数据

    参数:
        json_file (str): JSON文件路径

    返回:
        dict: 包含问题和模板的字典
    """
    try:
        logger.info(f"正在加载JSON文件: {json_file}")
        with open(json_file, "r", encoding="utf-8") as f:
            data = json.load(f)
        logger.info(f"成功加载JSON文件")
        return data
    except Exception as e:
        logger.error(f"加载JSON文件失败: {str(e)}")
        return None

def save_json_data(data, output_file):
    """
    保存JSON数据

    参数:
        data: 要保存的数据
        output_file (str): 输出文件路径
    """
    try:
        logger.info(f"正在保存JSON文件: {output_file}")
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"成功保存JSON文件: {output_file}")
    except Exception as e:
        logger.error(f"保存JSON文件失败: {str(e)}")

def split_data(data):
    """
    拆分问题和模板数据

    参数:
        data (dict): 包含问题和模板的字典

    返回:
        tuple: (问题数据, 模板数据)
    """
    try:
        questions = data.get("问题", [])
        templates_raw = data.get("模板", [])

        # 转换模板数据结构，使其更易于使用
        templates_dict = {}

        # 检查模板数据结构
        logger.info(f"原始模板数据类型: {type(templates_raw)}, 长度: {len(templates_raw)}")

        if isinstance(templates_raw, list):
            # 合并所有模板字典
            for template_item in templates_raw:
                if isinstance(template_item, dict):
                    for key, value in template_item.items():
                        templates_dict[key] = value

        # 如果没有提取到模板，尝试从问题数据中提取
        if not templates_dict:
            logger.warning("从模板列表中未提取到模板，尝试从问题数据中提取")
            template_set = set()
            for question in questions:
                if "预期key+value" in question and question["预期key+value"]:
                    # 尝试构建一个简单的模板
                    template = f"{{#数据日期#}}{{#产品简称#}}的{{#指标名#}}是多少?"
                    template_set.add(template)

            # 将提取的模板添加到字典中
            for i, template in enumerate(template_set):
                templates_dict[template] = template

        logger.info(f"成功拆分数据: {len(questions)}个问题, {len(templates_dict)}个模板")
        return questions, templates_dict
    except Exception as e:
        logger.error(f"拆分数据失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return [], {}

def main():
    # 加载JSON数据
    data = load_json_data()
    if not data:
        logger.error("无法加载数据，程序退出")
        return

    # 拆分问题和模板
    questions, templates = split_data(data)

    # 保存问题数据
    save_json_data(questions, "questions.json")

    # 保存模板数据
    save_json_data(templates, "templates.json")

    logger.info("数据拆分完成")

if __name__ == "__main__":
    main()
