"""
将Excel文件转换为JSON格式
用于平安理财ChatBI问答系统
"""

import pandas as pd
import json
import os
import sys

def convert_excel_to_json(excel_file, json_file=None):
    """
    将Excel文件转换为JSON格式

    参数:
        excel_file (str): Excel文件路径
        json_file (str): 输出JSON文件路径，如果为None则使用Excel文件名

    返回:
        str: 生成的JSON文件路径
    """
    # 检查文件是否存在
    if not os.path.exists(excel_file):
        print(f"错误: 文件 {excel_file} 不存在")
        return None

    # 如果未指定JSON文件名，则使用Excel文件名
    if json_file is None:
        json_file = os.path.splitext(excel_file)[0] + ".json"

    try:
        # 读取Excel文件中的所有sheet
        print(f"正在读取Excel文件: {excel_file}")
        # 尝试不同的编码方式
        try:
            excel_data = pd.read_excel(excel_file, sheet_name=None)
        except UnicodeDecodeError:
            # 如果默认编码失败，尝试使用GBK编码
            excel_data = pd.read_excel(excel_file, sheet_name=None, encoding='gbk')

        # 创建结果字典
        result = {}

        # 处理每个sheet
        for sheet_name, df in excel_data.items():
            print(f"处理sheet: {sheet_name}")

            # 确保列名是字符串类型
            df.columns = df.columns.astype(str)

            # 将DataFrame转换为字典列表，确保处理NaN值
            sheet_data = df.fillna("").to_dict(orient="records")
            result[sheet_name] = sheet_data

        # 将结果转换为JSON，确保不使用ASCII编码
        json_data = json.dumps(result, ensure_ascii=False, indent=2)

        # 保存JSON文件，使用UTF-8编码
        with open(json_file, "w", encoding="utf-8") as f:
            f.write(json_data)

        print(f"已成功将Excel文件转换为JSON: {json_file}")
        return json_file

    except Exception as e:
        print(f"处理Excel文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    # 获取命令行参数
    if len(sys.argv) > 1:
        excel_file = sys.argv[1]
    else:
        # 默认使用当前目录下的Excel文件
        excel_file = "AI验证问题清单.xlsx"

    # 转换Excel文件
    convert_excel_to_json(excel_file)

if __name__ == "__main__":
    main()
