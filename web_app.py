"""
平安理财ChatBI问答系统网页界面
使用Flask框架实现
"""

import json
import os
import time
import logging
from datetime import datetime
from flask import Flask, request, jsonify, render_template, send_from_directory
from chatbi_core import process_question

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chatbi_web.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ChatBI-Web")

# 创建Flask应用
app = Flask(__name__)

# 加载历史记录
def load_history():
    """
    加载历史记录
    
    返回:
        list: 历史记录
    """
    try:
        if os.path.exists("history.json"):
            with open("history.json", "r", encoding="utf-8") as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"加载历史记录失败: {str(e)}")
    return []

# 保存历史记录
def save_history(history):
    """
    保存历史记录
    
    参数:
        history (list): 历史记录
    """
    try:
        with open("history.json", "w", encoding="utf-8") as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"保存历史记录失败: {str(e)}")

# 主页路由
@app.route('/')
def index():
    """
    主页路由
    """
    return render_template('index.html')

# 静态文件路由
@app.route('/static/<path:path>')
def send_static(path):
    """
    静态文件路由
    """
    return send_from_directory('static', path)

# API路由：处理问题
@app.route('/api/process_question', methods=['POST'])
def api_process_question():
    """
    API路由：处理问题
    """
    try:
        # 获取请求数据
        data = request.get_json()
        user_question = data.get('question', '')
        
        if not user_question:
            return jsonify({
                'success': False,
                'error': '问题不能为空'
            })
        
        # 处理问题
        logger.info(f"收到用户问题: {user_question}")
        result = process_question(user_question)
        
        if result["success"]:
            # 添加到历史记录
            history = load_history()
            history.append(result)
            save_history(history)
            
            # 返回结果
            return jsonify({
                'success': True,
                'result': result
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '处理问题失败'),
                'result': result
            })
    
    except Exception as e:
        logger.error(f"处理请求时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API路由：获取历史记录
@app.route('/api/history', methods=['GET'])
def api_history():
    """
    API路由：获取历史记录
    """
    try:
        history = load_history()
        return jsonify({
            'success': True,
            'history': history
        })
    except Exception as e:
        logger.error(f"获取历史记录时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API路由：清空历史记录
@app.route('/api/clear_history', methods=['POST'])
def api_clear_history():
    """
    API路由：清空历史记录
    """
    try:
        save_history([])
        return jsonify({
            'success': True
        })
    except Exception as e:
        logger.error(f"清空历史记录时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API路由：获取模板列表
@app.route('/api/templates', methods=['GET'])
def api_templates():
    """
    API路由：获取模板列表
    """
    try:
        with open("templates.json", "r", encoding="utf-8") as f:
            templates = json.load(f)
        return jsonify({
            'success': True,
            'templates': templates
        })
    except Exception as e:
        logger.error(f"获取模板列表时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 启动应用
if __name__ == '__main__':
    # 确保templates和static目录存在
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    # 启动应用
    app.run(host='0.0.0.0', port=5000, debug=True)
