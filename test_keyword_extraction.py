"""
测试千问API提取关键词功能
"""

import json
import dashscope
from dashscope import Generation

# 设置API密钥
API_KEY = 'sk-4af3f43ae9a74f0ebed6736b453a47c6'
dashscope.api_key = API_KEY

def load_prompt_template():
    """
    加载提示词模板
    """
    try:
        with open("prompts/extraction_prompt.txt", "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        print(f"加载提示词模板失败: {str(e)}")
        return None

def extract_keywords(user_question, prompt_template):
    """
    使用千问API提取关键词
    
    参数:
        user_question (str): 用户问题
        prompt_template (str): 提示词模板
        
    返回:
        dict: 提取的关键词
    """
    print(f"从用户问题中提取关键词: {user_question}")
    
    # 填充提示词模板
    prompt = prompt_template.replace("{user_question}", user_question)
    
    try:
        # 调用千问API
        response = Generation.call(
            model="qwen-max",
            prompt=prompt,
            temperature=0.3,
            max_tokens=500,
            result_format='message'
        )
        
        # 检查响应
        if response.status_code == 200:
            # 解析模型返回的JSON
            content = response.output.choices[0].message.content
            print("模型回复:")
            print("-" * 50)
            print(content)
            print("-" * 50)
            
            # 提取JSON部分
            json_start = content.find('{')
            json_end = content.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                result = json.loads(json_str)
                print(f"提取的关键词: {json.dumps(result, ensure_ascii=False, indent=2)}")
                return result
            else:
                print("无法从模型响应中提取JSON")
                return {}
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            print(f"错误信息: {response.code}: {response.message}")
            return {}
    
    except Exception as e:
        print(f"API调用出错: {str(e)}")
        return {}

def test_extraction():
    """
    测试关键词提取功能
    """
    # 加载提示词模板
    prompt_template = load_prompt_template()
    if not prompt_template:
        print("无法加载提示词模板，测试终止")
        return
    
    # 测试用例
    test_cases = [
        "昨天天天成长3号在小招的赎回金额？",
        "上个月启航添利在蚂蚁渠道的规模？",
        "本季度稳利多6号的净申赎金额是多少？",
        "去年新启航系列产品在工行的总客户数？"
    ]
    
    # 运行测试
    for i, test_case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {test_case}")
        extract_keywords(test_case, prompt_template)

if __name__ == "__main__":
    test_extraction()
